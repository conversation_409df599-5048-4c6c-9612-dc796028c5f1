.classpath
.DS_Store
.sfdx
.project
.salesforce
node_modules
.idea
.sf
.tern-project
.settings
.sf
selenium-client-jars/
test/artifacts
assets/server.key
assets/env-var-values.txt
assets/server.key.hex
force-app/main/default/lwc/fgxEntInputDemoTab/fgxEntInputDemoTab.js
force-app/main/default/lwc/fgxEntInputDemoTab/fgxEntInputDemoTab.html
manifest/package.xml

# Illuminated Cloud (IntelliJ IDEA)
IlluminatedCloud
out
.idea
*.iml
/force-app/main/default/lwc/fgxEntInputDemoTab/

# Added by Illuminated Cloud
.localdev/
target/
/.illuminatedCloud/
**/tsconfig*.json
**/*.tsbuildinfo
scripts/query.soql
