#!/bin/bash
#convert the dx metadata to traditional 
#metadata format in a ./deploy dir

# bring in config file variables
source config/dx-utils.cfg

if [ $# -lt 1 ]
then
    DEPLOYDIR='deploy'
    ROOTDIR='force-app/main/'
    PACKAGENAME=$DEFAULT_PACKAGE_NAME
    DELTAONLY=''
    DIFFFROM=''
else
    DEPLOYDIR=$1
    ROOTDIR=$2
    PACKAGENAME=$3
    DELTAONLY=$4
    DIFFFROM=$5
fi

if [ -z "$DELTAONLY"  ]
then
    sfdx project convert source -r $ROOTDIR -d $DEPLOYDIR
else
    echo 'delta deploy dir'
    DEPLOYDIR='changed-sources/'
    ROOTDIR='force-app/'

    if [ -z "$DIFFFROM"  ]
    then
        echo 'default from HEAD^'
        DIFFFROM='HEAD^'
    else
        echo 'dif from ' "$DIFFFROM"
    fi

    #triggering build
    sf sgd source delta --to "HEAD" --from $DIFFFROM --output $DEPLOYDIR --generate-delta --source $ROOTDIR

    # If destructiveChanges.xml exists, move it to destructiveChangesPost.xml
    if [ -f "${DEPLOYDIR}destructiveChanges/destructiveChanges.xml" ]; then
        echo "Moving destructiveChanges.xml to destructiveChangesPost.xml"
        mv "${DEPLOYDIR}destructiveChanges/destructiveChanges.xml" "${DEPLOYDIR}destructiveChanges/destructiveChangesPost.xml"
    fi

    # Handle Flow Deletions if present in destructiveChangesPost.xml
    if [ -f "${DEPLOYDIR}destructiveChanges/destructiveChangesPost.xml" ]; then
        echo "Found destructiveChangesPost.xml at ${DEPLOYDIR}destructiveChanges/destructiveChangesPost.xml"

        if grep -q '<name>Flow</name>' "${DEPLOYDIR}destructiveChanges/destructiveChangesPost.xml"; then
            echo "Flow deletions detected - preparing flow definitions"

            # Create directory for flow deletion metadata with correct structure
            mkdir -p flow-deletion/force-app/main/default/flowDefinitions
            echo "Created flow-deletion/force-app/main/default/flowDefinitions directory"

            # Extract Flow members using grep and sed
            FLOWS=$(grep -B 1 '<name>Flow</name>' "${DEPLOYDIR}destructiveChanges/destructiveChangesPost.xml" | \
                   grep '<members>' | \
                   sed 's/.*<members>\(.*\)<\/members>.*/\1/' | \
                   sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

            echo "Debug - Extracted flows: '$FLOWS'"

            # For each flow, create FlowDefinition file only if it exists in the org
            if [ ! -z "$FLOWS" ]; then
                echo "$FLOWS" | while IFS= read -r FLOW; do
                    # Skip if FLOW is empty or just whitespace
                    if [ -z "${FLOW// }" ]; then
                        continue
                    fi

                    echo "Processing flow: $FLOW"

                    # Check if Flow Definition exists in the org
                    FLOW_EXISTS=$(sf data query -q "SELECT Id FROM FlowDefinition WHERE DeveloperName='$FLOW' LIMIT 1" --json --use-tooling-api | jq -r '.result.totalSize')

                    if [ "$FLOW_EXISTS" -gt 0 ]; then
                        echo "Flow Definition exists in org for: $FLOW - creating FlowDefinition file"
                        # Create FlowDefinition with activeVersionNumber set to 0
                        cat > "flow-deletion/force-app/main/default/flowDefinitions/$FLOW.flowDefinition-meta.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<FlowDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <activeVersionNumber>0</activeVersionNumber>
</FlowDefinition>
EOF
                        echo "Created flow definition file for $FLOW"
                    else
                        echo "Flow Definition does not exist in org for: $FLOW - skipping"
                        continue
                    fi
                done

                # Check if any flow definition files were created
                if [ "$(find flow-deletion/force-app/main/default/flowDefinitions -name '*.flowDefinition-meta.xml' | wc -l)" -gt 0 ]; then
                    # Add FlowDefinition type to the existing package.xml
                    PACKAGE_XML="${DEPLOYDIR}package/package.xml"
                    if [ -f "$PACKAGE_XML" ]; then
                        # Check if FlowDefinition type already exists
                        if ! grep -q '<name>FlowDefinition</name>' "$PACKAGE_XML"; then
                            # Create temporary file
                            TEMP_FILE=$(mktemp)

                            # Add FlowDefinition type before the version tag
                            awk '/<version>/ {
                                print "    <types>"
                                print "        <members>*</members>"
                                print "        <name>FlowDefinition</name>"
                                print "    </types>"
                                print $0
                                next
                            }
                            {print}' "$PACKAGE_XML" > "$TEMP_FILE"

                            # Replace original file with modified content
                            mv "$TEMP_FILE" "$PACKAGE_XML"
                            echo "Added FlowDefinition type to package.xml"
                        else
                            echo "FlowDefinition type already exists in package.xml"
                        fi
                    else
                        echo "Warning: package.xml not found at $PACKAGE_XML"
                    fi

                    # Copy flow-deletion contents to the correct directory structure
                    echo "Copying flow-deletion contents to ${DEPLOYDIR}force-app"
                    cp -r flow-deletion/force-app/* "${DEPLOYDIR}force-app/"
                fi

                # Clean up temporary directory
                rm -rf flow-deletion
            else
                echo "No flow names were extracted from destructiveChangesPost.xml"
                rm -rf flow-deletion
            fi
        else
            echo "No Flow type section found in destructiveChangesPost.xml"
        fi
    else
        echo "No destructiveChangesPost.xml found at ${DEPLOYDIR}destructiveChanges/destructiveChangesPost.xml"
    fi
fi