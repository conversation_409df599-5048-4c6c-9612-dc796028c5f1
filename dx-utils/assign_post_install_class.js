#!/usr/local/bin/node

/* eslint-env es6 */

//##########################################################################
/* This script is used to assign a post install class to First-Generation Managed Packages
To use, update the packageName and postInstallClassName
https://help.salesforce.com/articleView?id=apex_post_install_script_specify.htm&type=5
*/
//##########################################################################
const packageName = 'put_package_name_here'
const postInstallClassName = 'put_post_install_class_here';

var fs = require("fs");

var xmlData = fs.readFileSync('deploy/package.xml', 'utf8');
xmlData = xmlData.replace('</Package>', '  <postInstallClass>' + postInstallClassName + '</postInstallClass>');
xmlData += '\n</Package>';

fs.writeFile("deploy/package.xml", xmlData, function(err, data) {
    if (err){
        console.log(err);
        process.exit(1);
    } else{
        console.log("Updated XML file to include postInstallClass " + postInstallClassName);
    }
});
