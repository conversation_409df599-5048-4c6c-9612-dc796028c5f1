#!/bin/bash
# use this command to run destructive changes
# from current checked-out branch 

###################### THIS IS FOR CIRCLE CI TO FAIL IF THERE IS FAILURE DO NOT REMOVE ######################
# Exit script if a statement returns a non-true return value.
set -o errexit

# Use the error status of the first failure, rather than that of the last item in a pipeline.
set -o pipefail
##############################################################################################################

if [ $# -lt 1 ]
then
    echo 'Usage: destructive.sh alias'
    exit
fi

ALIAS=$1
CHECKONLY=$2
DELTAONLY=$3

if [ -z "$CHECKONLY" ]
then
    echo 'real destroy'
else
    echo 'checkonly destroy'
    CHECKONLY='--dry-run'
fi

if [ -z "$DELTAONLY"  ]
then
    echo 'default destroy dir'
    DEPLOYDIR='destructive'
    MANIFESTDIR='destructive/package.xml'
    PREDESTRUCTIVEDIR='destructive/destructiveChangesPre.xml'
    POSTDESTRUCTIVEDIR='destructive/destructiveChangesPost.xml'
else
    echo 'delta destroy dir'
    DEPLOYDIR='changed-sources/destructiveChanges'
    MANIFESTDIR='changed-sources/destructiveChanges/package.xml'
    DESTRUCTIVEDIR='changed-sources/destructiveChanges/destructiveChangesPre.xml'
    POSTDESTRUCTIVEDIR='changed-sources/destructiveChanges/destructiveChangesPost.xml'
fi

# Ensure package.xml exists with minimum required content
if [ ! -f "$MANIFESTDIR" ] || ! grep -q '<version>' "$MANIFESTDIR"; then
    echo "Creating/Updating package.xml"
    cat > "$MANIFESTDIR" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<Package xmlns="http://soap.sforce.com/2006/04/metadata">
    <version>58.0</version>
</Package>
EOF
fi

# Run pre-destructive changes if destructiveChangesPre.xml exists and has content
if [ -f "$PREDESTRUCTIVEDIR" ] && grep -q '<types>' "$PREDESTRUCTIVEDIR" ; then
    echo "---- deploying pre-destructive changes ----"
    sf project deploy start $CHECKONLY --target-org $ALIAS --pre-destructive-changes $PREDESTRUCTIVEDIR --manifest $MANIFESTDIR --ignore-warnings
fi

# Run post-destructive changes if destructiveChangesPost.xml exists and has content
if [ -f "$POSTDESTRUCTIVEDIR" ] && grep -q '<types>' "$POSTDESTRUCTIVEDIR" ; then
    echo "---- deploying post-destructive changes ----"
    sf project deploy start $CHECKONLY --target-org $ALIAS --post-destructive-changes $POSTDESTRUCTIVEDIR --manifest $MANIFESTDIR --ignore-warnings
fi