<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Tracking_Link__c</fullName>
    <externalId>false</externalId>
    <formula>IF(AND(ISPICKVAL($Profile.UserType, &quot;CspLitePortal&quot;), RecordType.Name = &quot;IT Shipment&quot;),
HYPERLINK(&quot;https://fgx.my.site.com/service/TrackingView?search=&quot;&amp;CaseNumber, &quot;Track&quot;,&quot;_blank&quot; ),
HYPERLINK(&quot;/apex/TrackingView?search=&quot;&amp;CaseNumber,CaseNumber,&quot;_self&quot; ))</formula>
    <label>Tracking Link</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
