<?xml version="1.0" encoding="UTF-8"?>
<RecordType xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Inquiry</fullName>
    <active>true</active>
    <businessProcess>Inquiry Process</businessProcess>
    <compactLayoutAssignment>Mobile_Compact_Layout</compactLayoutAssignment>
    <description>Use this to create a case for a general questions</description>
    <label>Inquiry</label>
    <picklistValues>
        <picklist>Billing_Closure_Reason__c</picklist>
        <values>
            <fullName>Billed with another Shipment</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>No Charge for Shipment</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Other</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Billing_Invoice_Status__c</picklist>
        <values>
            <fullName>Closed Un-invoiced</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Fully Billed</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Not Billed</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Partially Billed</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Current_Packaging__c</picklist>
        <values>
            <fullName>Bare Metal</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Boxed %26 Palletized</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Flight Cases</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>I Don%27t Know</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>OEM Boxes</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Pre-Installed in Cabinet</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>DIMWeightEntryType__c</picklist>
        <values>
            <fullName>Confirmed by Agent</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Estimate For Client</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Finalized by Warehouse</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Provided By Client</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Destination_Location_Type__c</picklist>
        <values>
            <fullName>Data Center</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Office</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Other</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Warehouse</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Destination_Search_Choice__c</picklist>
        <values>
            <fullName>Google</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>SFDC</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Site</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Destination_US_State__c</picklist>
        <values>
            <fullName>Alabama</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Alaska</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Arizona</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Arkansas</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>California</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Colorado</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Connecticut</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Delaware</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>District of Columbia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Florida</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Georgia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Hawaii</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Idaho</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Illinois</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Indiana</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Iowa</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Kansas</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Kentucky</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Louisiana</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Maine</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Maryland</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Massachusetts</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Michigan</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Minnesota</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Mississippi</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Missouri</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Montana</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Nebraska</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Nevada</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New Hampshire</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New Jersey</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New Mexico</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New York</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>North Carolina</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>North Dakota</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Ohio</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Oklahoma</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Oregon</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Pennsylvania</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Rhode Island</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>South Carolina</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>South Dakota</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Tennessee</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Texas</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Utah</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Vermont</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Virginia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Washington</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>West Virginia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Wisconsin</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Wyoming</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Equipment_Condition__c</picklist>
        <values>
            <fullName>Brand New</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>I Don%27t Know</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Mix of New %26 Used</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>OEM Refurbished</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Used</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Flow_Status__c</picklist>
        <values>
            <fullName>Approved</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Exception</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>In Draft</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Pending Review</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>IATA_Battery_Type__c</picklist>
        <values>
            <fullName>Class 9 DG</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>No Batteries</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>SP A67 %28Lead-Acid%29</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>UN3091 %28ELM%29</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>UN3481 %26 UN3091 %28ELI%2FELM%29</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>UN3481 %28ELI%29</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Incoterms__c</picklist>
        <values>
            <fullName>CIF</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>DAP</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>DDP</fullName>
            <default>true</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Last_Action__c</picklist>
        <values>
            <fullName>Customer Comment</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer E-mail</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer Field Update</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>FGX Field Update</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>FGX Sent Comment</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>FGX Sent E-mail</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Manifest_Status__c</picklist>
        <values>
            <fullName>Completed</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>In Progress</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Pending Review</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Ready to Manifest</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Origin</picklist>
        <values>
            <fullName>Email</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Phone</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Portal</fullName>
            <default>true</default>
        </values>
        <values>
            <fullName>Twitter</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Web</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Origin_Location_Type__c</picklist>
        <values>
            <fullName>Data Center</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Office</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Other</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Warehouse</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Origin_Search_Choice__c</picklist>
        <values>
            <fullName>Google</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>SFDC</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Site</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Origin_US_State__c</picklist>
        <values>
            <fullName>Alabama</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Alaska</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Arizona</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Arkansas</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>California</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Colorado</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Connecticut</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Delaware</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>District of Columbia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Florida</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Georgia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Hawaii</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Idaho</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Illinois</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Indiana</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Iowa</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Kansas</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Kentucky</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Louisiana</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Maine</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Maryland</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Massachusetts</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Michigan</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Minnesota</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Mississippi</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Missouri</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Montana</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Nebraska</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Nevada</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New Hampshire</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New Jersey</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New Mexico</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New York</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>North Carolina</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>North Dakota</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Ohio</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Oklahoma</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Oregon</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Pennsylvania</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Rhode Island</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>South Carolina</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>South Dakota</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Tennessee</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Texas</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Utah</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Vermont</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Virginia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Washington</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>West Virginia</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Wisconsin</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Wyoming</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Packing_Type__c</picklist>
        <values>
            <fullName>Box</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Crate</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Pallet</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Piece</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Priority</picklist>
        <values>
            <fullName>High</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Low</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Medium</fullName>
            <default>true</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Reason</picklist>
        <values>
            <fullName>Case Created in Error</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Case Still Open</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Service_Level__c</picklist>
        <values>
            <fullName>Door to Port</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>EconomyIT</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>FlexIOR</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Managed Courier</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>PriorityIT</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Supervisor_Category__c</picklist>
        <values>
            <fullName>Client Discussion</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Export Controlled</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>High Value</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Local Sale</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Multi-Destination</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Time Critical</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Transaction</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Supervisor_Review_Status__c</picklist>
        <values>
            <fullName>Completed</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>In Process</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Not Started</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Supervisor_Review_Type__c</picklist>
        <values>
            <fullName>Not Reviewed</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Reviewed</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Supervisor_Tier__c</picklist>
        <values>
            <fullName>Tier I</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Tier II</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Tier III</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Transaction_Type__c</picklist>
        <values>
            <fullName>Customer Doesn%27t Know</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Hardware Already Purchased</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Hardware Not Yet Purchased</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Hardware Will Be Leased%2FFinanced</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>TxnSurvey_Asset_Transfer__c</picklist>
        <values>
            <fullName>Customer Doesn%27t Know</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer Doesn%27t Want to Transfer Assets</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer Wants to Transfer Assets</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>TxnSurvey_Funds_Transfer__c</picklist>
        <values>
            <fullName>Customer Doesn%27t Know</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer Doesn%27t Want to Transfer Funds</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer Wants to Transfer Funds</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>TxnSurvey_Related_Entity__c</picklist>
        <values>
            <fullName>Customer Doesn%27t Have Related Entity</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer Doesn%27t Know</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Customer Has Related Entity</fullName>
            <default>false</default>
        </values>
    </picklistValues>
    <picklistValues>
        <picklist>Type</picklist>
        <values>
            <fullName>General Inquiry</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Insurance Question</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>New Account Setup</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Packing Question</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Status Update</fullName>
            <default>false</default>
        </values>
        <values>
            <fullName>Transit Time Question</fullName>
            <default>false</default>
        </values>
    </picklistValues>
</RecordType>
