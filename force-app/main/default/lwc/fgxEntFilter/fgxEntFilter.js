/**
 * Created by reis<PERSON><PERSON> on 6/25/23.
 */

import {api, LightningElement} from 'lwc';
import resources from '@salesforce/resourceUrl/FGXEnterpriseResources';

export default class FgxEntFilter extends LightningElement {

    _value = '';

    @api placeholder;

    @api
    get value() {
        return this._value;
    }

    set value(value) {
        this._value = value;
    }

    get searchIcon() {
        return resources + "/images/search.svg";
    }

    handleChange(event) {
        this._value = event.target.value;
        this.dispatchEvent(
            new CustomEvent(
                'change',
                {
                    detail: this._value
                }
            )
        );
    }
}