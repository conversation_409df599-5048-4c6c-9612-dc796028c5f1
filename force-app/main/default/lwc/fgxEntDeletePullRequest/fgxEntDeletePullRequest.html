<!--
 * fgxEntDeletePullRequest
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 9/19/24
 -->

<template>
    <template if:true={record}>
        <h3 class="h3 mbr">Delete Pull Request</h3>
        <p class="mbm p secondary-neutral-color">
            <span>{baseVerbiage}</span>
            <template lwc:if={showQuotedVerbiage}>
                <span>{addtQuotedVerbiage}</span>
            </template>
        </p>
        <div class="flex flex-gap-sp mbs space-between">
            <c-fgx-ent-button
                    type="secondary"
                    label="Nevermind"
                    onclick={handleClose}>
            </c-fgx-ent-button>
            <c-fgx-ent-button
                    type="danger"
                    label="Delete"
                    loading={loading}
                    onclick={handleArchive}>
            </c-fgx-ent-button>
        </div>
    </template>
</template>