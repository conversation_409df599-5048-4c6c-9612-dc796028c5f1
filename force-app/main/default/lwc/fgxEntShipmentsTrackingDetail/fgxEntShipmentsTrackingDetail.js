import { LightningElement, api } from 'lwc';
import resources from '@salesforce/resourceUrl/FGXEnterpriseResources';

export default class FgxEntShipmentsTrackingDetail extends LightningElement {
    @api trackingItem;
    @api shipment;
    @api currentItem;
    @api totalItems;

    get containerClasses(){
        // add padding to final tracking item
        if (this.currentItem === this.totalItems - 1)
            return "flex flex-gap-rp mlr pbm";
        return "flex flex-gap-rp mlr";
    }
    
    get returnArrowIcon(){
        return resources + '/images/returnarrow.svg';
    }

    get timestamp(){
        if (this.trackingItem.DateTime__c){
            const dateTime = new Date(this.trackingItem.DateTime__c);

            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            const options = { 
                year: 'numeric', 
                month: 'numeric', 
                day: 'numeric', 
                hour: 'numeric', 
                minute: 'numeric',
                hour12: false,
                timeZone: userTimezone
            };

            const dateFormatter = new Intl.DateTimeFormat('en-US', options);
            const formattedDate = dateFormatter.format(dateTime);

            const [date, time] = formattedDate.split(',');

            return `${date.trim()} ${time.trim()}`;
        }
        else
            return '';
    }

    get item(){
        if (this.trackingItem.Description__c)
            return `${this.trackingItem.Status__c}: ${this.trackingItem.Description__c}`
        else {
            let description;
            switch (this.trackingItem.Status__c){
                case 'Booked': {
                    description = `Shipment confirmed with tracking #: ${this.shipment.caseNumber}`;
                    break;
                }
                case 'Enroute to FGX': {
                    description = 'Moving to the nearest FGX facility for processing';
                    break;
                }
                case 'Scheduling Pickup': {
                    if (this.shipment.originCity && this.shipment.originCountryName)
                        description = `Our team is coordinating collection from ${this.shipment.originCity}, ${this.shipment.originCountryName}`;
                    else
                        description = 'Our team is coordinating collection';
                    break;
                }
                case 'Received @ FGX': {
                    description = 'Shipment arrived at FGX facility for further processing';
                    break;
                }
                case 'Crating/Packing': {
                    description = 'Packing is underway to ensure safe transport';
                    break;
                }
                case 'Pending Manifest': {
                    description = 'Bill of materials verification is underway';
                    break;
                }
                case 'Pending Doc Approval': {
                    description = 'Shipping documents are under review by local customs specialist';
                    break;
                }
                case 'Pending Permit': {
                    description = 'Required permits are in process and/or under agency review';
                    break;
                }
                case 'Pending Payment': {
                    description = 'Advance payment is required for the shipment to proceed';
                    break;
                }
                case 'In Transit': {
                    description = 'Shipment is moving towards the destination';
                    break;
                }
                case 'Clearance Processing': {
                    description = 'Customs processing is underway';
                    break;
                }
                case 'Permit Processing': {
                    description = 'Required permits are in process and/or under agency review';
                    break;
                }
                case 'Out for Delivery': {
                    description = 'Arrangements for final delivery are underway';
                    break;
                }
                case 'Delivered': {
                    if (this.shipment.podName && this.shipment.podDate)
                        description = `Received by: ${this.shipment.podName} on ${this.shipment.podDate}`;
                    else
                        description = 'POD details pending';
                    break;
                }
                case 'On Hold': {
                    description = 'Contact FGX for more information';
                    break;
                }
                case 'Pending Client Action': {
                    description = 'Please check action items or contact FGX';
                    break;
                }
                case 'Quote Request': {
                    description = 'Quote is being prepared by our team';
                    break;
                }
                case 'Quoted': {
                    description = 'Shipment can proceed once quote is accepted';
                    break;
                }
                default: {
                    description = '';
                    break;
                }
            }

            return `${this.trackingItem.Status__c}: ${description}`;
        }
    }
}