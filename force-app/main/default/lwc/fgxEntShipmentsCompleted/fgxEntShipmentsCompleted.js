import { LightningElement} from 'lwc';
import getShipments from '@salesforce/apex/FGXEnt_ShipmentController.getShipments';
import resources from '@salesforce/resourceUrl/FGXEnterpriseResources';
import {ShowToastEvent} from 'c/showToastEvent';
import {getPages, getConstants} from "c/fgxEntConstants";
const PAGES = getPages();
const CONSTANTS = getConstants();

export default class FgxEntShipmentsCompleted extends LightningElement {
    shipments = [];
    debounceSearchTimer;
    loading;
    allRecordsLoaded;
    queryParams;
    initialLoad = false;

    noResultsProps = { 
        icon: this.shipmentsIcon,
        header: "Completed Shipments",
        subheader: "You don't have any completed shipments yet. Book a shipment to get started.",
        linkTarget: PAGES.QUOTEBOOKING,
        linkLabel: "Book a Shipment",
        linkType: "action",
    };

    get initialLoading(){
        return !this.initialLoad;
    }

    get showNoResults() {
        return !this.showResults & !this.loading;
    }

    get showResults() {
        return this.initialLoad && this.shipments.length > 0;
    }

    get shipmentsIcon() {
        return resources + '/images/shipments.svg';
    }

    connectedCallback() {
        this.queryParams = {
            limitSize: 25,
            offset: 0,
            statuses: [
                CONSTANTS.CASE_STATUS_DELIVERED,
                CONSTANTS.CASE_STATUS_CLOSED
            ],
            searchValue: null,
            searchFields: [
                'CaseNumber',
                'Client_Ref__c',
                'Destination_Country__r.Name',
                'Destination_Country__r.Code__c',
                'Origin_Country__r.Name',
                'Origin_Country__r.Code__c',
                'Status'
            ],
            sortedBy: 'Action_Item_Count__c',
            sortDirection: 'DESC'
        };

        this.handleLoadShipmentData();
    }

    /**
     * @description: handles the change event
     * coming from the filter component
     * @param event
     */
    handleFilterChange(event) {
        window.clearTimeout(this.debounceSearchTimer);

        this.queryParams.searchValue = event.detail;
        this.queryParams.offset = 0;
        this.allRecordsLoaded = false;

        if(!event.detail) {
            this.handleLoadShipmentData(true);
        } else {
            this.debounceSearchTimer = setTimeout(() => {
                this.handleLoadShipmentData(true);
            }, 800)
        }
    }

    /**
     * @description helper function to make imperative calls
     * to get more quotes during lazy loading and handle stopping
     * callouts when it's reached the end of available records
     * @param clearRecords
     */
    handleLoadShipmentData(clearRecords) {
        if (this.loading || this.allRecordsLoaded) {
            return;
        }
        this.loading = true;

        getShipments({
            requestString: JSON.stringify(this.queryParams),
            shipmentTable: true
        })
            .then((data) => {
                // console.log('Data in completed shipments: ', data);
                if(clearRecords) {
                    this.shipments = [];
                }
                if (data && data.length > 0) {
                    this.shipments = [...this.shipments, ...data];

                    if (data.length < this.queryParams.limitSize)
                        this.allRecordsLoaded = true;
                } else {
                    this.allRecordsLoaded = true;
                }
                this.initialLoad = true;
            })
            .catch((error) => {
                this.allRecordsLoaded = true;
                this.initialLoad = true;
                this.dispatchEvent(
                    new ShowToastEvent({
                        label: 'Error',
                        message: 'Problem retrieving shipment data',
                        variant: 'error',
                    })
                );
            })
            .finally(() => {
                this.loading = false;
            });
    }

    /**
     * @description handler for the scroll event which tells us
     * if we have reached the bottom of the scrollable list and
     * to then invoke getting more records if possible.
     * @param event
     */
    handleScroll(event) {
        const target = event.target;
        if (target.scrollHeight - target.scrollTop <= target.clientHeight + 100) {
            this.handleLoadMore();
        }
    }

    /**
     * @description helper function to determine if
     * it's okay to lazy load more records and then to increment the
     * offset if so
     */
    handleLoadMore() {
        if( !this.loading && !this.allRecordsLoaded) {
            this.queryParams.offset =  this.queryParams.offset + this.queryParams.limitSize;
            this.handleLoadShipmentData();
        }
    }

    /**
     * @description handler for the sort event which tells us
     * which column to sort by and in which direction.
     * we will fetch new data from shipments table based on this info.
     * @param event
     */
    handleSort(event){
        event.stopPropagation();
        this.queryParams.offset = 0;
        this.queryParams.sortedBy = event.detail.sortedBy;
        this.queryParams.sortDirection = event.detail.sortDirection;
        this.handleLoadShipmentData(true);
    }
}