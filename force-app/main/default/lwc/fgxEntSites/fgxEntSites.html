<template>
    <div class="full-width overflow-hidden mtm mbl">
        <c-fgx-ent-main-display-header
                main-header={pageMainHeader}
                sub-header={pageSubHeader}
                button-name="Add a Site"
                button-type="action"
                type="popup">
        <span slot="content">
            <c-fgx-ent-stepper
                    onsubmit={handleStepperSubmit}
                    onfinished={handleStepperFinish}
                    oncontinue={handleContinue}>
                <c-fgx-ent-site-compliance
                        object-api-name="Site__c"
                        ignore-flow-status="true"
                        step-field-name="Site_Compliance_Step__c">
                </c-fgx-ent-site-compliance>
            </c-fgx-ent-stepper>
        </span>
        </c-fgx-ent-main-display-header>
        <template lwc:if={showSiteSurvey}>
            <c-fgx-ent-section-popup type="popup" reveal="true">
                <c-fgx-ent-stepper onclose={handleSurveyClose}>
                    <c-fgx-ent-site-survey
                            record-id={siteId}
                            object-api-name="Site__c"
                            ignore-flow-status="true"
                            allow-save-on-first-step="true"
                            step-field-name="Site_Survey_Step__c">
                    </c-fgx-ent-site-survey>
                </c-fgx-ent-stepper>
            </c-fgx-ent-section-popup>
        </template>
        <c-fgx-ent-sites-dashboard></c-fgx-ent-sites-dashboard>
    </div>
</template>