import { LightningElement, api } from 'lwc';
import { getConstants } from 'c/fgxEntConstants';
import { NavigationMixin } from "lightning/navigation";
import disabledPill from "@salesforce/resourceUrl/disabledPill";
import { loadStyle } from "lightning/platformResourceLoader";

const CONSTANTS = getConstants();

export default class BomManifestProgressModalLineDetails extends NavigationMixin(LightningElement) {
    @api bomLine;
    @api modalDepth = 0;
    headers = ['Serial Number', 'Piece ID', 'COO', 'Quantity', 'Manifest Exceptions', 'Photos'];

    connectedCallback(){
        loadStyle(this, disabledPill);
    }

    get intangible(){
        return this.bomLine?.Category__c === 'Software/License' || this.bomLine?.Category__c == 'Warranty/Support';
    }

    get notStarted(){
        return this.bomLine?.manifestDisplayStatus === null;
    }

    get fullyManifested(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_FULLY_MANIFESTED;
    }

    get fullyManifestedExceptions(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_FULLY_MANIFESTED_EXCEPTIONS;
    }

    get partiallyReceived(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_PARTIALLY_RECEIVED;
    }

    get missingQuantity(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_MISSING_QUANTITY;
    }

    get lowQuantity(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_LOW_QUANTITY;
    }

    get surplus(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_SURPLUS;
    }

    get absent(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_ABSENT;
    }

    get configItem(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_CONFIG_ITEM;
    }

    get unexpected(){
        return this.bomLine?.manifestDisplayStatus === CONSTANTS.MANIFEST_STATUS_UNEXPECTED;
    }

    get exceptionMapArray() {
        return this.bomLine?.exceptionMap ? Object.entries(this.bomLine?.exceptionMap).map(([key, value]) => ({key, value})) : [];
    }

    get cooMismatchSize(){
        return this.bomLine?.cooMismatch?.length;
    }

    get tableStyles(){
        return `z-index: ${9000 + parseInt(this.modalDepth)};`
    }

    get bomLineUnitsFormatted(){
        return (this.bomLine?.lineRelatedUnits || []).map(unit => {
            return {
                ...unit,
                coo: unit?.COO_Unknown__c ? 'Unknown' : unit?.Country_of_Origin__c
            }
        })
    }

    handlePhotoPreview(event){
        const fileId = event.currentTarget.dataset.id;
        this[NavigationMixin.Navigate]({
            type: "standard__namedPage",
            attributes: {
                pageName: "filePreview",
            },
            state: {
                selectedRecordId: fileId,
            },
        });
    }
}