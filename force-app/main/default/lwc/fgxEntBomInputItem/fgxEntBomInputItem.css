/**
 * fgxEntBOMInputItem
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 12/6/23
 */
@import 'c/fgxEntGlobalStyles';

.topContainer {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1.75fr 1fr;
    grid-column-gap: var(--sp);
    align-items: flex-start;
}

.bom-item {
    transition: all 0.5s ease-out;
    opacity: 0;
}

.bom-item.show {
    opacity: 1;
}

.bullet-item {
    list-style-type: circle;
}