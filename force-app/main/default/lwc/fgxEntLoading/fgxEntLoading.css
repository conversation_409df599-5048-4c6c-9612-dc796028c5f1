/**
 * fgxEntLoading
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 2/11/24
 */
@import 'c/fgxEntGlobalStyles';


.hidden {
    right: -50vw;
}

.content-positioning {
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.overlay {
    z-index: 9997;
    width: 100%;
    height: 100%;
}

.reveal {
    backdrop-filter: blur(2px);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.65);
    cursor: default;
    z-index: 9998;
}