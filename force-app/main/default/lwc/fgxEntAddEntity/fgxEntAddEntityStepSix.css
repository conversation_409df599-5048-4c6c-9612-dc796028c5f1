/**
 * fgxEntAddEntityStepFive.css
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 10/12/23
 */

@import 'c/fgxEntGlobalStyles';

.optionLayout {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    padding: var(--lp);
    cursor: pointer;
    grid-auto-flow: row;
    align-content: center;
    justify-content: center;
    align-items: center;
    justify-items: center;
    grid-template-rows: 50px auto;
}

.optionLayout p {
    color: var(--secondary-neutral-color); /* Set initial color to secondary-neutral-color */
}

.optionLayout.selected,
.optionLayout:hover {
    border-color: var(--black);
}

.optionLayout.selected {
    background-color: var(--secondary-button-hover-color);
}

.optionLayout:hover p,
.optionLayout.selected p {
    color: var(--black); /* Change the color to black on hover or click */
}

.min-medium-width {
    min-width: 750px; 
}