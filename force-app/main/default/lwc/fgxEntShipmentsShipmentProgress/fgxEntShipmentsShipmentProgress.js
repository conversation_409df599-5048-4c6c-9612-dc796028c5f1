import { LightningElement, api, wire } from 'lwc';
import resources from '@salesforce/resourceUrl/FGXEnterpriseResources';
import getTrackingItems from '@salesforce/apex/FGXEnt_ShipmentController.getTrackingItems';
import getTimelineData from '@salesforce/apex/FGXEnt_ShipmentController.getTimelineData';


export default class FgxEntShipmentsShipmentProgress extends LightningElement {
    @api shipment;
    @api actionItems;
    
    accordionStatusMap = new Map([
        ['Quote Request', 'Quoting'],
        ['Quoted', 'Quoting'],
        ['Booked', 'Collection'],
        ['Enroute to FGX', 'Collection'],
        ['Scheduling Pickup', 'Collection'],
        ['Received @ FGX', 'Manifest'],
        ['Crating/Packing', 'Manifest'],
        ['Pending Manifest', 'Manifest'],
        ['Pending Doc Approval', 'Compliance and Document Preparation'],
        ['Pending Permit', 'Compliance and Document Preparation'],
        ['In Transit', 'Transit'],
        ['Clearance Processing', 'Customs Clearance'],
        ['Permit Processing', 'Customs Clearance'],
        ['Out for Delivery', 'Last Mile Delivery'],
        ['Pending Payment', 'On Hold'],
        ['Pending Client Action', 'On Hold'],
        ['On Hold', 'On Hold']
    ]);
    statusNotFound = false; 
    currentAccordionStatus;
    trackingData;
    timelineData = [];

    @wire(getTimelineData, {countryId:'$shipment.destinationId'})
    timelineWire(result){
        if(result.data){
            this.timelineData = result.data;
            // console.log('Timeline data: ', this.timelineData);
        }
    }

    connectedCallback(){
        this.currentAccordionStatus = this.accordionStatusMap.get(this.shipment.status);
        // if current status is not part listed in map above, will render 1 accordion named Tracking Events and put everything under it
        if (!this.currentAccordionStatus){
            this.statusNotFound = true;
            this.currentAccordionStatus = 'Tracking Events';
        }

        getTrackingItems({caseId: this.shipment.Id})
        .then((result) => {
            if (result){
                this.trackingData = [...result];
                // console.log('Tracking data: ', JSON.stringify(this.trackingData));

                // if there is no tracking data, push generic message
                if (this.trackingData.length === 0)
                    this.trackingData.push({
                        Id: 'ABC',
                        DateTime__c: new Date().toISOString(),
                        Status__c: this.currentAccordionStatus,
                        Description__c: 'There are no tracking details currently available. Please check back later!'
                    });
                else{
                    // if current status is on hold, go through already desc sorted list and find most recent valid status
                    if (this.currentAccordionStatus === 'On Hold'){
                        for (let i = 0; i < this.trackingData.length; i++){
                            const statusCheck = this.accordionStatusMap.get(this.trackingData[i].Status__c);
                            if (statusCheck && statusCheck !== 'On Hold'){
                                this.currentAccordionStatus = statusCheck;
                                break;
                            }
                        }
                    }
                    // if still on hold (no valid prev statuses), set to tracking events
                    if (this.currentAccordionStatus === 'On Hold'){
                        this.statusNotFound = true;
                        this.currentAccordionStatus = 'Tracking Events';
                    }

                    // reorder events to be in asc order for display
                    this.trackingData.reverse();
                }
            }

            // console.log('Current accordion status: ', this.currentAccordionStatus);
        }).catch((error) => console.error('Error fetching tracking items: ', error));
    }

    get status(){
        return this.shipment.status;
    }

    get statusDescription(){
        return this.shipment.statusDescription;
    }

    get nextStep(){
        return this.shipment.nextStep;
    }

    get nextStepDescription(){
        return this.shipment.nextStepDescription;
    }

    get nextStepContainerClasses(){
        if (this.shipment.nextStep === 'To be determined')
            return 'border border-radius--medium flex space-between ptm prm pbm plm background--gray'
        else
            return 'border border-radius--medium flex space-between ptm prm pbm plm background--white';
    }

    get nextStepHeaderClasses(){
        if (this.shipment.nextStep === 'To be determined')
            return "p mbr";
        return "p mbr";
    }

    get nextStepMessageClasses(){
        if (this.shipment.nextStep === 'To be determined')
            return "h3 mbr";
        return "h3 mbr";
    }

    get nextStepDescriptionClasses(){
        if (this.shipment.nextStep === 'To be determined')
            return 'p'
        else
            return 'p';
    }

    get etaContainerClasses(){
        if (this.etaMessage === 'Pending')
            return "border border-radius--medium flex space-between ptm prm pbm plm background--gray";
        return "border border-radius--medium flex space-between ptm prm pbm plm background--white";
    }

    get etaHeaderClasses(){
        if (this.etaMessage === 'Pending')
            return "p mbr";
        return "p mbr";
    }

    get etaMessageClasses(){
        if (this.etaMessage === 'Pending')
            return "h3 mbr";
        return "h3 mbr";
    }

    get etaDescriptionClasses(){
        if (this.etaMessage === 'Pending')
            return "p";
        return "p";
    }

    get containerClasses(){
        if (this.delivered)
            return 'mbm'
        else
            return 'grid grid-columns-three mbm'
    }

    get delivered(){
        // want to show both delivered and closed cases as delivered. use poddate to determine this (in case case was closed but not delivered)
        return this.shipment.podDate && (this.shipment.status === 'Delivered' || this.shipment.status === 'Closed') ? true : false;
    }

    get isDropShip() {
        return this.shipment?.dropship;
    }

    get etaMessage(){
        if (this.timelineData.length === 0)
            return 'Pending';
        switch (this.shipment.status){
            case 'Received @ FGX':
            case 'Pending Manifest':
            case 'Pending Doc Approval':
            case 'Pending Permit': {
                const minDays = this.timelineData.reduce((total, item) => total + item.Days_Min__c, 0);
                const maxDays = this.timelineData.reduce((total, item) => total + item.Days_Max__c, 0);
                return `${minDays} to ${maxDays} working days`;
            }
            case 'In Transit': {
                let minDays = 0;
                let maxDays = 0;
                this.timelineData.forEach((obj) => {
                    if(obj.Category__c !== "Manifest & Document Preparation"){
                        minDays += obj.Days_Min__c;
                        maxDays += obj.Days_Max__c;
                    }
                });
                return `${minDays} to ${maxDays} working days`;
            }
            case 'Clearance Processing': 
            case 'Permit Processing': {
                let minDays = 0;
                let maxDays = 0;
                this.timelineData.forEach((obj) => {
                    if(obj.Category__c !== "Manifest & Document Preparation" && obj.Category__c !== 'Direct Air Freight'){
                        minDays += obj.Days_Min__c;
                        maxDays += obj.Days_Max__c;
                    }
                });
                return `${minDays} to ${maxDays} working days`;
            }

            case 'Out for Delivery': {
                let minDays = 0;
                let maxDays = 0;
                this.timelineData.forEach((obj) => {
                    if(obj.Category__c !== "Manifest & Document Preparation" && obj.Category__c !== 'Direct Air Freight'
                        && obj.Category__c !== 'Customs Clearance' && obj.Category__c !== 'Permit Processing'){
                        minDays += obj.Days_Min__c;
                        maxDays += obj.Days_Max__c;
                    }
                });
                return `${minDays} to ${maxDays} working days`;
            }
            case 'Booked':
            case 'Enroute to FGX':
            case 'Scheduling Pickup':
            case 'Crating/Packing':
            case 'Pending Payment':
            case 'On Hold':
            case 'Pending Client Action':
            case 'Quote Request':
            case 'Quoted':
            default:
                return 'Pending';
        }
    }

    get etaDescription(){
        if (this.timelineData.length === 0)
            return 'An estimated timeline is not available';
        switch (this.shipment.status){
            case 'Booked':
            case 'Enroute to FGX':
            case 'Scheduling Pickup':
                return 'An estimate will be available once the shipment is received at FGX';
            case 'Received @ FGX': 
            case 'Pending Manifest':
            case 'Pending Doc Approval':
            case 'Pending Permit':
            case 'In Transit':
            case 'Clearance Processing': 
            case 'Permit Processing':
            case 'Out for Delivery':
                return this.shipment.domesticShip ? 'Based on typical processing times' : 'Based on typical processing times, pending customs';
            case 'Crating/Packing':
                return 'An estimate will be available once packing is completed';
            case 'Pending Payment':
                return 'An estimate will be available once payment in processed';
            case 'On Hold':
                return 'An estimate will be available once the hold is cleared';
            case 'Pending Client Action':
                return 'An estimate will be available when action items are resolved';
            case 'Quote Request':
            case 'Quoted':
                return 'An estimate will be available when the quote is accepted';
            default:
                return 'An estimated timeline is not available';
        }
    }

    get collectionLabel() {
        return this.isDropShip ? 'You will ship to FGX' : 'Collection';
    }

    get currentStatusIcon() {
        return resources + '/images/status--acknowledge.svg';
    }

    get nextStatusIcon() {
        return resources + '/images/status--change.svg';
    }

    get calendarIcon() {
        return resources + '/images/calendar.svg';
    }

    get returnIcon() {
        return resources + '/images/returnarrow.svg';
    }

    get rightKernelIcon() {
        return resources + '/images/rightkernel.svg';
    }

    get warningIcon() {
        return resources + '/images/warning.svg';
    }
}