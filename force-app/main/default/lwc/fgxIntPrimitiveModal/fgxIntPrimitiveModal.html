<template>
    <template lwc:if={showButton}>
        <template lwc:if={buttonIsIcon}>
            <div class="icon-container" onclick={handleClick}>
                <lightning-icon 
                    icon-name={iconName}
                    title={buttonTitle}>
                </lightning-icon>
            </div>
        </template>
        <template lwc:else>
            <lightning-button
                title={buttonTitle}
                loading={loading}
                variant={buttonVariant}
                label={buttonName}
                disabled={disabled}
                icon-name={iconName}
                icon-position={iconPosition}
                class={buttonTextClass}
                onclick={handleClick}>
            </lightning-button>
        </template>
    </template>
    <div class={hideRevealClass}>
        <section class="modal slds-fade-in-open" style={modalStyles}>
            <div class={containerClasses}>
                <div class="modal_header" style="width:100%;">
                    <h1 class="modal_title slds-hyphenate">
                        {header}
                    </h1>
                </div>
                <div class={contentClasses} style="width:100%;">
                    <slot name="body"></slot>
                </div>
                <div class="modal_footer" style="width:100%;">
                    <slot name="footer" onclose={handleClose}></slot>
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open" style={backdropStyles}></div>
    </div>
</template>