/**
 * fgxEntQuoteRequestContent
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 12/5/23
 */
import { api, LightningElement, track, wire } from 'lwc';
import getSites from "@salesforce/apex/FGXEnt_QuoteRequestContentController.getSites";
import getEntities from "@salesforce/apex/FGXEnt_QuoteRequestContentController.getEntities";
import getAccounts from "@salesforce/apex/FGXEnt_QuoteRequestContentController.getAccounts";
import getInternalEOR from "@salesforce/apex/FGXEnt_QuoteRequestContentController.getInternalEOR";
import getInternalIOR from "@salesforce/apex/FGXEnt_QuoteRequestContentController.getInternalIOR";
import getPlaceholderEntity from "@salesforce/apex/FGXEnt_QuoteRequestContentController.getPlaceholderEntity";
import getData from '@salesforce/apex/FGXEnt_QuoteRequestContentController.getData';
import getBOMLinesGPT from '@salesforce/apex/FGXEnt_QuoteRequestContentController.getBOMLinesGPT';
import getBOMLinesCSV from '@salesforce/apex/FGXEnt_QuoteRequestContentController.getBOMLinesCSV';
import getCountries from '@salesforce/apex/CountryLookupController.getCountries';

import { refreshApex } from "@salesforce/apex";
import {DismissToastEvent, ShowToastEvent} from 'c/showToastEvent';
import { getConstants, getEvents } from 'c/fgxEntConstants';
import { getUserRecord } from "c/userService";
import { getCurrencyData } from "c/currencyService";
import { normalizeBoolean } from 'c/utilsPrivate';
import { getPages, getActionMessages, getRenderMessages } from 'c/fgxEntConstants';
import { getObjectInfo, getPicklistValues } from "lightning/uiObjectInfoApi";

import labels from 'c/customLabels';
import {
    subscribe,
    publish,
    unsubscribe,
    APPLICATION_SCOPE,
    MessageContext,
} from 'lightning/messageService';
import action from '@salesforce/messageChannel/ActionChannel__c';

const PAGES = getPages();
const RENDER_MESSAGES = getRenderMessages();
const ACTION_MESSAGES = getActionMessages();
const CONSTANTS = getConstants();
const EVENTS = getEvents();

const CUSTOMER_DOESNT_KNOW = 'Customer Doesn\'t Know';
const ENABLED = 'Enabled';

import FgxEntQuoteRequestBasics from "c/fgxEntQuoteRequestBasics";
import resources from '@salesforce/resourceUrl/FGXEnterpriseResources';

import stepOne from "./step1.html";
import stepTwo from "./step2.html";
import stepThree from "./step3.html";
import stepFour from "./step4.html";
import stepFive from "./step5.html";
import stepSix from "./step6.html";
import stepSeven from "./step7.html";
import stepEight from "./step8.html";
import stepNine from "./step9.html";
import stepTen from "./step10.html";
import stepEleven from "./step11.html";

import SCHEMA from 'c/fgxEntSchemaImports';
import pdfJS from '@salesforce/resourceUrl/pdfJS';
import pdfWORKER from '@salesforce/resourceUrl/pdfWORKER';
import coreJS from '@salesforce/resourceUrl/coreJSBundleMin';
import {loadScript} from "lightning/platformResourceLoader";
import {classSet} from "c/utils";
import {isEmptyString} from "c/inputUtils";

const VARIANT = CONSTANTS.VARIANT;
const AVAILABLE_STATUSES = ['Pending Review', 'Approved'];
const BOM_CONFIRM_BAD_INPUT = 'The total value entered does not match the bill of materials, please click back and verify the Bill of Materials was correctly uploaded';
const BOM_CONFIRM_VALUE_MISSING = 'Please re-enter the total BOM value to confirm the Bill of Materials was correctly uploaded';

export default class FgxEntQuoteRequestContent extends FgxEntQuoteRequestBasics {

    @track _step = 0;
    @track _variant = VARIANT.NEW;
    @track _isUpdateContinuation = false;
    @track originDefaultTab = 1;
    @track destinationDefaultTab = 1;

    /**
     * this is used to store temp data that
     * needs to be confirmed by user before
     * we can persist it
     */
    @track _draftData = undefined;
    @track _draftRelatedRecordData = undefined;
    @track user;
    @track currencyData;
    @track _data = {}; // Quote
    @track _relatedRecordData = {}; // Quote Version
    @track _relatedRecordBOMLineData = [];
    @track _relatedRecordPieceLineData = [];
    @track _relatedRecordQuoteLineData = [];
    @track _relatedRecordBOMLineCSVDataErrors = {};
    @track _relatedRecordBOMLineCSVFatalErrors = [];
    @track _generateNewQuote = false;
    @track _generateNewQuoteVersion = false;

    @track undoBOMBuffer = [];
    @track undoPackageLineBuffer = [];

    @track bomCSVImportComplete = false;
    @track bomAIImportComplete = false;

    subscription;
    weightUnitSelection = 'lb';
    dimensionUnitSelection = 'in';
    reclaimableTaxConfirmation = false;
    thirdPartyConfirmation = false;
    monetaryPaymentsConfirmation = false;
    purchasingEntityConfirmation = false
    showFileUploadWarning = false;
    showSiteSurvey = false;
    initialLoadComplete = false;
    defaultBOMInputTab;
    _bomInputComponentCache;
    _pieceInputComponentCache;
    createSiteId;
    deadlineSelect = null;

    label = labels.getLabels();

    @track _stepTemplates = [
        {step: 0, pathStep: 0, template: stepOne},
        {step: 1, pathStep: 1, template: stepTwo},
        {step: 2, pathStep: 2, template: stepThree},
        {step: 3, pathStep: 3, template: stepFour},
        {step: 4, pathStep: 4, template: stepFive},
        {step: 5, pathStep: 5, template: stepSix},
        {step: 6, pathStep: 5, template: stepSeven},
        {step: 7, pathStep: 6, template: stepEight},
        {step: 8, pathStep: 7, template: stepNine},
        {step: 9, pathStep: 8, template: stepTen, submitStep: true},
        {step: 10, pathStep: 9, template: stepEleven, finalStep: true},
    ];

    /**
     * An array of objects in the following format:
     * {[SCHEMA.ENTITY_COUNTRY.fieldApiName]: [0]} where there
     * is a key representing the field api name and the value
     * is an array of steps that it applies to for revoking. If
     * a change of that field on that step happens and Flow_Status
     * is currently Approved, the status will update from Approved
     * to In Draft until it is submitted. If it is submitted at the
     * time of the reverting change it would be set to Pending Review
     */
    revokeApprovedFields = [];

    /**
     * An array of objects in the following format:
     * {[SCHEMA.ENTITY_IMPORT_STATUS.fieldApiName]: [0, 1, 2, 3, 4, 5]},
     * where the key represents the status the value side of
     * steps applies to. In the example above, the Import Status field
     * would be the status field pertaining to steps 0 - 5. This is used
     * to handle revoking approvals and which field to send to Pending Review
     * when submitting.
     * @type {[]}
     */
    statusFieldByStep = [];
    wireRecordData;
    siteWire;
    entityWire;
    accountWire;
    siteRecords = [];
    entityRecords = [];
    countryRecords = [];
    countryOptions = [];
    accountRecords = [];
    internalEOR;
    internalIOR;
    placeholderEntity;
    multiTabRenderWait;
    sectionPopupRenderWait;
    showLoading = false;
    intangibleModalShown = false;
    bomInputValidityCheckOnRender = false;
    newQuoteGenerated = false;
    newQuoteVersionGenerated = false;
    loadingLabel;
    originDestinationCache = {}
    _relatedRecordId = '';
    cachedRelatedFiles;

    @api recordId;
    @api objectApiName = SCHEMA.QUOTE_OBJ.objectApiName;
    @api relatedObjectApiName = SCHEMA.QUOTE_VERSION_OBJ.objectApiName;

    /**
     * The parent stepper component does quite a bit of logic to handle setting
     * status fields automatically. Mainly, if set to true, this will allow the stepper
     * to default the status field pertaining to the current step to "In Draft" if it
     * is currently blank. Additionally, it will automatically set the current steps
     * status field to "Pending Review" when "Save & Submit" is called. Add Entity has
     * different logic so we needed to ignore this automatic logic somehow.
     * @type {boolean}
     */
    @api allowAutoStatusUpdates = false;

    @api
    get relatedRecordId() {
        return this._relatedRecordId;
    }
    set relatedRecordId(value) {
        if(value) {
            this._relatedRecordId = value;
        }
    }

    @api
    get step() {
        return this._step;
    }
    set step(value) {
        value = parseInt(value);
        if(value >= 0) {
            let previous = this._step;
            this._step = value;
            this.notifyStepChange(previous);
        }
    }

    @api
    get variant() {
        return this._variant;
    }
    set variant(value) {
        if(value) {
            this._variant = value;
        }

        if(this.isCloneMode) {
            this.generateNewQuoteVersion = true;
            this.generateNewQuote = true;
        }

        if(this.isUpdateMode) {
            this.reclaimableTaxConfirmation = true;
            this.thirdPartyConfirmation = true;
            this.monetaryPaymentsConfirmation = true;
            this.purchasingEntityConfirmation = true;
            this.generateNewQuoteVersion = !this.isUpdateContinuation;
        }
    }

    @api
    get isUpdateContinuation() {
        return this._isUpdateContinuation;
    }
    set isUpdateContinuation(value) {
        this._isUpdateContinuation = value;
        if(value) {
            if(this.generateNewQuoteVersion) {
                this.generateNewQuoteVersion = false;
            }
        }
    }

    @api
    get stepTemplates() {
        return this._stepTemplates;
    }
    set stepTemplates(value) {
        this._stepTemplates = value;
    }

    @api
    get generateNewQuote() {
        return this._generateNewQuote;
    }
    set generateNewQuote(value) {
        this._generateNewQuote = value;
    }

    @api
    get generateNewQuoteVersion() {
        return this._generateNewQuoteVersion;
    }
    set generateNewQuoteVersion(value) {
        this._generateNewQuoteVersion = value;
    }

    @api setNewQuoteGenerated() {
        this.newQuoteGenerated = true;
        this.generateNewQuote = false;
    }

    @api setNewQuoteVersionGenerated() {
        this.newQuoteVersionGenerated = true;
        this.generateNewQuoteVersion = false;
    }

    @api
    get isApprovedStatus() {
        let statusField = this.getStatusFields();
        if(statusField) {
            return statusField.some(status => this._data[status] === CONSTANTS.RECORD_STATUS_APPROVED);
        }
        return this._data[CONSTANTS.FLOW_STATUS_FIELD] === CONSTANTS.RECORD_STATUS_APPROVED;
    }

    @api modifyingCloningRecord;

    @wire(MessageContext)
    messageContext;

    @wire(getObjectInfo, {objectApiName: SCHEMA.QUOTE_OBJ})
    objectInfo;

    get multiTabComponent() {
        return this.template.querySelector('c-fgx-ent-multi-tab-content');
    }

    get recordData() {
        return this.inConfirmMode ? this._draftData : this._data;
    }

    get relatedRecordData() {
        return this.inConfirmMode ? this._draftRelatedRecordData : this._relatedRecordData;
    }

    /**
     * @description: Returns a map of a given object
     * to it's respective data getter
     * @type {{}}
     */
    get objectToRecord() {
        return {
            [this.QUOTE_OBJ]: this.recordData,
            [this.QUOTE_VERSION_OBJ]: this.relatedRecordData,
            undefined: this.recordData
        }
    }

    get bomInputComponentCache() {
        this._bomInputComponentCache = this.template.querySelectorAll('c-fgx-ent-bom-input');
        return this._bomInputComponentCache;
    }

    get pieceInputComponentCache() {
        this._pieceInputComponentCache = this.template.querySelectorAll('c-fgx-ent-piece-input');
        return this._pieceInputComponentCache;
    }

    get inConfirmMode() {
        return this._draftData !== undefined || this._draftRelatedRecordData !== undefined;
    }

    get pathStep() {
        return this.stepTemplates.find((item) => item.step === this.step)?.pathStep;
    }

    get toolTipIcon() {
        return resources + '/images/tooltip.svg'
    }

    get globeArrowIcon() {
        return resources + '/images/globearrow.svg'
    }

    get inventoryIcon() {
        return resources + '/images/inventory.svg';
    }

    get bizIcon() {
        return resources + '/images/biz_icon.svg';
    }

    get balanceSheetIcon() {
        return resources + '/images/balance_sheet.svg';
    }

    get lightningIcon() {
        return resources + '/images/lightning.svg';
    }

    get documentsIcon() {
        return resources + '/images/documents.svg';
    }

    get quotesIcon() {
        return resources + '/images/quotes.svg';
    }

    get calendarIcon() {
        return resources + '/images/calendar.svg';
    }

    get flagIcon() {
        return resources + '/images/flag.svg';
    }

    get forceCacheRelatedFiles() {
        return this.isUpdateMode && this.generateNewQuoteVersion;
    }

    get totalBOMLineCount() {
        if(this._relatedRecordBOMLineData) {
            return this._relatedRecordBOMLineData.length;
        }
        return 0;
    }

    get totalPieceCount() {
        if(this._relatedRecordPieceLineData) {
            return this._relatedRecordPieceLineData.reduce(
                (subTotal, piece) => {
                    return subTotal + parseInt(piece[SCHEMA.PACKAGE_LINE_QUANTITY.fieldApiName] ? piece[SCHEMA.PACKAGE_LINE_QUANTITY.fieldApiName] : 0)
                },0
            );
        }
        return 0;
    }

    get totalGrossWeight() {
        let total = 0;
        if(this._relatedRecordPieceLineData) {
            total = this._relatedRecordPieceLineData.reduce(
                (subTotal, piece) => {
                    return subTotal + (
                        (piece[SCHEMA.PACKAGE_LINE_UNIT_WEIGHT.fieldApiName] ? piece[SCHEMA.PACKAGE_LINE_UNIT_WEIGHT.fieldApiName] : 0) *
                        (piece[SCHEMA.PACKAGE_LINE_QUANTITY.fieldApiName] ? piece[SCHEMA.PACKAGE_LINE_QUANTITY.fieldApiName] : 0)
                    )
                },0
            );
            if(!total) {
                total = 0;
            }
        }
        return `${total.toLocaleString('en-US',{ minimumFractionDigits: 2 })} ${this.weightUnitSelection}`;
    }

    get bomInputMultiTabButtonOneLabel() {
        return (this.relatedRecordData[SCHEMA.QUOTE_VERSION_BOM_AI_INGESTED.fieldApiName] || this.relatedRecordData[SCHEMA.QUOTE_VERSION_BOM_CSV_INGESTED.fieldApiName]) ? 'Review Uploaded BOM' : 'Manually Enter BOM';
    }

    get totalBOMLineValue() {
        if(this._relatedRecordBOMLineData) {
            return this._relatedRecordBOMLineData.reduce(
                (subTotal, bomLine) => {
                    return subTotal + (
                        (bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] : 0)  *
                        (bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] : 0)
                    )
                },0
            );
        }
    }

    get totalBOMSoftwareValue() {
        let total = 0;

        if(this._relatedRecordBOMLineData) {
            let softwareLines = this._relatedRecordBOMLineData.filter((item) => (item[SCHEMA.BOM_LINE_CATEGORY.fieldApiName] !== 'Hardware' && !isEmptyString(item[SCHEMA.BOM_LINE_CATEGORY.fieldApiName])));

            total = softwareLines.reduce(
                (subTotal, bomLine) => {
                    return subTotal + (
                        (bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] : 0)  *
                        (bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] : 0)
                    )
                },0
            );
            if(!total) {
                total = 0;
            }
        }
        return total;
    }

    get totalBOMHardwareValue() {
        let total = 0;

        if(this._relatedRecordBOMLineData) {
            let hardwareLines = this._relatedRecordBOMLineData.filter((item) => (item[SCHEMA.BOM_LINE_CATEGORY.fieldApiName] === 'Hardware' || isEmptyString(item[SCHEMA.BOM_LINE_CATEGORY.fieldApiName])));

            total = hardwareLines.reduce(
                (subTotal, bomLine) => {
                    return subTotal + (
                        (bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] : 0)  *
                        (bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] : 0)
                    )
                },0
            );
            if(!total) {
                total = 0;
            }
        }
        return total;
    }

    get lineBreakStyles() {
        return classSet(
            `line-break mbm`
        )
            .add({
                'mtm': this.showIOREORConfirmation
            })
            .toString();
    }

    get csvImportClasses() {
        return classSet(
            ``
        )
            .add({
                'visible': !this.bomCSVImportComplete,
                'hidden': this.bomCSVImportComplete
            })
            .toString();
    }

    get csvImportResultsClasses() {
        return classSet(
            ``
        )
            .add({
                'visible': this.bomCSVImportComplete,
                'hidden': !this.bomCSVImportComplete
            })
            .toString();
    }

    get aiImportClasses() {
        return classSet(
            ``
        )
            .add({
                'visible': !this.bomAIImportComplete,
                'hidden': this.bomAIImportComplete
            })
            .toString();
    }

    get aiImportResultsClasses() {
        return classSet(
            ``
        )
            .add({
                'visible': this.bomAIImportComplete,
                'hidden': !this.bomAIImportComplete
            })
            .toString();
    }

    get uploadBOMClasses(){
        return classSet(
            `relative`
        )
            .add({
                'height-60px ptl pbl': this.showFileUploadWarning
            })
            .toString();
    }

    get currencyOptions() {
        return this.currencyData?.currencyIsoValues.map((isoCode) => {
            return {label: isoCode, value: isoCode};
        });
    }

    get destinationUSStateOptions() {
        if(this.destinationStateUSPicklistValues) {
            return this.destinationStateUSPicklistValues?.data?.values?.map(val => ({label: val.label, value: val.value}));
        }
        return [];
    }

    get originUSStateOptions() {
        if(this.originStateUSPicklistValues) {
            return this.originStateUSPicklistValues?.data?.values?.map(val => ({label: val.label, value: val.value}));
        }
        return [];
    }

    get setConditionOptions() {
        return [
            {label: 'New', value: 'New'},
            {label: 'Used', value: 'Used'},
            {label: 'Refurbished', value: 'Refurbished'},
            {label: 'Clear All', value: ''}
        ]
    }

    get weightOptions() {
        return [
            {label: 'Pounds', value: 'lb'},
            {label: 'Kilograms', value: 'kg'}
        ];
    }

    get dimensionOptions() {
        return [
            {label: 'Inches', value: 'in' },
            {label: 'Centimeters', value: 'cm'}
        ];
    }

    get deadlineOptions() {
        return [
            {label: 'Yes, I have a deadline', value: true},
            {label: 'No specific deadline, please balance speed and cost', value: false}
        ];
    }

    get packingConfigOptions() {
        return [
            {value: 'Bare Metal', label: 'The equipment is loose and not packed (Bare Metal)'},
            {value: 'Loose Boxes (non-OEM)', label: 'The equipment was repacked and is currently in non-OEM boxes'},
            {value: 'OEM Boxes', label: 'The equipment is in the original OEM boxes'},
            {value: 'Boxed & Palletized', label: 'The equipment is boxed up well and packed onto a pallet'},
            {value: 'Custom Crated', label: 'The equipment is custom packed in wooded crates'},
            {value: 'Flight Cases', label: 'The equipment is in flight cases'},
            {value: 'Pre-Installed in Cabinet', label: 'The equipment has been pre-installed into a rack or cabinet'},
            {value: 'I Don\'t Know', label: 'I don\'t know yet (no problem, FGX will follow-up)'}
        ];
    }

    get dimWeightEntryOptions() {
        return [
            {value: 'Provided By Client', label: 'Yes (and I can enter them now)'},
            {value: 'Estimate For Client', label: 'No (please estimate this for me)'}
        ];
    }

    get approvedPendingSiteOptions() {
        if(this.siteRecords) {
            return this.siteRecords.map((site) => {
                return {
                    label: site.Site_Name__c,
                    value: site.Id,
                    image: site.Country__r?.Flag_Icon__c ? `${resources}/images/flags/${site.Country__r?.Flag_Icon__c}` : null,
                    subItems:[site.Site_Type__c, site.Site_Code__c, site.Formatted_Address__c].filter((item) => item != null),
                    additionalSearchableItems:[site.Country__r?.Name].filter((item) => item != null)
                };
            });
        }
        return [];
    }

    get cachedSiteId() {
        if(this.siteRecords) {
            if(this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD]) {
                let outerSplit = this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD].split(';');
                if(outerSplit.length === 2) {
                    let searchChoice = outerSplit[0].split(':');
                    let location = outerSplit[1].split(':');

                    if(searchChoice[1] === 'Site') {
                        return location[1];
                    }
                }
            }
        }
        return null;
    }

    get cachedCity() {
        if(this.countryRecords) {
            if(this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD]) {
                let outerSplit = this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD].split(';');
                if(outerSplit.length === 2) {
                    let searchChoice = outerSplit[0].split(':');
                    let location = outerSplit[1].split(':');

                    if(searchChoice[1] === 'Google') {
                        let cityCountry = location[1].split(',');
                        if(cityCountry.length >= 2) {
                            return cityCountry[0];
                        }
                    }
                }
            }
        }
        return null;
    }

    get cachedCountryId() {
        if(this.countryRecords) {
            if(this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD]) {
                let outerSplit = this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD].split(';');
                if(outerSplit.length === 2) {
                    let searchChoice = outerSplit[0].split(':');
                    let location = outerSplit[1].split(':');

                    if(searchChoice[1] === 'Google') {
                        let cityCountry = location[1].split(',');
                        if(cityCountry.length >= 2) {
                            return cityCountry[1];
                        }
                    }
                }
            }
        }
        return null;
    }

    get cachedState() {
        if(this.countryRecords) {
            if(this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD]) {
                let outerSplit = this.recordData[this.ORIGIN_ADDRESS_CACHE_FIELD].split(';');
                if(outerSplit.length === 2) {
                    let searchChoice = outerSplit[0].split(':');
                    let location = outerSplit[1].split(':');

                    if(searchChoice[1] === 'Google') {
                        let cityCountry = location[1].split(',');
                        if(cityCountry.length === 3) {
                            return cityCountry[2];
                        }
                    }
                }
            }
        }
        return null;
    }

    get originCityCountry() {
        if(this.isDropShipToFGX) {
            return 'FGX New Jersey'
        }
        if(this.originCity && this.originCountryName) {
            let vals = [];
            if(this.originCity) {
                vals.push(this.originCity);
            }
            if(this.isOriginCountryUS && this.originStateAbbr) {
                vals.push(this.originStateAbbr);
            }
            if(this.originCountryName) {
                vals.push(this.originCountryName);
            }
            return vals.join(', ');
        }
        return '';
    }

    get destinationCityCountry() {
        if(this.destinationCity && this.destinationCountryName) {
            let vals = [];
            if(this.destinationCity) {
                vals.push(this.destinationCity);
            }
            if(this.isDestinationCountryUS && this.destinationStateAbbr) {
                vals.push(this.destinationStateAbbr);
            }
            if(this.destinationCountryName) {
                vals.push(this.destinationCountryName);
            }
            return vals.join(', ');
        }
        return '';
    }

    get isDropShipToFGX() {
        return this.relatedRecordData[this.QUOTE_VERSION_DROP_SHIP_FGX_FIELD];
    }

    /**
     * @description Returns the US country record
     * @returns {*|null}
     */
    get usCountryRecord() {
        if(this.countryRecords) {
            return this.countryRecords.find((country)=>(country[SCHEMA.COUNTRY_CODE.fieldApiName] === 'US'));
        }
        return null;
    }

    /**
     * @description: Returns whether this
     * is a domestic quote ie. Origin Country == Destination Country
     * @returns {boolean}
     */
    get isDomestic() {
        return this.originCountry === this.destinationCountry;
    }

    /**
     * @description Returns the Quote__c.Origin_Country__c
     * record if there is one associated to the quote
     * @returns {*|null}
     */
    get originCountry() {
        if(this.recordData[this.ORIGIN_COUNTRY_FIELD] && this.countryRecords) {
            return this.countryRecords.find((country)=>(country.Id === this.recordData[this.ORIGIN_COUNTRY_FIELD]));
        } else if(this.isDropShipToFGX) {
            return this.usCountryRecord;
        }
        return null;
    }

    /**
     * @description Returns the Quote__c.Origin_City__c
     * value
     * @returns {*}
     */
    get originCity() {
        return this.recordData[this.ORIGIN_CITY_FIELD];
    }

    /**
     * @description Returns the 2 letter abbreviation of the
     * currently selected origin state
     * @returns {*}
     */
    get originStateAbbr() {
        let stateUS = this.recordData[this.ORIGIN_STATE_US_FIELD];
        if(stateUS) {
            return CONSTANTS.STATE_TO_ABBRV[this.recordData[this.ORIGIN_STATE_US_FIELD].toLowerCase()];
        }
        return null;
    }

    /**
     * @description Returns the Quote__c.Destination_Country__c
     * record if there is one associated to the quote
     * @returns {*|null}
     */
    get destinationCountry() {
        if(this.recordData[this.DESTINATION_COUNTRY_FIELD] && this.countryRecords) {
            return this.countryRecords.find((country)=>(country.Id === this.recordData[this.DESTINATION_COUNTRY_FIELD]));
        }
        return null;
    }

    /**
     * @description Returns the destination City
     * @returns {*}
     */
    get destinationCity() {
        return this.recordData[this.DESTINATION_CITY_FIELD];
    }

    /**
     * @description Returns the 2 letter abbreviation of the
     * currently selected destination state
     * @returns {*}
     */
    get destinationStateAbbr() {
        let stateUS = this.recordData[this.DESTINATION_STATE_US_FIELD];
        if(stateUS) {
            return CONSTANTS.STATE_TO_ABBRV[this.recordData[this.DESTINATION_STATE_US_FIELD].toLowerCase()];
        }
        return null;
    }

    /**
     * @description Returns the Origin_Country__r.Countries__c.CustomsCode_Label__c
     * field if there is a origin country specified on the quote
     * @returns {*}
     */
    get originCountryCustomsCodeLabel() {
        if(this.originCountry) {
            if(this.originCountry[SCHEMA.COUNTRY_CUSTOMSCODE_LABEL.fieldApiName]) {
                return this.originCountry[SCHEMA.COUNTRY_CUSTOMSCODE_LABEL.fieldApiName];
            } else {
                return 'Customs Code';
            }
        }
    }

    /**
     * @description Returns the Destination_Country__r.Countries__c.CustomsCode_Label__c
     * field if there is a destination country specified on the quote
     * @returns {*}
     */
    get destinationCountryCustomsCodeLabel() {
        if(this.destinationCountry) {
            if(this.destinationCountry[SCHEMA.COUNTRY_CUSTOMSCODE_LABEL.fieldApiName]) {
                return this.destinationCountry[SCHEMA.COUNTRY_CUSTOMSCODE_LABEL.fieldApiName];
            } else {
                return 'Customs Code';
            }
        }
    }

    /**
     * @description Returns selected origin country code
     * @returns {*|null}
     */
    get originCountryCode() {
        if(this.originCountry) {
            return this.originCountry[SCHEMA.COUNTRY_CODE.fieldApiName];
        }
        return null;
    }

    /**
     * @description Returns selected origin country name
     * @returns {*|null}
     */
    get originCountryName() {
        if(this.originCountry) {
            return this.originCountry[SCHEMA.COUNTRY_NAME.fieldApiName];
        }
        return null;
    }

    /**
     * @description Returns selected destination country code
     * @returns {*|null}
     */
    get destinationCountryCode() {
        if(this.destinationCountry) {
            return this.destinationCountry[SCHEMA.COUNTRY_CODE.fieldApiName];
        }
        return null;
    }

    /**
     * @description Returns selected destination country name
     * @returns {*|null}
     */
    get destinationCountryName() {
        if(this.destinationCountry) {
            return this.destinationCountry[SCHEMA.COUNTRY_NAME.fieldApiName];
        }
        return null;
    }

    /**
     * @description Returns whether or not the currently selected
     * origin country is the US
     * @returns {boolean}
     */
    get isOriginCountryUS() {
        if(this.originCountry) {
            return this.originCountry[SCHEMA.COUNTRY_CODE.fieldApiName] === 'US';
        } else if(this.isDropShipToFGX) {
            return true;
        }
        return false;
    }

    /**
     * @description Returns whether or not the currently selected
     * destination country is the US
     * @returns {boolean}
     */
    get isDestinationCountryUS() {
        if(this.destinationCountry) {
            return this.destinationCountry[SCHEMA.COUNTRY_CODE.fieldApiName] === 'US';
        }
        return false;
    }

    /**
     * @description Returns a filtered list of the entityRecords
     * that has the same Country as Quote.Origin_Country__c
     * @returns {*[]}
     */
    get originCountryEntityRecords() {
        if(this.entityRecords) {
            return this.entityRecords.filter((entity) => (
                (
                    entity.Country__c === this.recordData[this.ORIGIN_COUNTRY_FIELD] ||
                    (this.isDropShipToFGX && entity.Country__c === this.usCountryRecord.Id)
                )
            ));
        }
        return [];
    }

    /**
     * @description Returns the tooltip for the origin address
     * input
     * @returns {string}
     */
    get originTooltip() {
        if(this.isUpdateMode) {
            return 'The origin address is locked when updating quotes. Please create a new request or clone this quote to modify the origin address.';
        }
        return 'To reduce costs, we recommend that you drop-ship directly from your vendor to an FGX facility.';
    }

    /**
     * @description Returns the tooltip for the destination address
     * input
     * @returns {string}
     */
    get destinationTooltip() {
        return 'The destination address is locked when updating quotes. Please create a new request or clone this quote to modify the destination address.';
    }

    /**
     * @description Returns a filtered list of the entityRecords
     * that has the same Country as Quote.Destination_Country__c
     * @returns {*[]}
     */
    get destinationCountryEntityRecords() {
        if(this.entityRecords) {
            return this.entityRecords.filter((entity) => (
                entity.Country__c === this.recordData[this.DESTINATION_COUNTRY_FIELD]
            ));
        }
        return [];
    }

    /**
     * @description Returns the default entity for the origin
     * country if one is specified
     * @returns {*|null}
     */
    get exportEntityDefault() {
        if(this.originCountryEntityRecords) {
            return this.originCountryEntityRecords?.find((entity)=> (['Exporter Only', 'Importer & Exporter'].includes(entity.Default_Setting__c)));
        }
        return null;
    }

    /**
     * @description Returns the default entity for the origin
     * country if one is specified
     * @returns {*|null}
     */
    get importEntityDefault() {
        if(this.destinationCountryEntityRecords) {
            return this.destinationCountryEntityRecords?.find((entity)=> (['Importer Only', 'Importer & Exporter'].includes(entity.Default_Setting__c)));
        }
        return null;
    }

    get exportEntityOptions() {
        if(this.exportEntityDefault) {
            let defaultEntity = this.exportEntityDefault;
            let statusApproved = defaultEntity[SCHEMA.ENTITY_EXPORT_STATUS.fieldApiName] === 'Approved';

            let statusIndicator =  {
                icon: statusApproved ? 'green-circle' : null,
                label: statusApproved ? 'Default Exporter' : null,
            }

            return [{
                label: defaultEntity.Entity_Name__c,
                value: defaultEntity.Id,
                image: defaultEntity.Country__r?.Flag_Icon__c ? `${resources}/images/flags/${defaultEntity.Country__r?.Flag_Icon__c}` : null,
                subItems:[defaultEntity.Entity_Code__c, defaultEntity.Entity_Name__c, defaultEntity.Customs_Code__c ? `${this.originCountryCustomsCodeLabel}: ${defaultEntity.Customs_Code__c}`: null].filter((item) => item != null),
                additionalSearchableItems:[defaultEntity.Country__r?.Name].filter((item) => item != null),
                statusIndicator
            }];
        } else {
            let mappedEntityRecords = this.originCountryEntityRecords.map((entity) => {
                let entityStatus = entity[SCHEMA.ENTITY_EXPORT_STATUS.fieldApiName];
                let statusIndicator =  {};

                if(entityStatus === 'In Draft') {
                    statusIndicator['icon'] = 'red-circle';
                    statusIndicator['label'] = 'Export Status: Not Approved';
                } else if(entityStatus === 'Pending Review') {
                    statusIndicator['icon'] = 'neutral-circle';
                    statusIndicator['label'] = 'Export Status: Pending Review';
                } else if(entityStatus === 'Approved') {
                    statusIndicator['icon'] = 'green-circle';
                    statusIndicator['label'] = 'Approved Exporter';
                } else {
                    statusIndicator['icon'] = 'neutral-circle';
                    statusIndicator['label'] = 'Export Status: Not Setup';
                }

                return {
                    label: entity.Entity_Name__c,
                    value: entity.Id,
                    image: entity.Country__r?.Flag_Icon__c ? `${resources}/images/flags/${entity.Country__r?.Flag_Icon__c}` : null,
                    subItems:[entity.Entity_Code__c, entity.Entity_Name__c, entity.Customs_Code__c ? `${this.originCountryCustomsCodeLabel}: ${entity.Customs_Code__c}` : null].filter((item) => item != null),
                    additionalSearchableItems:[entity.Country__r?.Name].filter((item) => item != null),
                    statusIndicator
                };
            });

            if(!this.isOriginCountryUS) {
                return [
                    {
                        label: 'FGX Exporter of Record Service',
                        value: this.internalEOR?.Id,
                        image: `${resources}/images/logo.svg`,
                        subItems:['EOR', 'FGX', 'Licensed Trading Company', this.originCountryCode].filter((item) => item != null),
                        additionalSearchableItems:[this.internalEOR.Country__r?.Name].filter((item) => item != null),
                    },
                    ...mappedEntityRecords
                ]
            }
            return mappedEntityRecords;
        }
    }

    get importEntityOptions() {
        if(this.importEntityDefault) {
            let defaultEntity = this.importEntityDefault;
            let statusApproved = defaultEntity[SCHEMA.ENTITY_IMPORT_STATUS.fieldApiName] === 'Approved';
            let statusIndicator =  {
                icon: statusApproved ? 'green-circle' : null,
                label: statusApproved ? 'Default Importer' : null,
            }

            return [{
                label: defaultEntity.Entity_Name__c,
                value: defaultEntity.Id,
                image: defaultEntity.Country__r?.Flag_Icon__c ? `${resources}/images/flags/${defaultEntity.Country__r?.Flag_Icon__c}` : null,
                subItems:[defaultEntity.Entity_Code__c, defaultEntity.Entity_Name__c, defaultEntity.Customs_Code__c ? `${this.destinationCountryCustomsCodeLabel}: ${defaultEntity.Customs_Code__c}` : null].filter((item) => item != null),
                additionalSearchableItems:[defaultEntity.Country__r?.Name].filter((item) => item != null),
                statusIndicator
            }];
        } else {
            let mappedEntityRecords = this.destinationCountryEntityRecords.map((entity) => {
                let entityStatus = entity[SCHEMA.ENTITY_IMPORT_STATUS.fieldApiName];
                let statusIndicator =  {};

                if(entityStatus === 'In Draft') {
                    statusIndicator['icon'] = 'red-circle';
                    statusIndicator['label'] = 'Import Status: Not Approved';
                } else if(entityStatus === 'Pending Review') {
                    statusIndicator['icon'] = 'neutral-circle';
                    statusIndicator['label'] = 'Import Status: Pending Review';
                } else if(entityStatus === 'Approved') {
                    statusIndicator['icon'] = 'green-circle';
                    statusIndicator['label'] = 'Approved Importer';
                } else {
                    statusIndicator['icon'] = 'neutral-circle';
                    statusIndicator['label'] = 'Import Status: Not Setup';
                }

                return {
                    label: entity.Entity_Name__c,
                    value: entity.Id,
                    image: entity.Country__r?.Flag_Icon__c ? `${resources}/images/flags/${entity.Country__r?.Flag_Icon__c}` : null,
                    subItems:[entity.Entity_Code__c, entity.Entity_Name__c, entity.Customs_Code__c ? `${this.destinationCountryCustomsCodeLabel}: ${entity.Customs_Code__c}` : null].filter((item) => item != null),
                    additionalSearchableItems:[entity.Country__r?.Name].filter((item) => item != null),
                    statusIndicator
                };
            });

            if(!this.isDestinationCountryUS) {
                return [
                    {
                        label: 'FGX Importer of Record Service',
                        value: this.internalIOR?.Id,
                        image: `${resources}/images/logo.svg`,
                        subItems:['IOR', 'FGX', 'Licensed Trading Company', this.destinationCountryCode].filter((item) => item != null),
                        additionalSearchableItems:[this.internalIOR.Country__r?.Name].filter((item) => item != null),
                    },
                    ...mappedEntityRecords
                ]
            }
            return mappedEntityRecords;
        }
    }

    get purchasingEntityOptions() {
        let processedEntities = [...this.entityRecords]?.sort((entityA, entityB) => {
            if(
                entityA.Code__c === 'US' && AVAILABLE_STATUSES.includes(entityA.Export_Status__c) &&
                !(entityB.Code__c === 'US' && AVAILABLE_STATUSES.includes(entityB.Export_Status__c))
            ) {
                return -1;
            }

            if(
                !(entityA.Code__c === 'US' && AVAILABLE_STATUSES.includes(entityA.Export_Status__c)) &&
                entityB.Code__c === 'US' && AVAILABLE_STATUSES.includes(entityB.Export_Status__c)
            ) {
                return 1;
            }

            if(
                entityA.Country__c === this.recordData[this.DESTINATION_COUNTRY_FIELD] &&
                entityB.Country__c !== this.recordData[this.DESTINATION_COUNTRY_FIELD]
            ) {
                return -1;
            }

            if(
                entityA.Country__c !== this.recordData[this.DESTINATION_COUNTRY_FIELD] &&
                entityB.Country__c === this.recordData[this.DESTINATION_COUNTRY_FIELD]
            ) {
                return 1;
            }

            return 0;
        });

        if(processedEntities) {
            processedEntities = processedEntities?.filter((entity) => (
                !([this.placeholderEntity?.Id].includes(entity.Id)) &&
                !([this.internalIOR?.Id].includes(entity.Id)) &&
                !([this.internalEOR?.Id].includes(entity.Id))
            ));

            processedEntities = processedEntities?.map((entity) => {
                return {
                    label: entity.Entity_Name__c,
                    value: entity.Id,
                    image: entity.Country__r?.Flag_Icon__c ? `${resources}/images/flags/${entity.Country__r?.Flag_Icon__c}` : null,
                    subItems: [entity.Default_Setting__c ? `Default ${entity.Default_Setting__c}` : 'Entity', entity.Entity_Code__c, entity.Entity_Name__c, entity.Customs_Code__c ? `${this.destinationCountryCustomsCodeLabel}: ${entity.Customs_Code__c}` : null].filter((item) => item != null),
                    additionalSearchableItems:[entity.Country__r?.Name].filter((item) => item != null),
                };
            });
        }

        return processedEntities;
    }

    get billingAccountOptions() {
        return this.accountRecords.map((entity) => {
            return {
                label: entity.Name,
                value: entity.Id
            };
        });
    }

    get purchasingEntityCountry() {
        return this.entityRecords.find(entity => entity.Id === this.recordData[this.QUOTE_PURCHASING_ENTITY_FIELD])?.Country__c;
    }

    get hasSitesAvailable() {
        return this.approvedPendingSiteOptions.length !== 0;
    }

    get singleSiteAlreadySelected() {
        return (this.approvedPendingSiteOptions.length === 1 && !!this.recordData[this.ORIGIN_SITE_FIELD] );
    }

    get originSitesSelectionLabel() {
        return this.hasSitesAvailable ? 'Select from your sites' : 'There are no approved sites for your account.';
    }

    get originSiteSelectionDisabled() {
        return !this.hasSitesAvailable || (this.isUpdateMode && (!!this.cachedSiteId || !!this.cachedCity || !!this.cachedCountryId));
    }

    get originAddressDisabled() {
        return (this.isUpdateMode && (!!this.cachedSiteId || !!this.cachedCity || !!this.cachedCountryId));
    }

    get destinationSitesSelectionLabel() {
        let label = 'Select from your sites';

        if(!this.hasSitesAvailable) {
            label = 'There are no approved sites for your account.';
        } else if(this.singleSiteAlreadySelected) {
            label = 'There are no other approved sites for your account. Please create a destination site under Ecosystem.';
        }
        return label;
    }

    get destinationSiteSelectionDisabled() {
        return (!this.hasSitesAvailable || this.singleSiteAlreadySelected || this.destinationDisabled);
    }

    get bomConfirmMsgWhenBadInput() {
        return BOM_CONFIRM_BAD_INPUT;
    }

    get bomConfirmMsgWhenValueMissing() {
        return BOM_CONFIRM_VALUE_MISSING;
    }

    get showBOMUndo() {
        return this.undoBOMBuffer.length > 0;
    }

    get showPieceUndo() {
        return this.undoPackageLineBuffer.length > 0 && !this.undoPackageLineBuffer.some(element => !element.index);
    }

    get showIntangibleModal() {
        return this.step === 6 && !this.intangibleModalShown && this.totalBOMLineIntangibleValue > 0 && (this.recordData[this.ORIGIN_COUNTRY_FIELD] === this.purchasingEntityCountry)
    }

    get showIOREORConfirmation() {
        if(this.internalEOR) {
            return this.recordData[this.EXPORT_ENTITY_FIELD] === this.internalEOR?.Id || this.recordData[this.IMPORT_ENTITY_FIELD] === this.internalIOR?.Id;
        }
        return false;
    }

    get showCSVImportResults() {
        return this._relatedRecordBOMLineData.length > 0 && this._relatedRecordBOMLineCSVFatalErrors.length === 0 && this.bomCSVImportComplete;
    }

    get showRetryCSVUpload() {
        return this._relatedRecordBOMLineCSVFatalErrors.length > 0 || this.bomCSVImportComplete
    }

    get showRetryAIUpload() {
        return this._relatedRecordBOMLineData.length > 0 && this.bomAIImportComplete;
    }

    get showCSVImportFatalErrors() {
        return this._relatedRecordBOMLineCSVFatalErrors.length > 0 && this.bomCSVImportComplete;
    }

    get showAIImportResults() {
        return this._relatedRecordBOMLineData.length > 0 && this.bomAIImportComplete;
    }

    get showPieceInput() {
        return this.relatedRecordData[this.DIM_WEIGHT_ENTRY_TYPE_FIELD] === 'Provided By Client';
    }

    get showNoEOREntities() {
        return this.exportEntityOptions?.length === 0;
    }

    get showNoIOREntities() {
        return this.importEntityOptions?.length === 0;
    }

    get eorTip() {
        return `Using your own ${this.originCountry?.Name} entity can save you on export costs.`;
    }

    get iorTip() {
        return `Using your own ${this.destinationCountry?.Name} entity can save you on import costs and enable tax reclamation.`;
    }

    get totalBOMLineIntangibleValue() {
        return this._relatedRecordBOMLineData
            .filter((line)=> ['Software/License', 'Warranty/Support','Other Intangible'].includes(line[SCHEMA.BOM_LINE_PRODUCT_TYPE.fieldApiName]))
            .reduce(
                (subTotal, bomLine) => {
                    return subTotal + (
                        (bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] : 0)  *
                        (bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] : 0)
                    )
                },0
            );
    }

    get isAdvancedLogisticsMode() {
        if(this.user?.Account) {
            return this.user.Account[SCHEMA.ACCOUNT_ADV_LOGISTICS.fieldApiName] === ENABLED;
        }
        return false;
    }

    get isAdvancedCommercialization() {
        if(this.user?.Account) {
            return this.user.Account[SCHEMA.ACCOUNT_ADV_COMMERCIALIZATION.fieldApiName] === ENABLED;
        }
        return false;
    }

    get isAdvancedBilling() {
        if(this.user?.Account) {
            return this.user.Account[SCHEMA.ACCOUNT_ADV_BILLING.fieldApiName] === ENABLED;
        }
        return false;
    }

    get isNewMode() {
        return this.variant === VARIANT.NEW;
    }

    get isUpdateMode() {
        return this.variant === VARIANT.UPDATE;
    }

    get isModifyMode() {
        return this.variant === VARIANT.MODIFY;
    }

    get isCloneMode() {
        return this.variant === VARIANT.CLONE;
    }

    get today(){
        return new Date().toISOString().split('T')[0];
    }

    get showDIMWeightEntry() {
        return (!['Bare Metal','I Don\'t Know'].includes(this.relatedRecordData[this.PACKING_CONFIG_FIELD]) &&
            !isEmptyString(this.relatedRecordData[this.PACKING_CONFIG_FIELD]));
    }

    get showTargetDeliveryDate() {
        return this.deadlineSelect;
    }

    get csvBOMTemplateDownloadURL() {
        if(this.isAdvancedLogisticsMode) {
            return `${resources}/templates/BOM_Upload_Template_AdvancedLogistics.xlsx`;
        } else {
            return `${resources}/templates/BOM_Upload_Template_Standard.xlsx`;
        }
    }

    get originDestinationDisabled() {
        return false;
    }

    get destinationDisabled() {
        return this.isUpdateMode;
    }

    /********* Commercialization ********
     * below i have conditional checks for a number of
     * getters that return the same thing. i am doing
     * this in anticipation of changes coming that will
     * necessitate differences in the values
     */

    get commercializationHeader() {
        if(!this.isAdvancedCommercialization) {
            return 'What is the hardware purchasing & transaction scenario?';
        } else {
            return 'What is the hardware purchasing & transaction scenario?';
        }
    }

    get commercializationSubtitle() {
        if(!this.isAdvancedCommercialization) {
            return 'Although you aren\'t buying any equipment from FGX, to get ahead of any potential issues, we\'ll need to confirm how the hardware was (or will be) purchased and whether there are any asset transfer requirements.';
        } else {
            return 'Although you aren\'t buying any equipment from FGX, to get ahead of any potential issues, we\'ll need to confirm which of your entities paid for or will pay for the hardware and will ultimately own it.';
        }
    }

    get commercializationCheckboxTxt() {
        if(!this.isAdvancedCommercialization) {
            return 'I understand that if I have or plan to use a foreign entity to purchase the equipment for this shipment, I may have problems remitting payment to the hardware vendor or settling inter-company accounts.';
        } else {
            return 'I understand that if I have or plan to use a foreign entity to purchase the equipment for this shipment, I may have problems remitting payment to the hardware vendor or settling inter-company accounts. FGX will follow-up based on the responses above.';
        }
    }

    /** Commercialization Q1 **/
    get commercializationQuestion1Label() {
        if(!this.isAdvancedCommercialization) {
            return 'Which of these scenarios best describe the hardware in this shipment?';
        } else {
            return 'Which of these scenarios best describe the hardware in this shipment?';
        }
    }

    get commercializationQuestion1Req() {
        if(!this.isAdvancedCommercialization) {
            return true;
        } else {
            return true;
        }
    }

    get commercializationQuestion1Options() {
        return [
            {label: 'I have already purchased the hardware', value: 'Hardware Already Purchased' },
            {label: 'I haven\'t purchased the hardware but plan to', value: 'Hardware Not Yet Purchased'},
            {label: 'I am leasing or financing the hardware', value: 'Hardware Will Be Leased/Financed'},
            {label: 'I don\'t know yet', value: CUSTOMER_DOESNT_KNOW}
        ];
    }

    /** Commercialization Q2 **/

    get commercializationQuestion2Icon() {
        if(!this.isAdvancedCommercialization) {
            return this.bizIcon;
        } else {
            return this.documentsIcon;
        }
    }

    get commercializationQuestion2Label() {
        if(!this.isAdvancedCommercialization) {
            return `Does your company have a related business entity in ${this.destinationCountry?.Name}?`;
        } else {
            let transactionType = this.relatedRecordData[this.QUOTE_VERSION_TRANSACTION_TYPE_FIELD];

            switch(transactionType) {
                case 'Hardware Already Purchased':
                    return 'Which entity has purchased the hardware?';
                case 'Hardware Not Yet Purchased':
                    return 'Which entity will purchase the hardware?';
                case 'Hardware Will Be Leased/Financed':
                    return 'Which entity will be leasing/financing the hardware?';

            }
        }
    }

    get commercializationQuestion2Visible() {
        if(!this.isAdvancedCommercialization) {
            return true;
        } else {
            return this.relatedRecordData[this.QUOTE_VERSION_TRANSACTION_TYPE_FIELD] !== CUSTOMER_DOESNT_KNOW &&
                this.relatedRecordData[this.QUOTE_VERSION_TRANSACTION_TYPE_FIELD];
        }
    }

    get commercializationQuestion2Req() {
        if(!this.isAdvancedCommercialization) {
            return true;
        } else {
            return this.relatedRecordData[this.QUOTE_VERSION_TRANSACTION_TYPE_FIELD] !== CUSTOMER_DOESNT_KNOW &&
                !isEmptyString(this.relatedRecordData[this.QUOTE_VERSION_TRANSACTION_TYPE_FIELD]);
        }
    }

    get commercializationQuestion2Options() {
        if(!this.isAdvancedCommercialization) {
            return [
                {label: 'Yes', value: 'Customer Has Related Entity' },
                {label: 'No', value: 'Customer Doesn\'t Have Related Entity'},
                {label: 'I don\'t know', value: CUSTOMER_DOESNT_KNOW}
            ];
        } else {
            return [
                {
                    label: 'I don\'t know',
                    value: this.placeholderEntity?.Id,
                    image: `${resources}/images/question.svg`,
                    subItems: ['TBD', 'FGX will follow-up accordingly']
                },
                ...this.purchasingEntityOptions
            ];
        }
    }

    /** Commercialization Q3 **/

    get commercializationQuestion3Icon() {
        if(!this.isAdvancedCommercialization) {
            return this.balanceSheetIcon;
        } else {
            return this.documentsIcon;
        }
    }

    get commercializationQuestion3Label() {
        if(!this.isAdvancedCommercialization) {
            return `Will the hardware assets be transferred onto the balance sheet of the local ${this.destinationCountry?.Name} entity?`;
        } else {
            return 'Which entity do you want to ultimately own the assets?';
        }
    }

    get commercializationQuestion3Req() {
        if(!this.isAdvancedCommercialization) {
            return true;
        } else {
            return true;
        }
    }

    get commercializationQuestion3Visible() {
        if(!this.isAdvancedCommercialization) {
            return this.relatedRecordData[this.QUOTE_VERSION_RELATED_ENTITY_FIELD] === 'Customer Has Related Entity';
        } else {
            return true;
        }
    }

    get commercializationQuestion3Options() {
        if(!this.isAdvancedCommercialization) {
            return [
                {label: 'Yes', value: 'Customer Wants to Transfer Assets' },
                {label: 'No', value: 'Customer Doesn\'t Want to Transfer Assets'},
                {label: 'I don\'t know', value: CUSTOMER_DOESNT_KNOW}
            ];
        } else {
            return [
                {
                    label: 'I don\'t know',
                    value: this.placeholderEntity?.Id,
                    image: `${resources}/images/question.svg`,
                    subItems: ['TBD', 'FGX will follow-up accordingly']
                },
                ...this.purchasingEntityOptions
            ];
        }
    }

    get commercializationQuestion3TooltipTxt() {
        return `For example will the local entity in ${this.destinationCountry?.Name} need to depreciate the hardware assets, or show ownership on the balance sheet?`;
    }


    /** Commercialization Q4 **/
    get commercializationQuestion4Label() {
        if(!this.isAdvancedCommercialization) {
            if(this.relatedRecordData[this.QUOTE_VERSION_RELATED_ENTITY_FIELD] === 'Customer Has Related Entity') {
                return `Will the ${this.destinationCountry?.Name} business entity need to transfer funds to pay for the hardware assets being shipped?`;
            } else {
                return `Will the ${this.destinationCountry?.Name} recipient need to transfer funds to pay for the hardware assets being shipped?`;
            }
        } else {

        }
    }

    get commercializationQuestion4Req() {
        if(!this.isAdvancedCommercialization) {
            return true;
        } else {
            return false;
        }
    }

    get commercializationQuestion4Visible() {
        if(!this.isAdvancedCommercialization) {
            return true;
        } else {
            return false;
        }
    }

    get commercializationQuestion4Options() {
        return [
            {label: 'Yes', value: 'Customer Wants to Transfer Funds' },
            {label: 'No', value: 'Customer Doesn\'t Want to Transfer Funds'},
            {label: 'I don\'t know', value: CUSTOMER_DOESNT_KNOW}
        ];
    }

    get commercializationQuestion4TooltipTxt() {
        if(!this.isAdvancedCommercialization) {
            if(this.relatedRecordData[this.QUOTE_VERSION_RELATED_ENTITY_FIELD] === 'Customer Has Related Entity') {
                return `This typically includes paying a vendor in another country or settling an inter-company asset transfer with headquarters.`;
            } else {
                return `In other words: as you don't have a business entity in ${this.destinationCountry?.Name}, will the recipient whether its a vendor or customer need to pay you back (or someone else) for the hardware in this shipment?`;
            }
        }
    }

    get postSubmitScreenHeader() {
        return this.isUpdateMode ? 'Thanks for updating your quote request!' : 'We\'ve successfully received your quote request!';
    }

    get postSubmitScreenSubheader() {
        return this.isUpdateMode ? 'We\'ll evaluate the updated details of your request and get back to you as soon as possible with a revised quote.' : 'The details you\'ve submitted will be thoroughly analyzed by our operations experts to ensure you have cost-optimized and accurate costs.';
    }

    get postSubmitQuoteNumber() {
        return this.isUpdateMode ? this.relatedRecordData.Name : this.recordData.Name;
    }

    get emptyArray(){
        return [];
    }

    @api getTotalSteps() {
        if(this._stepTemplates) {
            let filteredSteps = this._stepTemplates.filter((step) => (!step.finalStep && !step.submitStep) || (step.submitStep && step.finalStep) || (step.submitStep && !step.finalStep));
            return filteredSteps.length;
        }
        return 0;
    }

    @api getCurrentStep() {
        if(this._stepTemplates) {
            return this._stepTemplates[this.step];
        }
        return null;
    }

    @api getRecordData() {
        return this.recordData;
    }

    // BOM Lines
    @api getRelatedBOMLineRecords() {
        return this._relatedRecordBOMLineData.map((item) => ({
            ...item,
            [SCHEMA.BOM_LINE_CATEGORY.fieldApiName]: isEmptyString(item[SCHEMA.BOM_LINE_CATEGORY.fieldApiName]) ? 'Hardware' : item[SCHEMA.BOM_LINE_CATEGORY.fieldApiName]
        }));
    }

    @api setRelatedBOMLineRecords(records) {
        this._relatedRecordBOMLineData = [...records];
    }

    @api getRelatedBOMLinesToDelete() {
        return this.undoBOMBuffer.reduce((arr, line) => {
            if(line.item.Id) {
                arr.push(line.item);
            }
            return arr;
        }, [])
    }

    @api clearBOMLineDeleteBuffer() {
        this.undoBOMBuffer = [];
    }

    // Package Lines
    @api getRelatedPieceRecords() {
        return this._relatedRecordPieceLineData;
    }

    @api setRelatedPieceRecords(records) {
        this._relatedRecordPieceLineData = [...records];
    }

    @api getRelatedPiecesToDelete() {
        return this.undoPackageLineBuffer.reduce((arr, line) => {
            if(line.item.Id) {
                arr.push(line.item);
            }
            return arr;
        }, [])
    }

    @api clearPackageLineDeleteBuffer() {
        this.undoPackageLineBuffer = [];
    }

    // Quote Lines
    @api getRelatedQuoteLineRecords() {
        return this._relatedRecordQuoteLineData;
    }

    @api setRelatedQuoteLineRecords(records) {
        this._relatedRecordQuoteLineData = [...records];
    }

    @api getShipmentValues() {
        return {
            shipmentValue: this.totalBOMLineValue,
            hardwareValue: this.totalBOMHardwareValue,
            softwareValue: this.totalBOMSoftwareValue
        };
    }

    @api getShortDescription() {
        const groupBy = [SCHEMA.BOM_LINE_MANUFACTURER.fieldApiName, SCHEMA.BOM_LINE_PRODUCT_TYPE.fieldApiName];
        const groupedData = this._relatedRecordBOMLineData
            .filter((line)=> !(['Software/License', 'Warranty/Support','Other Intangible'].includes(line[groupBy[1]])))
            .reduce((acc, item) => {
                const key = `${item[groupBy[0]]}-${item[groupBy[1]]}`;
                if (!acc[key]) {
                    acc[key] = { ...item, totalValue: 0 };
                }
                acc[key].totalValue += item[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] * item[SCHEMA.BOM_LINE_QUANTITY.fieldApiName];
                return acc;
            }, {});

        const groupedArray = Object.values(groupedData);
        const maxGroup = groupedArray.reduce((max, item) => {
            return item.totalValue > max.totalValue ? item : max;
        }, { totalValue: 0 });

        let a = (maxGroup[groupBy[0]] || maxGroup[groupBy[1]]) ? [maxGroup[groupBy[0]], maxGroup[groupBy[1]]] : [];
        let b = [this.destinationCity, this.destinationCountryName]
        b = b.filter(item => item); // filter out empty strings (in case city is blank. avoids issue of Shipment to , COUNTRY NAME)

        let ab = [];
        if(a.length > 0)
            ab.push(a.join(' '));
        if(b.length > 0)
            ab.push(`Shipment to ${b.join(', ')}`);

        return ab.join(' ');
    }

    @api
    uploadedCachedFiles(relatedRecordId) {
        if(this.template.querySelector('c-fgx-file-upload-data')) {
            this.template.querySelector('c-fgx-file-upload-data').relatedRecordId = relatedRecordId;
        }
    }

    async connectedCallback() {
        this.subscribeToMessageChannel();
        this.revokeApprovedFields = [];
        this.statusFieldByStep = [
            {[SCHEMA.QUOTE_STATUS.fieldApiName]: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}
        ];

        this.allowAutoStatusUpdates = false;
        this.user = await getUserRecord();
        this.currencyData = await getCurrencyData();

        Promise.all([loadScript(this, coreJS), loadScript(this, pdfJS), loadScript(this, pdfWORKER)]).then(()=>{
            pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWORKER;
        }).catch((error)=>{
        })

        this.notifyStepChange();
    }

    renderedCallback() {
        // refreshing the wire function for the
        // current record on page rerender
        this.refreshRecord();
        this.setupStepDefaults();
    }

    disconnectedCallback() {
        this.unsubscribeToMessageChannel();
    }

    subscribeToMessageChannel() {
        if (!this.subscription) {
            this.subscription = subscribe(
                this.messageContext,
                action,
                (message) => this.handleActionEvents(message),
                { scope: APPLICATION_SCOPE }
            );
        }
    }

    unsubscribeToMessageChannel() {
        unsubscribe(this.subscription);
        this.subscription = null;
    }

    render() {
        // added this to support the new blocking of rendering until the component
        // has loaded and gotten to the correct step. this render event is usually called
        // in the wire function on the stepperChild but this is here in case this isn't a
        // continue flow with an existing record
        if(!this.recordId) {
            this.dispatchEvent( new CustomEvent( EVENTS.RENDERED_EVT, { bubbles: true, composed: true }));
        }

        return this.stepTemplates[this.step].template;
    }

    /**
     * @description helper function to set a local
     * cache of the origin/destination fields. since
     * we change the value in recordData we need to cache
     * it on the wire so we can do a compare in certain situations
     */
    cacheCurrentOriginDestination() {
        this.originDestinationCache = {
            [this.ORIGIN_SITE_FIELD]: this.recordData[this.ORIGIN_SITE_FIELD],
            [this.ORIGIN_COUNTRY_FIELD]: this.recordData[this.ORIGIN_COUNTRY_FIELD],
            [this.ORIGIN_CITY_FIELD]: this.recordData[this.ORIGIN_CITY_FIELD],
            [this.DESTINATION_SITE_FIELD]: this.recordData[this.DESTINATION_SITE_FIELD],
            [this.DESTINATION_COUNTRY_FIELD]: this.recordData[this.DESTINATION_COUNTRY_FIELD],
            [this.DESTINATION_CITY_FIELD]: this.recordData[this.DESTINATION_CITY_FIELD]
        }
    }

    /**
     * @description wire function to keep the
     * latest data in the stepper _data attribute.
     * this handles preloading records as well as
     * a workaround for notifying the parent stepper
     * that the child has rendered
     * @param result
     */
    @wire(getData, {recordId:'$recordId', relatedRecordId: '$_relatedRecordId'})
    stepperRecord(result) {
        if (result.data) {
            this.wireRecordData = result;
            this._data = {...result.data.quote};

            /** we don't want to risk the wire function overwriting anything currently in the bom editor **/
            if((this.step !== 5 && this.step !== 6) || !this.initialLoadComplete)  {
                this._relatedRecordData = Object.assign({}, result.data.activeQuoteVersion);
                this._relatedRecordBOMLineData = Object.assign([],[...result.data.bomLines]);
            }
            if(this.step !== 7 || !this.initialLoadComplete) {
                this._relatedRecordPieceLineData = Object.assign([],[...result.data.pieceLines]);
            }

            this._relatedRecordQuoteLineData = Object.assign([],[...result.data.quoteLines]);
            this.relatedRecordId = result.data.activeQuoteVersion?.Id;

            if(!this.step && !this.initialLoadComplete && this.isNewMode) {
                this.step = result.data.activeQuoteVersion[CONSTANTS.FLOW_STEP_FIELD];
            }

            if((this.isModifyMode || this.isCloneMode || this.isUpdateMode) && !this.modifyingCloningRecord) {
                this.modifyingCloningRecord = this._relatedRecordData;
            }

            if(!this.initialLoadComplete) {
                if(this.relatedRecordData[this.QUOTE_VERSION_TARGET_DELIVERY_DATE_FIELD] || this.recordData[this.TARGET_DELIVERY_DATE_FIELD]) {
                    this.deadlineSelect = true;
                } else if(this.step === 9){
                   this.deadlineSelect = false;
                }
            }

            this.notifyRecordChange();
            this.cacheCurrentOriginDestination();

            this.initialLoadComplete = true;

        } else if (result.error) {
            const event = new ShowToastEvent({
                label: 'Error',
                message: 'Error retrieving record: ' + result.error,
                variant: 'error',
                mode: 'dismissible'
            });
            this.dispatchEvent(event);
        }

        /**
         * this lets stepper parent know that we have rendered without
         * requiring a renderedCallback function inside every component
         * passed into the stepper. this fixes the flashing of an incorrect
         * step before the component has completely loaded
         */
        this.dispatchEvent( new CustomEvent( EVENTS.RENDERED_EVT, { bubbles: true, composed: true }));
    }

    @wire(getSites)
    wiredSites(result) {
        let data = result.data;
        let error = result.error;
        if (data) {
            this.siteWire = result;
            this.siteRecords = data;
        } else if (error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'Problem retrieving sites',
                    variant: 'error',
                })
            );
        }
    }

    @wire(getEntities)
    wiredEntities(result) {
        let data = result.data;
        let error = result.error;
        if (data) {
            this.entityWire = result;
            this.entityRecords = data
        } else if (error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'Problem retrieving entities',
                    variant: 'error',
                })
            );
        }
    }

    @wire(getInternalEOR)
    wiredEOR(result) {
        let data = result.data;
        let error = result.error;
        if (data) {
            this.internalEOR = data
        } else if (error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'Problem retrieving static entities',
                    variant: 'error',
                })
            );
        }
    }

    @wire(getInternalIOR)
    wiredIOR(result) {
        let data = result.data;
        let error = result.error;
        if (data) {
            this.internalIOR = data
        } else if (error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'Problem retrieving static entities',
                    variant: 'error',
                })
            );
        }
    }

    @wire(getPlaceholderEntity)
    wiredPlaceholderEntity(result) {
        let data = result.data;
        let error = result.error;
        if (data) {
            this.placeholderEntity = data
        } else if (error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'Problem retrieving static entities',
                    variant: 'error',
                })
            );
        }
    }

    @wire(getCountries)
    wiredCountries({ error, data }) {
        if (data) {
            this.countryRecords = data;
            this.countryOptions = data.map((country) => ({
                label: country.Name,
                value: country.Id,
                flagIcon: country.Flag_Icon__c,
                taxIdLabel: country.TaxID_Label__c,
                customsCodeLabel: country.CustomsCode_Label__c,
                additionalSearchableItems:[country.Code__c].filter((item) => item != null),
            }));

        } else if (error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'Problem retrieving countries',
                    variant: 'error',
                })
            );
        }
    }

    @wire(getAccounts)
    wiredAccounts(result) {
        let data = result.data;
        let error = result.error;
        if (data) {
            this.accountWire = result;
            this.accountRecords = data
        } else if (error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'Problem retrieving related accounts',
                    variant: 'error',
                })
            );
        }
    }

    @wire(getPicklistValues, {
        recordTypeId: "$objectInfo.data.defaultRecordTypeId",
        fieldApiName: SCHEMA.DESTINATION_STATE_US,
    })
    destinationStateUSPicklistValues;

    @wire(getPicklistValues, {
        recordTypeId: "$objectInfo.data.defaultRecordTypeId",
        fieldApiName: SCHEMA.ORIGIN_STATE_US,
    })
    originStateUSPicklistValues;

    /**
     * @description handles all form input on any
     * of the templates included in the component
     * extending this class. all onchange handlers
     * in the templates should point to this function.
     * @param event
     */
    handleFormInputChange(event) {
        let target = event.target.dataset.field;
        let object = event.target.dataset.object;
        let action = event.target.dataset.action; // denotes that this is not a field but rather an action
        let checked = event.target.checked;
        let value = event.target.value;
        let data = this.objectToRecord[object];

        if(action) {
            this.handleAction(event);
            return;
        }

        data[target] = event.currentTarget.type === 'toggle' ? checked : value;
        event.currentTarget.reportValidity();

        this.notifyRecordChange();
    }

    /**
     * @description: Handles the event that fires from
     * the create site component notifying that it's done
     * and we need to set the site we just received
     * @param event
     */
    handleCreateSiteFinished(event) {
        if(event.detail.recordData?.Id) {
            let recordId  = event.detail.recordData.Id;
            let field = event.target.dataset.field;

            if(!field) {
                this.createSiteId = recordId;
            }

            this.sendSiteEmail(recordId);

            refreshApex(this.siteWire).then(() => {
                if(field) {
                    this.recordData[field] = recordId;
                    this.notifyRecordChange();
                }
            })
        }
    }

    /**
     * @description: Handles the event that fires from
     * the compliance site component notifying that it's done
     * and the user wants to take the survey
     * @param event
     */
    handleCreateSiteContinue(event){
        event.stopPropagation();
        this.createSiteId = event.detail.recordData.Id;
        this.showSiteSurvey = true;
    }

    /**
     * @description Handles the completion of
     * the create site stepper
     * @param event
     */
    handleCreateSiteContinueStepperClose(event) {
        event.stopPropagation();
        this.createSiteId = null;
        this.showSiteSurvey = false;
    }

    /**
     * @description: Handles the event that fires from the
     * create entity component notifying that it's done and we
     * need to set the entity we just received.
     * @param event
     */
    handleCreateEntityFinished(event) {
        if(event.detail.recordData?.Id) {
            refreshApex(this.entityWire);
        }
    }

    /**
     * @description: Handles returning to the CSV upload
     * screen when the user is currently in the upload results state
     */
    handleReturnToUpload() {
        this.bomCSVImportComplete = false;
        this.bomAIImportComplete = false;
        this.showFileUploadWarning = true;
    }

    /**
     * @description: Handles downloading the correct BOM input CSV template
     */
    handleDownloadCSVUploadTemplate(){
        window.open(this.csvBOMTemplateDownloadURL, "_blank");
    }

    /************************************************
     * Override functions for handleFormInputChange
     ************************************************/

    /**
     * @description Handler for the origin address
     * being set, meaning we need to clear any site
     * record that may have been set before passing to
     * the regular change handler
     * @param event
     */
    handleOriginAddressChange(event) {
        this.handleFormInputChange(event);

        this.recordData[this.ORIGIN_SITE_FIELD] = null;
        this.recordData[this.ORIGIN_ADDRESS_LINE_1_FIELD] = '';
        this.recordData[this.ORIGIN_ADDRESS_LINE_2_FIELD] = '';
        this.recordData[this.ORIGIN_ADDRESS_LINE_3_FIELD] = '';
        this.recordData[this.ORIGIN_POST_CODE_FIELD] = '';
        this.recordData[this.ORIGIN_STATE_FIELD] = '';
        this.recordData[this.ORIGIN_COUNTY_PROVINCE_OTHER_FIELD] = '';
        this.recordData[this.ORIGIN_COMPANY_NAME_FIELD] = '';
        this.recordData[this.ORIGIN_SEARCH_CHOICE_FIELD] = 'Google';

        this.notifyRecordChange();
    }

    /**
     * @description Handler for the destination address
     * being set, meaning we need to clear any site
     * record that may have been set before passing to
     * the regular change handler
     * @param event
     */
    handleDestinationAddressChange(event) {
        this.handleFormInputChange(event);

        this.recordData[this.DESTINATION_SITE_FIELD] = null;
        this.recordData[this.DESTINATION_ADDRESS_LINE_1_FIELD] = '';
        this.recordData[this.DESTINATION_ADDRESS_LINE_2_FIELD] = '';
        this.recordData[this.DESTINATION_ADDRESS_LINE_3_FIELD] = '';
        this.recordData[this.DESTINATION_POST_CODE_FIELD] = '';
        this.recordData[this.DESTINATION_STATE_FIELD] = '';
        this.recordData[this.DESTINATION_COUNTY_PROVINCE_OTHER_FIELD] = '';
        this.recordData[this.DESTINATION_COMPANY_NAME_FIELD] = '';
        this.recordData[this.DESTINATION_SEARCH_CHOICE_FIELD] = 'Google';

        this.notifyRecordChange();
    }

    /**
     * @description Handler for the origin site
     * being set, meaning we need to clear any origin address
     * that may have been set before passing to
     * the regular change handler
     * @param event
     */
    handleOriginSiteChange(event) {
        this.handleFormInputChange(event);
        let site = this.siteRecords.find((item) => (this.recordData[this.ORIGIN_SITE_FIELD] === item.Id));

        this.recordData[this.ORIGIN_COUNTRY_FIELD] = site ? site[SCHEMA.SITE_COUNTRY.fieldApiName]: null;
        this.recordData[this.ORIGIN_ADDRESS_LINE_1_FIELD] = '';
        this.recordData[this.ORIGIN_ADDRESS_LINE_2_FIELD] = '';
        this.recordData[this.ORIGIN_ADDRESS_LINE_3_FIELD] = '';
        this.recordData[this.ORIGIN_POST_CODE_FIELD] = '';
        this.recordData[this.ORIGIN_CITY_FIELD] = site ? site[SCHEMA.SITE_CITY.fieldApiName] : '';
        this.recordData[this.ORIGIN_STATE_FIELD] = '';
        this.recordData[this.ORIGIN_STATE_US_FIELD] = '';
        this.recordData[this.ORIGIN_COUNTY_PROVINCE_OTHER_FIELD] = '';
        this.recordData[this.ORIGIN_COMPANY_NAME_FIELD] = '';
        this.recordData[this.ORIGIN_SEARCH_CHOICE_FIELD] = 'Site';

        this.notifyRecordChange();
    }

    /**
     * @description Handler for the destination site
     * being set, meaning we need to clear any origin address
     * that may have been set before passing to
     * the regular change handler
     * @param event
     */
    handleDestinationSiteChange(event) {
        this.handleFormInputChange(event);
        let site = this.siteRecords.find((item) => (this.recordData[this.DESTINATION_SITE_FIELD] === item.Id));

        this.recordData[this.DESTINATION_COUNTRY_FIELD] = site ? site[SCHEMA.SITE_COUNTRY.fieldApiName] : null;
        this.recordData[this.DESTINATION_ADDRESS_LINE_1_FIELD] = '';
        this.recordData[this.DESTINATION_ADDRESS_LINE_2_FIELD] = '';
        this.recordData[this.DESTINATION_ADDRESS_LINE_3_FIELD] = '';
        this.recordData[this.DESTINATION_POST_CODE_FIELD] = '';
        this.recordData[this.DESTINATION_CITY_FIELD] = site ? site[SCHEMA.SITE_CITY.fieldApiName] : '';
        this.recordData[this.DESTINATION_STATE_FIELD] = '';
        this.recordData[this.DESTINATION_STATE_US_FIELD] = '';
        this.recordData[this.DESTINATION_COUNTY_PROVINCE_OTHER_FIELD] = '';
        this.recordData[this.DESTINATION_COMPANY_NAME_FIELD] = '';
        this.recordData[this.DESTINATION_SEARCH_CHOICE_FIELD] = '';

        this.notifyRecordChange();
    }

    /**
     * @description Handler for the EOR being set,
     * so that if we are in update mode we can clear
     * the confirmations for FGX EOR/EOR before passing to
     * the regular change handler
     * @param event
     */
    handleEntityChange(event) {
        this.handleFormInputChange(event);

        if(this.isUpdateMode && this.showIOREORConfirmation) {
            this.reclaimableTaxConfirmation = false;
            this.thirdPartyConfirmation = false;
            this.monetaryPaymentsConfirmation = false;
        }
    }

    /**
     * @description: change handler override for Q1
     * of the commercialization screen. when in advanced
     * commercialization mode, this lets us clear out the
     * Purchasing Entity field when the user selects "I don't know"
     * @param event
     */
    handleCommercializationQ1Change(event) {
        if(this.isAdvancedCommercialization) {

            if(event.target.value === CUSTOMER_DOESNT_KNOW) {
                this.objectToRecord[this.QUOTE_VERSION_OBJ][this.QUOTE_VERSION_PURCHASING_ENTITY_FIELD] = null;
                this.objectToRecord[this.QUOTE_OBJ][this.QUOTE_PURCHASING_ENTITY_FIELD] = null;
            }
        }
        this.handleFormInputTwinFieldChange(event);
    }

    /**
     * @description: change handler override for Q2
     * of the commercialization screen. when NOT in advanced
     * commercialization mode, this lets us clear out the
     * Funds Transfer field when the user selects "I don't know"
     * @param event
     */
    handleCommercializationQ2Change(event) {
        if(!this.isAdvancedCommercialization) {
            this.objectToRecord[this.QUOTE_VERSION_OBJ][this.QUOTE_VERSION_FUNDS_TRANSFER_FIELD] = null;
            this.objectToRecord[this.QUOTE_OBJ][this.QUOTE_FUNDS_TRANSFER_FIELD] = null;

            if(event.target.value !== 'Customer Wants to Transfer Assets') {
                this.objectToRecord[this.QUOTE_VERSION_OBJ][this.QUOTE_VERSION_ASSET_TRANSFER_FIELD] = null;
                this.objectToRecord[this.QUOTE_OBJ][this.QUOTE_ASSET_TRANSFER_FIELD] = null;
            }
        }
        this.handleFormInputTwinFieldChange(event);
    }

    /**
     * @description: main change handler for the inputs
     * on the commercialization screen. since we have to
     * set fields on both the quote and quote version this
     * makes it cleaner to call each time with dataset values
     * @param event
     */
    handleFormInputTwinFieldChange(event) {
        this.handleFormInputChange(event);
        if(!this.isUpdateMode) {
            let target = event.target.dataset.twinField;
            this.objectToRecord[this.QUOTE_OBJ][target] = event.target.value;
            this.notifyRecordChange();
        }
    }

    /**
     * @description Handles currency change event.
     * @param {Object} event - The currency change event object.
     */
    handleCurrencyChange(event) {
        let currencyIsoCode = event.target.value;

        this._relatedRecordBOMLineData = this._relatedRecordBOMLineData.map((item) => ({
            ...item,
            [SCHEMA.BOM_LINE_CURR_CODE.fieldApiName]: currencyIsoCode
        }));
        this.objectToRecord[this.QUOTE_VERSION_OBJ][SCHEMA.QUOTE_VERSION_BOM_CURR_CODE.fieldApiName] = currencyIsoCode;
        this.objectToRecord[this.QUOTE_VERSION_OBJ][SCHEMA.QUOTE_VERSION_CURR_CODE.fieldApiName] = currencyIsoCode;

        if(currencyIsoCode !== 'USD') {
            this.objectToRecord[this.QUOTE_VERSION_OBJ][SCHEMA.QUOTE_VERSION_CURR_RATE.fieldApiName] = this.currencyData.currencyRateMap[currencyIsoCode];
            this.objectToRecord[this.QUOTE_VERSION_OBJ][SCHEMA.QUOTE_VERSION_CONVERSION_DATE.fieldApiName] = new Date().toISOString();
        } else {
            this.objectToRecord[this.QUOTE_VERSION_OBJ][SCHEMA.QUOTE_VERSION_CURR_RATE.fieldApiName] = 0;
            this.objectToRecord[this.QUOTE_VERSION_OBJ][SCHEMA.QUOTE_VERSION_CONVERSION_DATE.fieldApiName] = null;

        }
        this.notifyRecordChange();
    }

    /**
     * @description: Handles Client Ref changes since we need
     * to do some special logic around this after calling handleFormInputChange
     * @param event
     */
    handleClientRefChange(event) {
        this.handleFormInputChange(event);
        let value = event.target.value;
        let field = event.target.dataset.field;

        if(this.isNewMode || this.isCloneMode) {

            const fieldMap = {
                [this.VERSION_CLIENT_REF_FIELD]: this.CLIENT_REF_FIELD,
                [this.VERSION_CLIENT_REF2_FIELD]: this.CLIENT_REF2_FIELD
            }
            this.objectToRecord[this.QUOTE_OBJ][fieldMap[field]] = value;
        }
        this.notifyRecordChange();
    }

    /**
     * @description Handles the weight unit selection
     * event, so we can update all related line items weight units
     * @param event
     */
    handleWeightSelection(event) {
        this.weightUnitSelection = event.target.value;
        this._relatedRecordPieceLineData = this._relatedRecordPieceLineData.map((item) => ({
            ...item,
            [SCHEMA.PACKAGE_LINE_WEIGHT_UNIT.fieldApiName]: this.weightUnitSelection
        }));
    }

    /**
     * @description Handles the dimension unit selection
     * event, so we can update all related line items dimensional units
     * @param event
     */
    handleDimensionSelection(event) {
        this.dimensionUnitSelection = event.target.value;
        this._relatedRecordPieceLineData = this._relatedRecordPieceLineData.map((item) => ({
            ...item,
            [SCHEMA.PACKAGE_LINE_LENGTH_UNIT.fieldApiName]: this.dimensionUnitSelection
        }));
    }

    /**
     * @description Handles the
     */
    handleDimWeightEntryTypeChange(event) {
        this.handleFormInputTwinFieldChange(event);

        if(event.target.value === 'Estimate For Client') {
            this._relatedRecordPieceLineData = [];
        } else {
            this._relatedRecordPieceLineData = [{
                [CONSTANTS.SOBJECTTYPE]: SCHEMA.PACKAGE_LINE_OBJ.objectApiName,
                [SCHEMA.PACKAGE_LINE_QUOTE_VERSION.fieldApiName]: this.relatedRecordId,
                [SCHEMA.PACKAGE_LINE_WEIGHT_UNIT.fieldApiName]: 'lb',
                [SCHEMA.PACKAGE_LINE_LENGTH_UNIT.fieldApiName]: 'in'
            }];
        }
    }

    /**
     * @description Handles maintaining the variables
     * that drive the confirmation checkboxes on some of the
     * steps
     * @param event
     */
    handleConfirmationChange(event) {
        this[event.target.dataset.attr] = event.target.checked;
    }

    /**
     * @description Handles maintaining the variables that
     * drive the hide/show of the Target Delivery date
     * @param event
     */
    handleDeadlineSelect(event) {
        this[event.target.dataset.attr] = event.target.value;
        if(event.target.value === false || !event.target.value) {
            this.objectToRecord[this.QUOTE_OBJ][this.TARGET_DELIVERY_DATE_FIELD] = null;
            this.objectToRecord[this.QUOTE_VERSION_OBJ][this.QUOTE_VERSION_TARGET_DELIVERY_DATE_FIELD] = null;
            this.objectToRecord[this.QUOTE_OBJ][this.REQUESTED_SERVICE_LEVEL_FIELD] = CONSTANTS.SERVICE_LEVEL_ECONOMY;
            this.objectToRecord[this.QUOTE_VERSION_OBJ][this.QUOTE_VERSION_SERVICE_LEVEL_FIELD] = CONSTANTS.SERVICE_LEVEL_ECONOMY;;
            this.notifyRecordChange();
        }
    }

    /********************************************************
     * Related Line (BOM Line & Package Line) Event Handlers
     *******************************************************/

    /**
     * @description handles listening to and acting on
     * the bomInputItem relatedrecordchange event so that we
     * can keep track of quote line updates at this parent level.
     * also runs a validity check on either the expanded bom input or
     * collapsed bom input (whichever is open) and the row that was edited
     * is now valid, we remove the CSV parsing errors from it.
     * @param event
     */
    handleRelatedBOMLineRecordChange(event) {
        event.stopPropagation();
        let row = event.detail.index;
        this._relatedRecordBOMLineData[row] = {...event.detail.item, [SCHEMA.BOM_LINE_CREATED_BY_TYPE.fieldApiName]: 'Platform'};
        this.showFileUploadWarning = true;

        if(event.currentTarget?.checkValidity(row)) {
            this.removeBOMCSVParseErrorAtIndex(row);
        }
    }

    /**
     * @description handles listening to and acting on
     * the relatedrecorddelete event so that we
     * can keep track of quote line deletion here
     * @param event
     */
    handleRelatedBOMLineDelete(event) {
        event.stopPropagation();
        let index = event.detail.index;
        let deletedElement = this._relatedRecordBOMLineData.splice(index, 1);

        this.undoBOMBuffer.push({
            index: index,
            item: deletedElement[0]
        });

        this.removeBOMCSVParseErrorAtIndex(index);

        this.dispatchEvent(
            new ShowToastEvent({
                label: 'Success',
                message: 'BOM Line Item Deleted.',
                messageData: [
                    {
                        func: () => {
                            this.handleUndoRelatedBOMLineDelete();
                        },
                        type: 'function',
                        label: ' Undo',
                    },
                ],
                variant: 'success',
            })
        );
    }

    /**
     * @description: handles undoing the most recent deletion.
     * tries to return the item to it's original index, else inserts it
     * at the end of the array
     */
    handleUndoRelatedBOMLineDelete() {
        let undoElement = this.undoBOMBuffer.pop();

        if(this._relatedRecordBOMLineData.length - 1 <= undoElement.index) {
            this._relatedRecordBOMLineData.splice(undoElement.index, 0, undoElement.item);
        } else {
            this._relatedRecordBOMLineData.push(undoElement.item);
        }
    }

    /**
     * @description handles listening to and acting on
     * the relatedrecordaddline event so that we
     * can keep track of quote line addition at this parent level
     * @param event
     */
    handleRelatedBOMLineAdd(event) {
        event.stopPropagation();
        this._relatedRecordBOMLineData = [...this._relatedRecordBOMLineData, {[SCHEMA.BOM_LINE_QUOTE_VERSION.fieldApiName]: this.relatedRecordId }];
        this.showFileUploadWarning = true;
    }

    /**
     * @description handles listening to and acting on
     * the bomInputItem relatedrecordchange event so that we
     * can keep track of piece line updates at this parent level
     * @param event
     */
    handleRelatedPieceLineRecordChange(event) {
        event.preventDefault();
        event.stopPropagation();
        this._relatedRecordPieceLineData[event.detail.index] = {...event.detail.item};
    }

    /**
     * @description handles listening to and acting on
     * the relatedrecorddelete event so that we
     * can keep track of piece line deletion here
     * @param event
     */
    handleRelatedPieceLineDelete(event) {
        event.preventDefault();
        event.stopPropagation();

        let index = event.detail.index;
        let deletedElement = this._relatedRecordPieceLineData.splice(index, 1);

        this.undoPackageLineBuffer.push({
            index: index,
            item: deletedElement[0]
        });

        this.dispatchEvent(
            new ShowToastEvent({
                label: 'Success',
                message: 'Piece Deleted.',
                variant: 'success',
            })
        );
    }

    /**
     * @description handles listening to and acting on
     * the relatedrecordaddline event so that we
     * can keep track of piece line addition at this parent level
     * @param event
     */
    handleRelatedPieceLineAdd(event) {
        event.preventDefault();
        event.stopPropagation();
        this._relatedRecordPieceLineData = [
            ...this._relatedRecordPieceLineData,
            {
                [SCHEMA.PACKAGE_LINE_QUOTE_VERSION.fieldApiName]: this.relatedRecordId,
                [SCHEMA.PACKAGE_LINE_WEIGHT_UNIT.fieldApiName]: this.weightUnitSelection,
                [SCHEMA.PACKAGE_LINE_LENGTH_UNIT.fieldApiName]: this.dimensionUnitSelection
            }
        ];
    }

    /**
     * @description: handles undoing the most recent deletion.
     * tries to return the item to it's original index, else inserts it
     * at the end of the array
     */
    handleUndoRelatedPieceLineDelete() {
        let undoElement = this.undoPackageLineBuffer.pop();

        if(this._relatedRecordPieceLineData.length - 1 <= undoElement.index) {
            this._relatedRecordPieceLineData.splice(undoElement.index, 0, undoElement.item);
        } else {
            this._relatedRecordPieceLineData.push(undoElement.item);
        }
    }

    /**
     * @description handles action buttons
     * when they are interacted with. an example
     * of this functionality is the "Complete survey now" /
     * "Complete survey later" functionality where we need
     * to inject a "Submit & Close" state in the parent stepper
     * @param event
     */
    handleAction(event) {
        let action = event.target.dataset.action; // denotes that this is not a field but rather an action
        let actionValue = event.target.dataset.actionvalue

        if(action === CONSTANTS.ACTION.EXIT) {
            actionValue = normalizeBoolean(actionValue);

            this.dispatchEvent(
                new CustomEvent(
                    EVENTS.ACTION_EVT,
                    {
                        bubbles: true,
                        composed: true,
                        detail: {
                            action,
                            actionValue
                        }
                    }
                )
            );
        }
    }

    /**
     * @description: handler for when the expanded bom close
     * button is clicked
     */
    handleCloseExpandedBOM(event) {
        publish(this.messageContext, action, {detail: {id: event.target.dataset.componentid, action: ACTION_MESSAGES.COLLAPSE_ACTION}});
    }

    /**
     * @description: handler for the multi tab implementation on the origin
     * destination field. when switching between tabs we need to clear out selections
     * @param event
     */
    handleOriginTabChange(event) {
        event.stopPropagation();

        const tab = event.detail.selection;
        if (!tab) {
            return;
        }

        this.originDefaultTab = tab;

        const setDropShipFlags = (value) => {
            this.recordData[this.QUOTE_DROP_SHIP_FGX_FIELD] = value;
            this.relatedRecordData[this.QUOTE_VERSION_DROP_SHIP_FGX_FIELD] = value;
        };

        if (tab === 1) {
            setDropShipFlags(true);
            this.clearOriginFields();
        } else {
            setDropShipFlags(false);

            if (this.isUpdateMode) {
                switch (tab) {
                        case 2:
                            if (this.cachedSiteId) {
                                this.recordData[this.ORIGIN_SITE_FIELD] = this.cachedSiteId;
                                this.clearOriginFields([this.ORIGIN_SITE_FIELD]);
                        } else {
                            this.clearOriginFields();
                        }
                        break;

                    case 3:
                        if (this.cachedCity || this.cachedCountryId) {
                            this.recordData[this.ORIGIN_CITY_FIELD] = this.cachedCity;
                            this.recordData[this.ORIGIN_COUNTRY_FIELD] = this.cachedCountryId;
                            if(this.cachedState) {
                                this.recordData[this.ORIGIN_STATE_US_FIELD] = this.cachedState;
                            }

                            let excludedFields = [this.ORIGIN_CITY_FIELD, this.ORIGIN_COUNTRY_FIELD];
                            if(this.isOriginCountryUS) {
                                excludedFields = [...excludedFields, this.ORIGIN_STATE_US_FIELD];
                            }
                            this.clearOriginFields(excludedFields);
                        } else {
                            this.clearOriginFields();
                        }

                        break;

                    default:
                        this.clearOriginFields();
                        break;
                }
            } else {
                this.clearOriginFields();
            }
        }
    }

    clearOriginFields(excludeFields = []) {
        const fields = [
            this.ORIGIN_SITE_FIELD,
            this.ORIGIN_STATE_US_FIELD,
            this.ORIGIN_STATE_FIELD,
            this.ORIGIN_CITY_FIELD,
            this.ORIGIN_COUNTRY_FIELD,
        ];

        fields.forEach(field => {
            if (!excludeFields.includes(field)) {
                this.recordData[field] = null;
            }
        });
    }


    /**
     * @description: handler for the multi tab implementation on the origin
     * destination field. when switching between tabs we need to clear out selections
     * @param event
     */
    handleDestinationTabChange(event) {
        let tab = event.detail.selection;
        if(!tab) {
            return;
        }

        this.recordData[this.DESTINATION_SITE_FIELD] = null;
        this.recordData[this.DESTINATION_STATE_US_FIELD] = null;
        this.recordData[this.DESTINATION_STATE_FIELD] = null;
        this.recordData[this.DESTINATION_CITY_FIELD] = null;
        this.recordData[this.DESTINATION_COUNTRY_FIELD] = null;
    }

    /**
     * @description: Handler for the render/action LMS events that
     * we use as a form of "callback" to notify when components in unrelated hierarchies
     * have finished rendering thus we can proceed with the next event we want to send
     * those freshly rendered components.
     * @param event
     */
    handleActionEvents(event) {
        let actionType = event.detail;

        if(actionType === RENDER_MESSAGES.MULTI_TAB_RENDERED && this.multiTabRenderWait) {
            this.multiTabRenderWait = false;
            publish(this.messageContext, action, {detail: {id:'bom-ai-popup', action: ACTION_MESSAGES.EXPAND_ACTION}});
        }

        if(actionType === RENDER_MESSAGES.SECTION_POPUP_RENDERED && this.sectionPopupRenderWait) {
            this.sectionPopupRenderWait = false;
            publish(this.messageContext, action, {detail: ACTION_MESSAGES.VALDITY_CHECK_ACTION});
        }
    }

    /**
     * @description: handles when the user clicks "No" on the
     * intangibles confirmation in the BOM input confirmation screen.
     * in that scenario we need to go back to the BOM input step
     */
    handleIntangibleDecline() {
        this.step = this.step - 1;
    }

    /**
     * @description: handles when the user clicks "Yes" on the
     * intangibles confirmation in the BOM input confirmation screen.
     * in that scenario we mark that the screen was shown.
     */
    handleIntangibleAccept() {
        this.intangibleModalShown = true;
    }

    /**
     * @description: handles clearing the file upload warning
     * that is shown when there are bom lines present and we
     * want to warn the user they will be deleted
     */
    handleClearFileUploadWarning() {
        this.showFileUploadWarning = false;
    }

    /**
     * @description: handles setting all the BOM Line
     * Condition values when selected with the action
     * dropdown
     *
     * @param event
     */
    handleSetCondition(event) {
        let condition = event.target.value;

        this._relatedRecordBOMLineData = this._relatedRecordBOMLineData.map((item) => ({
            ...item,
            [SCHEMA.BOM_LINE_CONDITION.fieldApiName]: condition
        }));
    }

    /**
     * @description api function that the parent
     * stepper can call when it needs to discard
     * reverting changes that are now being cancelled
     * due to the user exiting the stepper or not confirming
     * the reverting change.
     */
    @api
    discardDraftData() {
        this._draftData = undefined;
        this._draftRelatedRecordData = undefined;

        this.generateNewQuote = false;
        this.generateNewQuoteVersion = false;

        /**
         * we need parent stepper to cache
         * the _data values instead of _draftValues
         */
        this.notifyRecordChange();
    }

    @api
    confirmDraftData() {
        this.exitDraftMode();

        /**
         * we need parent stepper to cache
         * the _data values instead of _draftValues
         */
        this.notifyRecordChange();
    }

    /**
     * @description api function used locally and by the stepper
     * parent to get the field api name of the status field that needs
     * to be updated depending on the step.
     * @returns {string[]}
     */
    @api
    getStatusFields() {
        let statusFieldArr = this.statusFieldByStep.filter(status =>
            Object.keys(status).some(k => status[k].includes(this._step))
        );

        if(statusFieldArr.length) {
            return statusFieldArr.reduce((arr, ele) => {
                return arr.concat(...[Object.keys(ele)]);
            }, [])
        } else {
            return [];
        }
    }

    /**
     * @description Function called by the stepper during step changes
     * to check if the user should be allowed to move to the next step.
     * This function gives the abstraction for stepper child implementations to
     * have custom logic for checking validity in case it's not as simple as
     * checking all the current step's component validity.
     * @returns {*}
     */
    @api validateData() {

        if(this.step === 1) {
            let validity = true;
            let validityMessage;

            if(this.isUpdateMode) {
                if(this.originDefaultTab === 2 && !this.recordData[this.ORIGIN_SITE_FIELD] && this.originSiteSelectionDisabled) {
                    validityMessage = 'Site selection is not available for this Quote. Please select "Ship to FGX"';
                    if(!!this.cachedCity || !!this.cachedCountryId) {
                        validityMessage += ' or "City and Country".';
                    }
                    validity = false;
                } else if(this.originDefaultTab === 3 && this.originAddressDisabled && (!this.recordData[this.ORIGIN_CITY_FIELD] || !this.recordData[this.ORIGIN_COUNTRY_FIELD])) {
                    validityMessage = 'City & Country selection is not available for this Quote. Please select "Ship to FGX"';
                    if(!!this.cachedSiteId) {
                        validityMessage += ' or "From Your Sites".';
                    }
                    validity = false;
                }

            } else {
                validity = [...this.template.querySelectorAll('c-fgx-ent-primitive-input, c-fgx-ent-primitive-select')]
                    .reduce((validSoFar, input) => {
                        input.reportValidity();
                        return validSoFar && (input.checkValidity() != undefined ? input.checkValidity() : true);
                    }, true);
            }


            return {
                validity,
                message: validityMessage
            };
        }

        // BOM Input (step 1 of 2)
        if(this.step === 5) {
            let validity = true;
            [...this.bomInputComponentCache].forEach((bomInput) => {
                 if(!bomInput.checkValidity()) {
                     validity = false;
                 }
            });

            return {
                validity,
                message: 'Please verify all required fields are populated and there is at least one bom line'
            };

        // Packing
        } else if(this.step === 7) {
            let validity = true;

            [
                ...this.pieceInputComponentCache,
                ...this.template.querySelectorAll(
                    'c-fgx-ent-primitive-select[data-field="Packing_Configuration__c"], ' +
                    'c-fgx-ent-primitive-select[data-field="DIMWeightEntryType__c"]')
            ]
            .forEach((input) => {
                if(!input.checkValidity()) {
                    input.reportValidity();
                    validity = false;
                }
            });

            return {
                validity,
                message: 'Please verify all required fields are populated and there is at least one packing row'
            };
        }
        // Delivery date
        else if(this.step === 8) {
            let validity = [...this.template.querySelectorAll('c-fgx-ent-primitive-select, c-fgx-ent-primitive-date')]
                .reduce((validSoFar, input) => {
                    input.reportValidity();
                    return validSoFar && (input.checkValidity() != undefined ? input.checkValidity() : true);
                }, true);

            return {
                validity,
                message: 'Please verify a valid (and non-past) delivery date is entered or select "No specific deadline, please balance speed and cost".'
            };
        }
        // Submit Step - Check Site Contact
        else if(this.step === 9) {
            let validity = true;
            [...this.template.querySelectorAll('c-fgx-ent-primitive-input, c-fgx-ent-primitive-select')]
                .forEach((input) => {
                    if(!input.checkValidity()) {
                        input.reportValidity();
                        validity = false;
                    }
                });
            return {
                validity,
                message: 'Please populate the site contact details before booking'
            }

        } else {
            let validity = [...this.template.querySelectorAll('c-fgx-ent-primitive-input, c-fgx-ent-primitive-select, c-fgx-ent-primitive-date, c-fgx-ent-primitive-input-checkbox, c-fgx-ent-primitive-confirm-input')]
                .reduce((validSoFar, input) => {
                    input.reportValidity();
                    return validSoFar && (input.checkValidity() != undefined ? input.checkValidity() : true);
                }, true);

            return {
                validity
            }
        }
    }

    /**
     * @description Helper function to handle setting up
     * state for different steps that need it (ie. defaulting
     * a multi tab view depending on what data is present)
     */
    setupStepDefaults() {
        // Origin
        if(this.step === 1) {
            if(this.multiTabComponent) {
                if(this.isDropShipToFGX){
                    this.originDefaultTab = 1;
                }

                else if(this.recordData[this.ORIGIN_SITE_FIELD]) {
                    this.originDefaultTab = 2;
                }

                else if(!this.recordData[this.ORIGIN_SITE_FIELD] && (this.recordData[this.ORIGIN_CITY_FIELD] || this.recordData[this.ORIGIN_COUNTRY_FIELD])) {
                    this.originDefaultTab = 3;
                }
            }
        }

        // Destination
        if(this.step === 2) {
            if(this.multiTabComponent) {
                if(this.recordData[this.DESTINATION_SITE_FIELD]) {
                    this.destinationDefaultTab = 1;
                }
                if(!this.recordData[this.DESTINATION_SITE_FIELD] && (this.recordData[this.DESTINATION_CITY_FIELD] || this.recordData[this.DESTINATION_COUNTRY_FIELD])) {
                    this.destinationDefaultTab = 2;
                }
            }
        }

        // Import & Export
        if(this.step === 3) {
            let defaultExportEntity = this.exportEntityDefault;
            let defaultImportEntity = this.importEntityDefault;

            if(!this.recordData[SCHEMA.EXPORT_ENTITY.fieldApiName] && defaultExportEntity) {
                this.recordData[SCHEMA.EXPORT_ENTITY.fieldApiName] = defaultExportEntity.Id;
                this.notifyRecordChange();
            }

            if(!this.recordData[SCHEMA.IMPORT_ENTITY.fieldApiName] && defaultImportEntity) {
                this.recordData[SCHEMA.IMPORT_ENTITY.fieldApiName] = defaultImportEntity.Id;
                this.notifyRecordChange();
            }
        }

        if(this.step === 6) {
            if(this._relatedRecordBOMLineData) {
                this.confirmBOMTotal = this._relatedRecordBOMLineData.reduce(
                    (subTotal, bomLine) => {
                        return subTotal + (
                            (bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_UNIT_VALUE.fieldApiName] : 0)  *
                            (bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] ? bomLine[SCHEMA.BOM_LINE_QUANTITY.fieldApiName] : 0)
                        )
                    },0
                );
            }
        }
    }

    /**
     * @description helper function to setup all the
     * draft data in the case that there is a change coming
     * in that necessitates a draft state
     */
    enterDraftMode() {
        let recordData = Object.assign({}, this._data);
        let relatedRecordData = Object.assign({}, this._relatedRecordData);

        if(this._draftData !== undefined) {
            recordData = {...recordData, ...this._draftData}
        }

        if(this._draftRelatedRecordData !== undefined) {
            relatedRecordData = {...relatedRecordData, ...this._draftRelatedRecordData}
        }

        this._draftData = recordData;
        this._draftRelatedRecordData = relatedRecordData;
    }

    /**
     * @description helper function to move all the draft data
     * changes back into the non draft state after they have been
     * confirmed
     */
    exitDraftMode() {
        let recordData = Object.assign({}, this._data);
        let relatedRecordData = Object.assign({}, this._relatedRecordData);

        if(this._draftData !== undefined) {
            recordData = {...recordData, ...this._draftData}
        }

        if(this._draftRelatedRecordData !== undefined) {
            relatedRecordData = {...relatedRecordData, ...this._draftRelatedRecordData}
        }

        this._data = recordData;
        this._relatedRecordData = relatedRecordData;

        this._draftData = undefined;
        this._draftRelatedRecordData = undefined;
    }

    /**
     * @description helper function to delete a specific
     * CSV parsing error row
     * @param index
     */
    removeBOMCSVParseErrorAtIndex(index) {
        if(Object.keys(this._relatedRecordBOMLineCSVDataErrors).includes(index.toString())) {
            delete this._relatedRecordBOMLineCSVDataErrors[index];
        }
    }

    /**
     * @description refresh the cached record in case data changed
     * between the last open and close of the stepper and to get the
     * latest between step changes. to be called in the stepper child
     * implementation in the renderedCallback()
     * @returns {Promise<any>}
     */
    @api
    refreshRecord() {
        if(this.recordData) {
            return refreshApex(this.wireRecordData);
        }
    }

    /**
     * @description helper function to dispatch a
     * step change event to be consumed by the stepper
     * parent
     * @param previous
     */
    notifyStepChange(previous) {
        const event = new CustomEvent(
            EVENTS.STEP_CHANGE_EVT,
            {
                bubbles: true,
                composed: true,
                detail: {
                    previous: previous,
                    step: this._step,
                    pathStep: this.pathStep
                }
            }
        );
        this.dispatchEvent(event);
        this.handleStepChangeUpdates(event);

        try {
            this.handleStepChange(event);
        } catch (e) {}
    }

    /**
     * @description helper function to dispatch a record
     * change event to be consumed by the stepper parent
     */
    notifyRecordChange() {
        this.dispatchEvent(
            new CustomEvent(
                EVENTS.RECORD_CHANGE_EVT,
                {
                    bubbles: true,
                    composed: true,
                    detail: {
                        recordData: this.recordData,
                        relatedRecordData: this.relatedRecordData
                    }
                }
            )
        );
    }

    /**
     * @description handles the notification event
     * from the file upload component telling us that a file
     * has been added which can't be saved yet because we don't
     * have a parent id yet.
     * @param event
     */
    handleCachedFileAdded(event) {
        this.cachedRelatedFiles = [...event.detail.data];
    }

    /**
     * @description handles the notification event
     * from the file upload component telling us that all the
     * files that were waiting to be uploaded have now been uploaded
     * and we can clear the pending list here.
     * @param event
     */
    handleCachedFilesUploaded(event) {
        this.cachedRelatedFiles = null;
    }

    /**
     * @description: Central handler called during step changes
     * so that any logic we need to do between specific step changes
     * can be handled here
     * @param event
     */
    handleStepChangeUpdates(event) {

        // Reset the show intangible modal flag to allow it to be shown again
        if(event.detail.previous === 8 && event.detail.step === 7) {
            this.intangibleModalShown = false;
        }

        // Removing the validity check from the expanded bom input that we do on every re-render
        // after getting AI results
        if(event.detail.previous === 7 && event.detail.step === 6) {
            this.bomInputValidityCheckOnRender = false;
        }

        // Domestic shipments need to skip Import/Export and Commercialization
        if((event.detail.previous === 2 || !event.detail.previous) && (event.detail.step === 3 || event.detail.step === 4) && this.isDomestic) {
            this.step = 5;
        }
        if(event.detail.previous === 5 && (event.detail.step === 4 || event.detail.step === 3) && this.isDomestic) {
            this.step = 2;
        }

        // When coming back to step 5 (in this case we'd be loading in an existing quote with lines that were ingested via CSV or AI),
        // we need to allow the results to be seen for AI and CSV upload in their respect tab. We also need to default to the correct
        // tab but we are doing that elsewhere.
        if(event.detail.step === 5 && event.detail.previous !== 5) {
            this.defaultBOMInputTab = 1;

            if(this.relatedRecordData[SCHEMA.QUOTE_VERSION_BOM_AI_INGESTED.fieldApiName]) {
                this.bomAIImportComplete = true;
                this.defaultBOMInputTab = 3;
            }
            if(this.relatedRecordData[SCHEMA.QUOTE_VERSION_BOM_CSV_INGESTED.fieldApiName]) {
                this.bomCSVImportComplete = true;
                this.defaultBOMInputTab = 2;
            }
        }

        // Send out a dismiss message to the sticky toast in case we still have one
        // when leaving the BOM step
        if(event.detail.previous === 5 && event.detail.step !== 5) {
            this.dispatchEvent(
                new DismissToastEvent()
            );

            if(event.detail.step > 5) {
                this._relatedRecordBOMLineCSVFatalErrors = [];
                this._relatedRecordBOMLineCSVDataErrors = {};
            }
        }
    }

    /**
     * @description: Handler to let us know that
     * an upload has started for the CSV used in the CSV to
     * BOM process.We need this so we can start the loading screen
     */
    handleCsvFileUploadStarted() {
        this.bomCSVImportComplete = false;
        this.bomAIImportComplete = false;
        this.showLoading = true;
        this.loadingLabel = 'CSV Parsing in Progress, this may take a minute…';
        this.clearBOMLineDeleteBuffer();
    }

    /**
     * @description: Handler that is fired when the uploader component
     * finishes inserting the CSV file. From here we send it to the BOM Csv service
     * to be processed and return a result.
     * @param event
     */
    handleCsvFileUploadFinished(event) {

        getBOMLinesCSV(
            {
                data: decodeURIComponent(atob(event.detail.data)),
                recordId: this.relatedRecordId,
                contentDocumentLink: event.detail.contentDocumentLink
            })
            .then(result => {
                this.bomCSVImportComplete = true;

                if(!result.responseLines || result.responseLines?.length === 0 || result.fatalErrors.length > 0) {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            label: 'Error',
                            message: 'There were errors during parsing. Please see them listed below.',
                            variant: 'error',
                        })
                    );
                    this._relatedRecordBOMLineCSVFatalErrors = result.fatalErrors;
                } else if(!result.deleteExistingLinesSuccess) {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            label: 'Error',
                            message: 'There were problems replacing existing BOM lines. Please see errors listed below.',
                            variant: 'error',
                        })
                    );
                    this._relatedRecordBOMLineCSVFatalErrors = result.bomLineDeleteErrors;
                } else {
                    this.showFileUploadWarning = true;
                    this._relatedRecordBOMLineData = [...result.responseLines]
                    this._relatedRecordBOMLineCSVDataErrors = result.rowErrors;
                    this._relatedRecordBOMLineCSVFatalErrors = [];

                    this.sectionPopupRenderWait = true;
                    this.bomInputValidityCheckOnRender = true;

                    publish(this.messageContext, action, {detail: {id:'bom-csv-popup', action: ACTION_MESSAGES.EXPAND_ACTION}});

                    let hasRowErrors = Object.keys(this._relatedRecordBOMLineCSVDataErrors).length > 0;
                    let message =  hasRowErrors ?
                        'CSV import partially successful. Please see rows with errors for further details.' :
                        'CSV successfully imported, please validate the results.'
                    let variant = hasRowErrors ? 'warning' : 'success';
                    let mode = hasRowErrors ? 'sticky' : 'dismissible'

                    this.dispatchEvent(
                        new ShowToastEvent({
                            label: 'Success',
                            message: message,
                            variant: variant,
                            mode: mode
                        })
                    );
                }
            })
            .catch(error => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        label: 'Error',
                        message: 'CSV Upload not successful.',
                        variant: 'error',
                    })
                );
            }).finally(() => {
                this.showLoading = false;
            });
    }

    /**
     * @description: Handler to let us know that
     * an upload has started for the PDF used in the AI
     * BOM process.We need this so we can start the loading screen
     */
    handleAiFileUploadStarted() {
        this.bomAIImportComplete = false;
        this.bomCSVImportComplete = false;
        this.showLoading = true;
        this.loadingLabel = '✨ AI Upload in Progress, this may take a minute…';
        this.clearBOMLineDeleteBuffer();
    }

    /**
     * @description: Handler that is fired when the uploader component
     * finishes inserting the PDF file. From here we use PDF.js to parse the PDF and
     * then send out the request to the OpenAI service
     * @param event
     */
    handleAiFileUploadFinished(event) {

        this.getPdfText(this.base64ToArrayBuffer(event.detail.data))
            .then(result => {
                getBOMLinesGPT(
                    {
                        data: result,
                        recordId: this.relatedRecordId,
                        contentDocumentLink: event.detail.contentDocumentLink
                    })
                    .then(result => {
                        if (!result.invoiceLines || result.invoiceLines?.length === 0) {
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    label: 'Error',
                                    message: 'AI Upload not successful, please use the CSV upload option.',
                                    variant: 'error',
                                })
                            );

                        } else if(!result.deleteExistingLinesSuccess) {
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    label: 'Error',
                                    message: 'There were problems replacing existing BOM lines.',
                                    variant: 'error',
                                })
                            );

                        } else {
                            this.bomAIImportComplete = true;
                            this.showFileUploadWarning = true;
                            this._relatedRecordBOMLineData = [...result.invoiceLines].map((line, index) => {
                                return {
                                    ...line,
                                    [SCHEMA.BOM_LINE_QUOTE_VERSION.fieldApiName]: this.relatedRecordId,
                                    [SCHEMA.BOM_LINE_CREATED_BY_TYPE.fieldApiName]: 'Platform',
                                    [SCHEMA.BOM_LINE_SORT_ORDER.fieldApiName]: index + 1,
                                }
                            });

                            this._relatedRecordBOMLineCSVDataErrors = {};
                            this.sectionPopupRenderWait = true;
                            this.bomInputValidityCheckOnRender = true;
                            publish(this.messageContext, action, {detail: {id:'bom-ai-popup', action: ACTION_MESSAGES.EXPAND_ACTION}});

                            this.dispatchEvent(
                                new ShowToastEvent({
                                    label: 'Success',
                                    message: 'PDF successfully imported via AI, please validate the results.',
                                    variant: 'success'
                                })
                            );
                        }
                    })
                    .catch(error => {
                        this.dispatchEvent(
                            new ShowToastEvent({
                                label: 'Error',
                                message: 'AI Upload not successful, please use the CSV upload option.',
                                variant: 'error',
                            })
                        );
                    }).finally(() => {
                        this.showLoading = false;
                    });

            }).catch(() => {
                this.showLoading = false;
                this.dispatchEvent(
                    new ShowToastEvent({
                        label: 'Error',
                        message: 'Unable to parse PDFs without a text layer.',
                        variant: 'error',
                    })
                );
            });
    }

    /**
     * @description: Helper to parse out the PDF information that
     * PDF.js gives us. This means going page by page, line by line,
     * and token by token
     * @param data
     * @returns {Promise<unknown>}
     */
    getPdfText(data) {
        return new Promise((resolve, reject) => {
            pdfjsLib.getDocument({data}).promise.then(async (doc) => {
                let pageTexts = Array.from({length: doc.numPages}, async (v, i) => {
                    return (await (await doc.getPage(i + 1)).getTextContent()).items.map(token => token.str).join('');
                });

                Promise.all(pageTexts).then(text => {
                    resolve(text.join(''));
                })
            }).catch((e) => {
                reject();
            });
        });
    }

    /**
     * @description: Helper to turn the base64 string into
     * an array buffer that PDF.js can use
     * @param base64
     * @returns {ArrayBufferLike}
     */
    base64ToArrayBuffer(base64) {
        var binaryString = atob(base64);
        var bytes = new Uint8Array(binaryString.length);
        for (var i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    }

    /**
     * @description: Handler that is fired when a site has been added.
     * Sends an email to the client with site details.
     * @param event
     */
    sendSiteEmail(recordId){
        const successEvent = new ShowToastEvent({
            label: "Success",
            message: "Site has been created! An email has been sent with additional information.",
            variant: "success",
            mode: "dismissable",
        });
    
        this.dispatchEvent(successEvent);
    }
}