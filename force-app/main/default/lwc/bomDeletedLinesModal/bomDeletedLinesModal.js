import { LightningElement, api, wire } from 'lwc';
import { publish, MessageContext } from 'lightning/messageService';
import channel from '@salesforce/messageChannel/IntModalChannel__c';

export default class BomDeletedLinesModal extends LightningElement {
    @api deletedLines;
    @api buttonTextClass;
    @api modalDepth = 0;
    subscription;
    componentId = 'deletedLinesModal';

    @wire(MessageContext)
    messageContext;

    get formattedLines(){
        return this.deletedLines.map(line => {
            return {
                ...line,
                formattedValue: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2 }).format(line?.Value__c ? line.Value__c : 0),
                formattedTotalValue: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2 }).format(line?.Total_Value__c ? line.Total_Value__c : 0),
            }
        })
    }

    get viewDeletedLinesLabel(){
        return `View (${this.deletedLines.length}) Deleted Lines`
    }

    get tableStyles(){
        return `z-index: ${9002 + parseInt(this.modalDepth)};`
    }

    handleClose(){
        if(this.messageContext) {
            publish(this.messageContext, channel, {detail: {id: this.componentId, action: 'close'}});
        }
    }
}