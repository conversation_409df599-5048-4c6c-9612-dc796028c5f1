<template>
    <div class="relative max-width-90vw">
        <div class={tableContainerClasses}>
            <table lwc:ref="table" class="table-fixed height-medium block border-collapse--separate"
                   onscroll={handleScroll}>
                <thead>
                <tr class="adjusted-headerRow headerRow align-items-center relative overflow-visible white-background top-sticky">
                    <th class="column large"
                        data-field="Site_Name__c"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>Site Name</p>
                            <template if:true={sortArrow.Site_Name__cShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="Name"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>Site ID</p>
                            <template if:true={sortArrow.NameShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="CountryName"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>Country</p>
                            <template if:true={sortArrow.CountryNameShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="City__c"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>City</p>
                            <template if:true={sortArrow.City__cShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="Site_Code__c"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>Site Code</p>
                            <template if:true={sortArrow.Site_Code__cShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="Site_Type__c"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>Site Type</p>
                            <template if:true={sortArrow.Site_Type__cShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="Site_Compliance_Status__c"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>Site Compliance Status</p>
                            <template if:true={sortArrow.Site_Compliance_Status__cShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column full-width"
                        data-field="Site_Survey_Status__c"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-sp">
                            <p>Site Survey Status</p>
                            <template if:true={sortArrow.Site_Survey_Status__cShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class=""></th>
                </tr>
                </thead>
                <tbody>
                <template for:each={recordList} for:item="record">
                    <c-fgx-ent-site-table-item
                            key={record.Id}
                            record-item={record}
                            show-table-shadow={showTableShadow}>
                    </c-fgx-ent-site-table-item>
                </template>
                </tbody>
            </table>
            <c-fgx-ent-loading
                    show={loading}
                    style-classes="mtl">
            </c-fgx-ent-loading>
        </div>
    </div>
</template>
