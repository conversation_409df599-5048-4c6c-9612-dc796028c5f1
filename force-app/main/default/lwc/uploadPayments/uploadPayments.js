import { LightningElement, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { publish, MessageContext } from 'lightning/messageService';
import channel from '@salesforce/messageChannel/IntModalChannel__c';
import { CurrentPageReference } from 'lightning/navigation';
import { IsConsoleNavigation, getFocusedTabInfo, closeTab } from 'lightning/platformWorkspaceApi';
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import getInvoices from '@salesforce/apex/UploadPaymentsController.getInvoices';
import getPayments from '@salesforce/apex/UploadPaymentsController.getPayments';
import uploadPayments from '@salesforce/apex/UploadPaymentsController.uploadPayments';

export default class UploadPayments extends NavigationMixin(LightningElement) {
    modalDepth = 0;
    showModal = false;
    modalOpened = false;
    bypassButton = true;
    componentId = 'uploadPaymentsModal';
    uploaded = false;
    errors = []; // store list of errors
    parsedData = [];
    rowsToUpload = [];
    existingPayments = [];
    existingInvoices = [];
    loading = false;

    expectedHeaders = [
        'Date',
        'RefNumber',
        'Applied to Invoice Ref',
        'Applied to Invoice Amount',
        'Applied to Invoice Disc Amount'
    ];
    headerMap = {
        'Date': 'Payment_Date__c',
        'RefNumber': 'Payment_Reference__c',
        'Applied to Invoice Ref': 'Invoice__r.Name',
        'Applied to Invoice Amount': 'Payment_Amount__c',
        'Applied to Invoice Disc Amount': 'Invoice_Discount_Amount__c'
    };

    @wire(CurrentPageReference)
    getPageReferenceParams(pageRef) {
        if (pageRef?.state) {
            this.showModal = pageRef.state.c__showModal === 'true';
        }
    }

    @wire(IsConsoleNavigation) 
    isConsoleNavigation;

    @wire(MessageContext)
    messageContext;

    renderedCallback() {
        if (this.showModal && !this.modalOpened) {
            const modal = this.refs.modal;
            if (modal) {
                modal.open();
                this.modalOpened = true;
            }
        }
    }

    get expectedHeaders() {
        return Object.keys(this.headerMap);
    }

    get invoiceNames(){
        return this.parsedData.map(line => line["Invoice__r.Name"]);
    }

    get submitDisabled(){
        return this.errors.some(error => error.message === "Cannot read file");
    }

    get errorsFound(){
        return this.errors.length > 0;
    }

    handleClose(){
        this[NavigationMixin.Navigate]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Payment__c',
                actionName: 'list'
            },
            state: {
                filterName: 'Recent'
            }
        });

        this.reset();
        this.modalOpened = false;
        if(this.messageContext) {
            publish(this.messageContext, channel, {detail: {id: this.componentId, action: 'close'}});
        }
        this.closeTab();
    }

    async closeTab() {
        if (!this.isConsoleNavigation) {
            return;
        }
        const { tabId } = await getFocusedTabInfo();
        await closeTab(tabId);
    }

    handleFileChange(event) {
        const file = event.target.files[0];
        this.readFile(file);
    }

    handleDragOver(event) {
        event.preventDefault();
    }

    handleDrop(event) {
        event.preventDefault();
        const file = event.dataTransfer.files[0];
        this.readFile(file);
    }

    triggerFileSelect() {
        this.refs.fileSelect.click();
    }

    readFile(file) {
        if (file && file.type === 'text/csv') {
            this.loading = true;
            const reader = new FileReader();
            reader.onload = () => {
                this.parseCSV(reader.result);
            };
            reader.readAsText(file);
        } else {
            this.showToast('Error', 'Please upload a valid CSV file.', 'error');
        }
    }

    parseCSV(csv) {
        const lines = csv.trim().split('\n');
        const headers = lines[0].split(',').map(header => header.trim());

        // if headers don't match, throw error and return
        if (headers.length !== this.expectedHeaders.length || !headers.every((header, i) => header === this.expectedHeaders[i])) {
            this.errors.push({key: this.errors.length, message: 'Cannot read file'});
            this.uploaded = true;
            this.loading = false;
            return;
        }

        const mappedHeaders = headers.map(header => this.headerMap[header] || null);

        this.parsedData = lines.slice(1).map(line => {
            const values = line.split(',').map(v => v.trim().replace(/^"|"$/g, ''));
            const row = {};
            mappedHeaders.forEach((key, i) => {
                if (key) {
                    let val = values[i] || '';

                    // convert date to YYYY-MM-DD
                    if (key === 'Payment_Date__c') {
                        const [month, day, year] = val.split('/');
                        if (month && day && year) {
                            val = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                        }
                    }

                    row[key] = val;
                }
            });
            return row;
        });
        console.log('Parsed Data:', JSON.stringify(this.parsedData));

        this.errorCheck();
    }

    async errorCheck(){
        await Promise.all([
            this.fetchExistingPayments(),
            this.fetchExistingInvoices()
        ]);

        const seenNewPayments = new Set(); // store unique new payments that we will used to check for dupes

        // calculate the total payment towards each invoice
        const paymentPerInvoice = this.parsedData.reduce((acc, row) => {
            const invoiceName = row["Invoice__r.Name"];
            const paymentAmount = parseFloat(row["Payment_Amount__c"]) || 0;

            acc.set(invoiceName, (acc.get(invoiceName) || 0) + paymentAmount);

            return acc;
        }, new Map());

        // make error list row by row
        this.parsedData.forEach((row, index) => {
            let errorFound = false;
            if (!row["Invoice__r.Name"] || !row["Payment_Date__c"] || !row["Payment_Amount__c"]){
                errorFound = true;
                this.errors.push({key: this.errors.length, message: `Row ${index+2}: Incomplete data`});
            }
            else {
                // check for duplicate payment
                // first among existing payments
                const duplicatePayment = this.existingPayments.filter(payment => payment?.Invoice__r?.Name === row["Invoice__r.Name"] 
                                            && payment?.Payment_Date__c === row["Payment_Date__c"] && payment?.Payment_Amount__c === Number(row["Payment_Amount__c"]));
                if (duplicatePayment.length > 0){
                    errorFound = true;
                    this.errors.push({key: this.errors.length, message: `Row ${index+2}: Matching payment already exists in the system`});
                }
                // then among new payments
                else {
                    const payment = `${row["Payment_Date__c"]}||${row["Invoice__r.Name"]}||${row["Payment_Amount__c"]}`;
                    if (seenNewPayments.has(payment)) {
                        // duplicate found among new payments
                        errorFound = true;
                        this.errors.push({key: this.errors.length, message: `Row ${index+2}: Duplicate payment detected`});
                    } else {
                        seenNewPayments.add(payment);
                    }
                }

                // check if invoice exists
                const invoice = this.existingInvoices.find(invoice => invoice?.Name === row["Invoice__r.Name"]);
                if (!invoice){
                    errorFound = true;
                    this.errors.push({key: this.errors.length, message: `Row ${index+2}: Invoice not found`});
                }
                // check for overpayment
                else if (invoice?.Amount_Due__c < row["Payment_Amount__c"]){
                    errorFound = true;
                    this.errors.push({key: this.errors.length, message: `Row ${index+2}: Invoice ${invoice.Name} will be overpaid. Amount remaining on invoice: $${invoice.Amount_Due__c}. Listed payment amount: $${row["Payment_Amount__c"]}`});
                }
                // check if the total of payments towards invoice in this patch is overpaying
                else {
                    const totalPayment = paymentPerInvoice.get(row["Invoice__r.Name"]) || 0;
                    if (totalPayment > invoice?.Amount_Due__c){
                        errorFound = true;
                        this.errors.push({key: this.errors.length ,message: `Row ${index+2}: Invoice ${invoice.Name} will be overpaid due to payments included. Amount remaining on invoice: $${invoice.Amount_Due__c}. Total payment amount included: $${totalPayment.toFixed(2)}`});
                    }
                }
            }

            if (!errorFound){
                this.rowsToUpload.push(row);
            }
        });

        this.uploaded = true;
        this.loading = false;
    }

    fetchExistingPayments(){
        return getPayments({ invoiceNames: this.invoiceNames })
        .then(results => {       
            this.existingPayments = [...results];
            console.log('Existing payments: ', JSON.stringify(this.existingPayments))
        })
        .catch(error => {
            console.error(error);
            this.showToast("Error", "There was an error fetching existing payments", "error");
        })
    }

    fetchExistingInvoices(){
        return getInvoices({ invoiceNames: this.invoiceNames })
        .then(results => {      
            this.existingInvoices = [...results]; 
            console.log('Existing invoices: ', JSON.stringify(this.existingInvoices))
        })
        .catch(error => {
            console.error(error);
            this.showToast("Error", "There was an error fetching existing invoices", "error");
        })
    }

    handleRetry(){
        this.reset();
    }

    reset(){
        this.errors = [];
        this.parsedData = [];
        this.uploaded = false;
        this.rowsToUpload = [];
        this.existingPayments = [];
        this.existingInvoices = [];
    }
    
    showToast(title, message, variant) {
        const showToastEvent = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
        });
        this.dispatchEvent(showToastEvent);
    }

    handleSubmit(){
        this.rowsToUpload = this.rowsToUpload.map(row => {
            return {
                ...row,
                "Invoice__c": this.existingInvoices.find(invoice => invoice?.Name === row["Invoice__r.Name"])?.Id
            }
        })
        
        this.loading = true;
        uploadPayments({ paymentsToInsert: JSON.stringify(this.rowsToUpload) })
        .then((result) => {
            this.showToast('Success', 'Payments uploaded', 'success');
            this.handleClose();
        })
        .catch((error) => {
            console.error('Error uploading payments:', error);
            this.showToast('Error', 'Error uploading payments', 'error');
        }).finally(()=> {
            this.loading = false;
        });
    }
}