<template>
    <template lwc:if={dataLoaded}>
        <template lwc:if={bomExist}>
            <div class={containerClasses}>
                <div class={headerStyleClasses}>
                    <div class="ptxs pbxs center">
                        <p>Manufacturer</p>
                    </div>
                    <div></div>
                    <div class="ptxs pbxs center">
                        <p>Part Number</p>
                    </div>
                    <div></div>
                    <div class="ptxs pbxs center">
                        <p>Product Description</p>
                    </div>
                    <div></div>
                    <div class="ptxs pbxs center">
                        <p>Quantity</p>
                    </div>
                    <div></div>
                    <div class="ptxs pbxs center">
                        <p>Unit Value</p>
                    </div>
                    <div></div>
                    <div class="ptxs pbxs center">
                        <p>Total Value</p>
                    </div>
                    <div></div>
                    <div class="ptxs pbxs center">
                        <p>Product Type</p>
                    </div>
                    <div></div>
                    <div class="ptxs pbxs center">
                        <p>Condition</p>
                    </div>
                    <template if:true={showAdvancedLogisticsFields}>
                        <div></div>
                        <div class="ptxs pbxs center">
                            <p>HS Code</p>
                        </div>
                        <div></div>
                        <div class="ptxs pbxs center">
                            <p>ECCN</p>
                        </div>
                        <div></div>
                        <div class="ptxs pbxs center">
                            <p>COO</p>
                        </div>
                    </template>
                </div>
                <div class="body">
                    <template
                        for:each={items}
                        for:item="item">
                        <c-fgx-ent-shipments-bom-table-item
                            key={item.Id}
                            item={item}
                            show-advanced-logistics-fields={showAdvancedLogisticsFields}
                            show-asset-units={showAssetUnits}>
                        </c-fgx-ent-shipments-bom-table-item>
                    </template>
                </div>
            </div>
        </template>
        <template lwc:else>
            <div class="full-width min-height-small pts pbs flex center flex-direction-column flex-gap-rp">
                <div class="text-align-center">
                    <p class="mbs">BOM Pending</p>
                    <p class="p secondary-neutral-color">
                        Please check your action items.
                    </p>
                </div>
            </div>
        </template>    
    </template>
</template>