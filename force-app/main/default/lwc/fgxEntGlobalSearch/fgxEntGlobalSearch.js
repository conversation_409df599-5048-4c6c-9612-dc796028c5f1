/**
 * fgxEntGlobalSearch
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 5/20/24
 */
import {LightningElement, api, track, wire} from 'lwc';
import getSearchResults from '@salesforce/apex/FGXEnt_GlobalSearchController.searchRecords';
import {ShowToastEvent} from "c/showToastEvent";
import { getPages } from "c/fgxEntConstants";
import { classSet } from "c/utils";
import { publish, MessageContext } from "lightning/messageService";
import { getActionMessages } from "c/fgxEntConstants";
import action from '@salesforce/messageChannel/ActionChannel__c';

import CASE_OBJ from '@salesforce/schema/Case';
import QUOTE_OBJ from '@salesforce/schema/Quote__c';
import ENTITY_OBJ from '@salesforce/schema/Entity__c';
import SITE_OBJ from '@salesforce/schema/Site__c';
import PULLREQUEST_OBJ from '@salesforce/schema/Pull_Request__c';

const PAGES = getPages();
const ACTION_MESSAGES = getActionMessages();

export default class FgxEntGlobalSearch extends LightningElement {
    data = {};
    debounceSearchTimer;
    searchValue = '';
    loading = false;
    showNoResults = false;

    @track
    objectFilterOptionsMap = {};

    @wire(MessageContext)
    messageContext;

    get quoteResultDisplayFields() {
        return {
            header: 'quoteNumber',
            headerIcon: 'quotes.svg',
            linkAttributes: {
                linkTarget: PAGES.QUOTEDETAIL,
                linkParams: {
                    recordId: 'Id',
                    loadAction: 'loadActionSelectQuoteVersion'
                }
            },
            subheader: [
                {
                    label: 'Origin',
                    fieldName: 'originCountryName',
                    cellAttributes: {
                        iconPath: '/images/flags/',
                        iconNameField: 'originFlagIcon',
                        iconPosition: 'left',
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Destination',
                    fieldName: 'destinationCountryName',
                    cellAttributes: {
                        iconPath: '/images/flags/',
                        iconNameField: 'destinationFlagIcon',
                        iconPosition: 'left',
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Client Ref',
                    fieldName: 'clientRef',
                    cellAttributes: {
                        nullReplacement: 'n/a'
                    }
                },
                {
                    label: 'Shipment Value',
                    fieldName: 'shipmentValue',
                    type: 'currency',
                    typeAttributes: {
                        currencyCode: 'USD'
                    }
                },
                { label: 'Quote Status', fieldName: 'quoteStatus'}

            ]
        }
    }

    get shipmentResultDisplayFields() {
        return {
            header: 'caseNumber',
            headerIcon: 'shipments.svg',
            linkAttributes: {
                linkTarget: PAGES.SHIPMENTDETAIL,
                linkParams: {
                    recordId: 'Id'
                }
            },
            subheader: [
                {
                    label: 'Origin',
                    fieldName: 'originCountryName',
                    cellAttributes: {
                        iconPath: '/images/flags/',
                        iconNameField: 'originFlagIcon',
                        iconPosition: 'left',
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Destination',
                    fieldName: 'destinationCountryName',
                    cellAttributes: {
                        iconPath: '/images/flags/',
                        iconNameField: 'destinationFlagIcon',
                        iconPosition: 'left',
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Shipment Value',
                    fieldName: 'shipmentValue',
                    type: 'currency',
                    typeAttributes: {
                        currencyCode: 'USD'
                    }
                },
                {
                    label: 'Export/Import Entity',
                    fieldName: 'exportImportEntity',
                    cellAttributes: {
                        nullReplacement: 'TBD'
                    }
                },
                { label: 'Origin/Destination Site', fieldName: 'originDestinationSite'},
                { label: 'Date Created', fieldName: 'createdDate'}
            ]
        }
    }

    get entityResultDisplayFields() {
        return {
            header: 'entityName',
            headerIcon: '',
            linkAttributes: {
                linkTarget: PAGES.ENTITYDETAIL,
                linkParams: {
                    recordId: 'entityId'
                }
            },
            subheader: [
                {
                    label: 'Entity Country',
                    fieldName: 'entityCountry',
                    cellAttributes: {
                        iconPath: '/images/flags/',
                        iconNameField: 'entityCountryImg',
                        iconPosition: 'left',
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Entity Code',
                    fieldName: 'entityCode',
                    cellAttributes: {
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Entity Type',
                    fieldName: 'entityType',
                    cellAttributes: {
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Import Status',
                    fieldName: 'entityImportStatus',
                    cellAttributes: {
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Export Status',
                    fieldName: 'entityExportStatus',
                    cellAttributes: {
                        nullReplacement: 'Pending'
                    }
                }
            ]
        }
    }

    get siteResultDisplayFields() {
        return {
            header: 'siteName',
            headerIcon: '',
            linkAttributes: {
                linkTarget: PAGES.SITEDETAIL,
                linkParams: {
                    recordId: 'Id'
                }
            },
            subheader: [
                {
                    label: 'Site Country',
                    fieldName: 'siteCountry',
                    cellAttributes: {
                        iconPath: '/images/flags/',
                        iconNameField: 'siteCountryImg',
                        iconPosition: 'left',
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Site Code',
                    fieldName: 'siteCode'
                },
                {
                    label: 'Site Type',
                    fieldName: 'siteType',
                    cellAttributes: {
                        nullReplacement: 'Unspecified'
                    }
                },
                {
                    label: 'Compliance Status',
                    fieldName: 'complianceStatus',
                    cellAttributes: {
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Survey Status',
                    fieldName: 'surveyStatus',
                    cellAttributes: {
                        nullReplacement: 'Pending'
                    }
                }
            ]
        }
    }

    get pullRequestResultDisplayFields() {
        return {
            header: 'pullRequestName',
            headerIcon: 'inventory.svg',
            linkAttributes: {
                linkTarget: PAGES.PULLREQUESTDETAIL,
                linkParams: {
                    recordId: 'id'
                }
            },
            subheader: [
                {
                    label: 'Status',
                    fieldName: 'status',
                    cellAttributes: {
                        nullReplacement: 'Pending'
                    }
                },
                {
                    label: 'Matching Serials',
                    fieldName: 'matchingSerialsString',
                    cellAttributes: {
                        nullReplacement: 'n/a'
                    }
                },
                {
                    label: 'Date Created',
                    fieldName: 'createdDate'
                }
            ]
        }
    }

    get columns() {
        return {
            [QUOTE_OBJ.objectApiName]: this.quoteResultDisplayFields,
            [CASE_OBJ.objectApiName]: this.shipmentResultDisplayFields,
            [ENTITY_OBJ.objectApiName]: this.entityResultDisplayFields,
            [SITE_OBJ.objectApiName]: this.siteResultDisplayFields,
            [PULLREQUEST_OBJ.objectApiName]: this.pullRequestResultDisplayFields
        }
    }


    get shipments() {
        if(this.data.shipments && this.data.shipments.length > 0 && (this.objectFilterOptionsMap.shipments?.selected || this.noFiltersSelected)) {
            return this.data.shipments;
        }
        return null;
    }

    get quotes() {
        if(this.data.quotes && this.data.quotes.length > 0 && (this.objectFilterOptionsMap.quotes?.selected || this.noFiltersSelected)) {
            return this.data.quotes;
        }
        return null;
    }

    get entities() {
        if(this.data.entities && this.data.entities.length > 0 && (this.objectFilterOptionsMap.entities?.selected || this.noFiltersSelected)) {
            return this.data.entities;
        }
        return null;
    }

    get sites() {
        if(this.data.sites && this.data.sites.length > 0 && (this.objectFilterOptionsMap.sites?.selected || this.noFiltersSelected)) {
            return this.data.sites;
        }
        return null;
    }

    get pullRequests() {
        if(this.data.pullRequests && this.data.pullRequests.length > 0 && (this.objectFilterOptionsMap.pullRequests?.selected || this.noFiltersSelected)) {
            return this.data.pullRequests;
        }
        return null;
    }

    get results() {
        let allResults = [];

        if (this.data.quotes && this.data.quotes.length > 0 && (this.objectFilterOptionsMap.quotes?.selected || this.noFiltersSelected)) {
            allResults.push(...this.data.quotes);
        }
        if (this.data.shipments && this.data.shipments.length > 0 && (this.objectFilterOptionsMap.shipments?.selected || this.noFiltersSelected)) {
            allResults.push(...this.data.shipments);
        }
        if (this.data.entities && this.data.entities.length > 0 && (this.objectFilterOptionsMap.entities?.selected || this.noFiltersSelected)) {
            allResults.push(...this.data.entities);
        }
        if (this.data.sites && this.data.sites.length > 0 && (this.objectFilterOptionsMap.sites?.selected || this.noFiltersSelected)) {
            allResults.push(...this.data.sites);
        }
        if (this.data.pullRequests && this.data.pullRequests.length > 0 && (this.objectFilterOptionsMap.pullRequests?.selected || this.noFiltersSelected)) {
            allResults.push(...this.data.pullRequests);
        }
        return allResults;
    }

    get noFiltersSelected() {
        return !Object.values(this.objectFilterOptionsMap).some((filter)=> filter.selected);
    }

    get searchContainerInnerClasses() {
        return classSet(
            'search-container-inner transition-max-height background--white full-width border-radius--medium flex flex-direction-column overflow-hidden z-index-999'
        )
            .add({
                'has-results': this.results
            })
            .toString();
    }

    get objectFilterOptions() {
        return Object.values(this.objectFilterOptionsMap);
    }

    setObjectFilterOptions() {
        this.objectFilterOptionsMap = {};
        if (this.data.quotes && this.data.quotes.length > 0) {
            this.objectFilterOptionsMap.quotes = { value: "quotes", label: "Quotes", selected: false, icon: 'quotes.svg'};
        }
        if (this.data.shipments && this.data.shipments.length > 0) {
            this.objectFilterOptionsMap.shipments = { value: "shipments", label: "Shipments", selected: false, icon: 'shipments.svg'};
        }
        if (this.data.entities && this.data.entities.length > 0) {
            this.objectFilterOptionsMap.entities = { value: "entities", label: "Entities", selected: false, icon: 'building_office.svg'};
        }
        if (this.data.sites && this.data.sites.length > 0) {
            this.objectFilterOptionsMap.sites = { value: "sites", label: "Sites", selected: false, icon: 'map_pin.svg'};
        }
        if (this.data.pullRequests && this.data.pullRequests.length > 0) {
            this.objectFilterOptionsMap.pullRequests = {label: 'Pull Requests', value: 'pullRequests', selected: false, count: this.data.pullRequests?.length || 0}
        }
    }

    renderedCallback() {
        this.focusSearchInput();
    }

    handleGetSearchResults() {
        if(!this.searchValue || this.searchValue.length <= 1) {
            if(!this.searchValue) {
                this.data = {};
            }
            this.loading = false;
            this.showNoResults = false;
            this.setObjectFilterOptions();
            return;
        }

        getSearchResults({
            searchValue: this.searchValue
        })
            .then((data) => {
                if (data) {
                    this.data = {...data};
                    this.showNoResults = this.data.results.length === 0;
                }

            })
            .catch((error) => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        label: 'Error',
                        message: 'Problem retrieving Quotes',
                        variant: 'error',
                    })
                );
            })
            .finally(() => {
                this.loading = false;
                this.setObjectFilterOptions();
            });
    }

    handleSearchChange(event) {
        window.clearTimeout(this.debounceSearchTimer);
        this.searchValue = event.target.value

        this.debounceSearchTimer = setTimeout(() => {
            this.loading = true;
            if(this.showNoResults) {
                this.showNoResults = false;
            }
            this.handleGetSearchResults();
        }, 300)
    }

    handleObjectFilter(event) {
        Object.keys(this.objectFilterOptionsMap).forEach((key, index) => {
            let filter = this.objectFilterOptionsMap[key];
            this.objectFilterOptionsMap[key].selected = (filter.value === event.detail.value) ? !filter.selected : false
        });
    }

    handleClearSearch() {
        this.searchValue = '';
        this.data = {};
        this.focusSearchInput();
        this.setObjectFilterOptions();
    }

    handleCloseSearch() {
        publish(this.messageContext, action, {detail: { action: ACTION_MESSAGES.TOGGLE_GLOBAL_SEARCH }});
    }

    focusSearchInput() {
        this.refs.searchInput.focus();
    }
}