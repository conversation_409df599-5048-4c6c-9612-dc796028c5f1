<template>
    <div class="relative max-width-90vw">
        <div class={tableContainerClasses}>
            <table lwc:ref="table" class="table-fixed height-medium block border-collapse--separate"
                   onscroll={handleScroll}>
                <thead>
                <tr class="adjusted-headerRow headerRow align-items-center relative overflow-visible white-background top-sticky">
                    <th class="column"
                        data-field="caseNumber"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp">
                            <p>Shipment Number</p>
                            <template lwc:if={sortArrow.caseNumberShow}>
                                <div>{sortArrowIcon}</div>
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="contactName"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp">
                            <p>Booked By</p>
                            <template lwc:if={sortArrow.contactNameShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="originCountryName"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp">
                            <p>Origin</p>
                            <template lwc:if={sortArrow.originCountryNameShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="destinationCountryName"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp">
                            <p>Destination</p>
                            <template lwc:if={sortArrow.destinationCountryNameShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="clientRef"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp">
                            <p>Reference No.</p>
                            <template lwc:if={sortArrow.clientRefShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="shipmentValue"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp">
                            <p>Shipment Value</p>
                            <template lwc:if={sortArrow.shipmentValueShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="status"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp">
                            <p>Current Status</p>
                            <template lwc:if={sortArrow.statusShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column"
                        data-field="nextStep">
                        <p>{nextStepsLabel}</p>
                    </th>
                    <th class="column"
                        data-field="deliveryETA">
                        <p>{etaForDeliveryLabel}</p>
                    </th>
                    <th class="column"
                        data-field="projectedDeliveryDate"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp width-12em">
                            <p>Projected Delivery Date</p>
                            <template lwc:if={sortArrow.projectedDeliveryDateShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class="column full-width"
                        data-field="actionCount"
                        onmouseenter={setArrow}
                        onmouseleave={setArrow}
                        onclick={handleSort}>
                        <div class="flex flex-gap-xsp plr">
                            <p>Action</p>
                            <template lwc:if={sortArrow.actionCountShow}>
                                {sortArrowIcon}
                            </template>
                        </div>
                    </th>
                    <th class=""></th>
                </tr>
                </thead>
                <tbody>
                    <template for:each={shipments} for:item="shipment">
                        <c-fgx-ent-shipments-table-row
                                key={shipment.Id}
                                shipment={shipment}
                                show-table-shadow={showTableShadow}>
                        </c-fgx-ent-shipments-table-row>
                    </template>
                </tbody>
            </table>
            <c-fgx-ent-loading
                show={loading}
                style-classes="mtl">
            </c-fgx-ent-loading>
        </div>
    </div>
</template>