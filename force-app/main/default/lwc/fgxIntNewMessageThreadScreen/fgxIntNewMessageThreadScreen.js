import { LightningElement, wire } from 'lwc';
import { getObjectInfo, getPicklistValues} from "lightning/uiObjectInfoApi";
import FGX_MESSAGING_OBJ from '@salesforce/schema/FGX_Messaging__c';
import VISIBILITY_FIELD from '@salesforce/schema/FGX_Messaging__c.Visibility__c';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class FgxIntNewMessageThreadScreen extends LightningElement {
    message;
    subject;
    visibilityPicklist;

    @wire(getObjectInfo, { objectApiName: FGX_MESSAGING_OBJ })
    fgxMessagingObjectMetadata;

    @wire(getPicklistValues, { recordTypeId: '$fgxMessagingObjectMetadata.data.defaultRecordTypeId', fieldApiName: VISIBILITY_FIELD})
    getVisibilityPicklist({data,error}) {
        if(data) {
            this.visibilityPicklist = data.values.map(val => ({label: val.label, value: val.value}));
        }
        else if(error) {
            console.error(error);
            this.showToast('Error', 'Error retrieiving visibility picklist', 'error');
        }
    }

    handleSubjectChange(event){
        this.subject = event.detail.value;
    }

    handleSubjectUpdate(){
        this.dispatchEvent(new CustomEvent('subjectupdate', { detail: this.subject }));
    }

    handleNotesChange(event){
        this.message = event.target.value;
    }

    handleNotesUpdate(){
        this.dispatchEvent(new CustomEvent('noteupdate', { detail: this.message }));
    }

    handleVisibilityChange(event){
        this.dispatchEvent(new CustomEvent('visibilityupdate', { detail: event.detail.value }));
    }

    showToast(title, message, variant) {
        const showToastEvent = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
        });
        this.dispatchEvent(showToastEvent);
    }
}