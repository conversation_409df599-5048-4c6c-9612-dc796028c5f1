/**
 * fgxEntQuoteBookingSchemaImport
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 9/27/24
 */

import QUOTE_OBJ from "@salesforce/schema/Quote__c";
import CASE_OBJ from "@salesforce/schema/Case";
import PACKAGE_LINE_OBJ from "@salesforce/schema/Shipment_Package_Line__c";
import BOM_LINE_OBJ from "@salesforce/schema/Commercial_Invoice_Line__c";

import CASE_CURR_CODE from "@salesforce/schema/Case.CurrencyIsoCode";
import CASE_BOM_CURR_CODE from "@salesforce/schema/Case.BOM_Currency_Code__c";
import CASE_CURR_RATE from "@salesforce/schema/Case.BOM_Conversion_Rate__c";
import CASE_CONVERSION_DATE from "@salesforce/schema/Case.BOM_Conversion_Date__c";

import BOM_LINE_UNIT_VALUE from "@salesforce/schema/Commercial_Invoice_Line__c.Value__c";
import BOM_LINE_QUANTITY from "@salesforce/schema/Commercial_Invoice_Line__c.Quantity__c";
import BOM_LINE_CURR_CODE from "@salesforce/schema/Commercial_Invoice_Line__c.CurrencyIsoCode";
import BOM_LINE_CATEGORY from "@salesforce/schema/Commercial_Invoice_Line__c.Category__c";
import BOM_LINE_CONDITION from "@salesforce/schema/Commercial_Invoice_Line__c.Condition__c";
import BOM_LINE_SHIPMENT from "@salesforce/schema/Commercial_Invoice_Line__c.Shipment__c";
import BOM_LINE_PRODUCT_TYPE from "@salesforce/schema/Commercial_Invoice_Line__c.Product_Type__c";
import BOM_LINE_MANUFACTURER from "@salesforce/schema/Commercial_Invoice_Line__c.Manufacturer__c";
import BOM_LINE_CREATED_BY_TYPE from "@salesforce/schema/Commercial_Invoice_Line__c.Created_By_Type__c";
import BOM_LINE_SORT_ORDER from "@salesforce/schema/Commercial_Invoice_Line__c.SortOrder__c";

import PACKAGE_LINE_UNIT_WEIGHT from '@salesforce/schema/Shipment_Package_Line__c.Unit_Weight__c';
import PACKAGE_LINE_QUANTITY from '@salesforce/schema/Shipment_Package_Line__c.Num_Pcs__c';
import PACKAGE_LINE_WEIGHT_UNIT from '@salesforce/schema/Shipment_Package_Line__c.Weight_Unit__c';
import PACKAGE_LINE_LENGTH_UNIT from '@salesforce/schema/Shipment_Package_Line__c.Length_Unit__c';
import PACKAGE_LINE_SHIPMENT from "@salesforce/schema/Shipment_Package_Line__c.Shipment__c";

import SUMMARY from "@salesforce/schema/Case.Summary__c";
import CLIENT_REF from "@salesforce/schema/Case.Client_Ref__c";
import CLIENT_REF2 from "@salesforce/schema/Case.Client_Ref2__c";

import ORIGIN_SITE from "@salesforce/schema/Case.Origin_Site__c";
import TARGET_DELIVERY_DATE from "@salesforce/schema/Case.Target_Date__c";

import ORIGIN_ADDRESS_LINE_1 from '@salesforce/schema/Case.Origin_Address_Line_1__c';
import ORIGIN_ADDRESS_LINE_2 from '@salesforce/schema/Case.Origin_Address_Line_2__c';
import ORIGIN_ADDRESS_LINE_3 from '@salesforce/schema/Case.Origin_Address_Line_3__c';
import ORIGIN_CITY from '@salesforce/schema/Case.Origin_City__c';
import ORIGIN_POST_CODE from '@salesforce/schema/Case.Origin_Post_Code__c';
import ORIGIN_STATE from '@salesforce/schema/Case.Origin_State__c';
import ORIGIN_STATE_US from '@salesforce/schema/Case.Origin_US_State__c';
import ORIGIN_COUNTRY from '@salesforce/schema/Case.Origin_Country__c';
import ORIGIN_COUNTY_PROVINCE_OTHER from '@salesforce/schema/Case.Origin_County_Province_Other__c';
import ORIGIN_COMPANY_NAME from '@salesforce/schema/Case.Origin_Company_Name__c';
import ORIGIN_FIRST_NAME from '@salesforce/schema/Case.Origin_First_Name__c';
import ORIGIN_LAST_NAME from '@salesforce/schema/Case.Origin_Last_Name__c';
import ORIGIN_EMAIL from '@salesforce/schema/Case.Origin_Email__c';
import ORIGIN_MOBILE_PHONE from '@salesforce/schema/Case.Origin_Mobile_Phone__c';
import ORIGIN_SEARCH_CHOICE from '@salesforce/schema/Case.Origin_Search_Choice__c';
import OUTBOUND_TICKET_NUM from '@salesforce/schema/Case.Outbound_Ticket_Num__c';
import INBOUND_TICKET_NUM from '@salesforce/schema/Case.Inbound_Ticket_Num__c';
import EXPORT_ENTITY from '@salesforce/schema/Case.Export_Entity__c';
import IMPORT_ENTITY from '@salesforce/schema/Case.Import_Entity__c';
import DROP_SHIP_FGX from '@salesforce/schema/Case.Drop_Ship_to_FGX__c';
import SERVICE_LEVEL from "@salesforce/schema/Case.Service_Level__c";

import DESTINATION_SITE from "@salesforce/schema/Case.Destination_Site__c";
import DESTINATION_ADDRESS_LINE_1 from '@salesforce/schema/Case.Destination_Address_Line_1__c';
import DESTINATION_ADDRESS_LINE_2 from '@salesforce/schema/Case.Destination_Address_Line_2__c';
import DESTINATION_ADDRESS_LINE_3 from '@salesforce/schema/Case.Destination_Address_Line_3__c';
import DESTINATION_CITY from '@salesforce/schema/Case.Destination_City__c';
import DESTINATION_POST_CODE from '@salesforce/schema/Case.Destination_Post_Code__c';
import DESTINATION_STATE from '@salesforce/schema/Case.Destination_State__c';
import DESTINATION_STATE_US from '@salesforce/schema/Case.Destination_US_State__c';
import DESTINATION_COUNTRY from '@salesforce/schema/Case.Destination_Country__c';
import DESTINATION_COUNTY_PROVINCE_OTHER from '@salesforce/schema/Case.Destination_County_Province_Other__c';
import DESTINATION_COMPANY_NAME from '@salesforce/schema/Case.Destination_Company_Name__c';
import DESTINATION_FIRST_NAME from '@salesforce/schema/Case.Destination_First_Name__c';
import DESTINATION_LAST_NAME from '@salesforce/schema/Case.Destination_Last_Name__c';
import DESTINATION_EMAIL from '@salesforce/schema/Case.Destination_Email__c';
import DESTINATION_MOBILE_PHONE from '@salesforce/schema/Case.Destination_Mobile_Phone__c';
import DESTINATION_SEARCH_CHOICE from '@salesforce/schema/Case.Destination_Search_Choice__c';

import BOM_CSV_INGESTED from '@salesforce/schema/Case.BOM_CSV_Ingested__c';
import BOM_AI_INGESTED from '@salesforce/schema/Case.BOM_AI_Ingested__c';
import TRANSACTION_TYPE from "@salesforce/schema/Case.Transaction_Type__c";
import RELATED_ENTITY from "@salesforce/schema/Case.TxnSurvey_Related_Entity__c";
import ASSET_TRANSFER from "@salesforce/schema/Case.TxnSurvey_Asset_Transfer__c";
import FUNDS_TRANSFER from "@salesforce/schema/Case.TxnSurvey_Funds_Transfer__c";
import PURCHASING_ENTITY from "@salesforce/schema/Case.Purchasing_Entity__c";
import ULTIMATE_OWNER_ENTITY from "@salesforce/schema/Case.Ultimate_Owner_Entity__c";
import BILLING_ACCT from "@salesforce/schema/Case.Billing_Override_Account__c";
import STATUS from "@salesforce/schema/Case.Flow_Status__c";
import PACKING_CONFIG from "@salesforce/schema/Case.Current_Packaging__c";
import DIM_WEIGHT_ENTRY_TYPE from "@salesforce/schema/Case.DIMWeightEntryType__c";

import ENTITY_EXPORT_STATUS from '@salesforce/schema/Entity__c.Export_Status__c';
import ENTITY_IMPORT_STATUS from '@salesforce/schema/Entity__c.Import_Status__c';

import COUNTRY_CUSTOMSCODE_LABEL from '@salesforce/schema/Countries__c.CustomsCode_Label__c';
import COUNTRY_CODE from '@salesforce/schema/Countries__c.Code__c';
import COUNTRY_NAME from '@salesforce/schema/Countries__c.Name';

import ACCOUNT_ADV_LOGISTICS from "@salesforce/schema/Account.Advanced_Logistics_Mode__c";
import ACCOUNT_ADV_COMMERCIALIZATION from "@salesforce/schema/Account.Advanced_Commercialization_Mode__c";
import ACCOUNT_ADV_BILLING from "@salesforce/schema/Account.Advanced_Billing_Mode__c";

import SITE_COUNTRY from '@salesforce/schema/Site__c.Country__c';
import SITE_CITY from '@salesforce/schema/Site__c.City__c';
import SITE_CONTACT_FIRST_NAME from '@salesforce/schema/Site__c.Site_Contact_First_Name__c';
import SITE_CONTACT_LAST_NAME from '@salesforce/schema/Site__c.Site_Contact_Last_Name__c';
import SITE_CONTACT_EMAIL from '@salesforce/schema/Site__c.Site_Contact_Email__c';
import SITE_CONTACT_PHONE from '@salesforce/schema/Site__c.Site_Contact_Phone__c';

const SCHEMA = {
    QUOTE_OBJ,
    CASE_OBJ,
    PACKAGE_LINE_OBJ,
    BOM_LINE_OBJ,
    CASE_CURR_CODE,
    CASE_BOM_CURR_CODE,
    CASE_CURR_RATE,
    CASE_CONVERSION_DATE,
    BOM_LINE_UNIT_VALUE,
    BOM_LINE_QUANTITY,
    BOM_LINE_CURR_CODE,
    BOM_LINE_CATEGORY,
    BOM_LINE_CONDITION,
    BOM_LINE_SHIPMENT,
    BOM_LINE_PRODUCT_TYPE,
    BOM_LINE_MANUFACTURER,
    BOM_LINE_CREATED_BY_TYPE,
    BOM_LINE_SORT_ORDER,
    PACKAGE_LINE_UNIT_WEIGHT,
    PACKAGE_LINE_QUANTITY,
    PACKAGE_LINE_WEIGHT_UNIT,
    PACKAGE_LINE_LENGTH_UNIT,
    PACKAGE_LINE_SHIPMENT,
    SUMMARY,
    CLIENT_REF,
    CLIENT_REF2,
    ORIGIN_SITE,
    TARGET_DELIVERY_DATE,
    ORIGIN_ADDRESS_LINE_1,
    ORIGIN_ADDRESS_LINE_2,
    ORIGIN_ADDRESS_LINE_3,
    ORIGIN_CITY,
    ORIGIN_POST_CODE,
    ORIGIN_STATE,
    ORIGIN_STATE_US,
    ORIGIN_COUNTRY,
    ORIGIN_COUNTY_PROVINCE_OTHER,
    ORIGIN_COMPANY_NAME,
    ORIGIN_FIRST_NAME,
    ORIGIN_LAST_NAME,
    ORIGIN_EMAIL,
    ORIGIN_MOBILE_PHONE,
    ORIGIN_SEARCH_CHOICE,
    OUTBOUND_TICKET_NUM,
    INBOUND_TICKET_NUM,
    EXPORT_ENTITY,
    IMPORT_ENTITY,
    DROP_SHIP_FGX,
    SERVICE_LEVEL,
    DESTINATION_SITE,
    DESTINATION_ADDRESS_LINE_1,
    DESTINATION_ADDRESS_LINE_2,
    DESTINATION_ADDRESS_LINE_3,
    DESTINATION_CITY,
    DESTINATION_POST_CODE,
    DESTINATION_STATE,
    DESTINATION_STATE_US,
    DESTINATION_COUNTRY,
    DESTINATION_COUNTY_PROVINCE_OTHER,
    DESTINATION_COMPANY_NAME,
    DESTINATION_FIRST_NAME,
    DESTINATION_LAST_NAME,
    DESTINATION_EMAIL,
    DESTINATION_MOBILE_PHONE,
    DESTINATION_SEARCH_CHOICE,
    BOM_CSV_INGESTED,
    BOM_AI_INGESTED,
    TRANSACTION_TYPE,
    RELATED_ENTITY,
    ASSET_TRANSFER,
    FUNDS_TRANSFER,
    PURCHASING_ENTITY,
    ULTIMATE_OWNER_ENTITY,
    BILLING_ACCT,
    STATUS,
    PACKING_CONFIG,
    DIM_WEIGHT_ENTRY_TYPE,
    ENTITY_EXPORT_STATUS,
    ENTITY_IMPORT_STATUS,
    COUNTRY_CUSTOMSCODE_LABEL,
    COUNTRY_CODE,
    COUNTRY_NAME,
    ACCOUNT_ADV_LOGISTICS,
    ACCOUNT_ADV_COMMERCIALIZATION,
    ACCOUNT_ADV_BILLING,
    SITE_COUNTRY,
    SITE_CITY,
    SITE_CONTACT_FIRST_NAME,
    SITE_CONTACT_LAST_NAME,
    SITE_CONTACT_EMAIL,
    SITE_CONTACT_PHONE
}

export default SCHEMA;