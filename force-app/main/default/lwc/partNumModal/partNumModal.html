<!-- sldsValidatorIgnore -->
<template>
    <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01" class="slds-modal slds-fade-in-open slds-modal_small" style="overflow-y:auto">
        <div class="slds-modal__container">
            <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse">
                <lightning-icon icon-name="utility:close" variant="inverse" alternative-text="Close" title="Close" onclick={closeHandler}></lightning-icon>
            </button>
            <div class="slds-modal__header">
                <h1 id="modal-heading-01" class="slds-modal__title slds-hyphenate">Edit Part Number</h1>
            </div>
                
            <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">

                <!-- Instructions -->
                <div class="slds-col slds-size_3-of-3 slds-p-around_medium">
                    <p>{instructions}</p>
                </div>
                
                <form>
                    <div class="slds-grid slds-wrap">
                        <div class="slds-col slds-size_1-of-2">
                            <div class="slds-p-around_medium lgc-bg">
                                <p>Uploaded Part Number:</p>
                                <input type='radio' class="partNum" name="partNum" value={uploadedPartNumber} checked></input>
                                <label for={uploadedPartNumber}>{uploadedPartNumber}</label><br>
                            </div>
                        </div>

                        <!-- first time, display initial data -->
                        <div class="slds-col slds-size_1-of-2">
                            <div class="slds-p-around_medium">
                                <p>Similar Existing Part Numbers:</p>
                                <template for:each={similarPartNumbers} for:item="part">
                                    <div key={part}>
                                        <input type='radio' name="partNum" class="partNum" value={part}></input>
                                        <label for={part}>{part}</label><br>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="slds-modal__footer slds-modal__footer_directional">
                <lightning-button class="slds-button" aria-label="Cancel and close" onclick={closeHandler} title="Cancel" label="Cancel">
                </lightning-button>
                <lightning-button variant="brand" type="submit" class="slds-button submit_button" title="Next" label="Next" 
                onclick={submitHandler} disabled={submitDisabled}></lightning-button>
            </div>
        </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open" role="presentation"></div>
</template>