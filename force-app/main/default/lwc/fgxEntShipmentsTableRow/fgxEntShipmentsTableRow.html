<template>
    <td class="bodyRowCell">
        <div class="flex flex-gap-sp align-items-center hover-primary-color">
            <div class="flex align-items-center">
                <button class="button_expandButton flex flex-gap-sp columnContent"
                        onclick={handleViewShipment}>
                    <div class="hover-primary-color">
                        <p title={caseNumber}>{caseNumber}</p>
                    </div>
                    <img loading="lazy" width="13" height="13" decoding="async" data-nimg="1" src={expandIcon}
                         style="color: transparent;">
                </button>
            </div>
        </div>
    </td>
    <td class="bodyRowCell">
        <p title={contactName}>{contactName}</p>
    </td>
    <td class="bodyRowCell">
        <div class="flex flex-gap-sp">
            <img src={originFlagImgURL} height="20" width="20"/>
            <p title={originCountry} class="p fifth-neutral-color">{originCountry}</p>
        </div>
    </td>
    <td class="bodyRowCell">
        <div class="flex flex-gap-sp">
            <img src={destinationFlagImgURL} height="20" width="20"/>
            <p title={destinationCountry} class="p fifth-neutral-color">{destinationCountry}</p>
        </div>
    </td>
    <td class={refCellClasses}>
        <p title={referenceNo}>{referenceNo}</p>
    </td>
    <td class="bodyRowCell">
        <p title={shipmentValue}>{shipmentValue}</p>
    </td>
    <td class="bodyRowCell">
        <p title={status}>{status}</p>
    </td>
    <td class={nextStepCellClasses}>
        <p title={nextStep}>{nextStep}</p>
    </td>
    <td class={deliveryEtaCellClasses}>
        <p title={deliveryETA}>{deliveryETA}</p>
    </td>
    <td class="bodyRowCell">
        <p title={projectedDeliveryDate}>{projectedDeliveryDate}</p>
    </td>
    <td>
        <div class="flex flex-gap-sp align-items-center justify-content-flex-start full-height truncate-overflow">
            <template lwc:if={isCompletedShipment}>
                <c-fgx-ent-button
                        type="secondary"
                        label="View POD"
                        full-width="true"
                        onclick={handleViewPOD}>
                </c-fgx-ent-button>
            </template>
            <template lwc:if={actionItems}>
                <c-fgx-ent-button
                    type="warning"
                    label="Action Required"
                    full-width="true"
                    onclick={handleViewAction}>
                </c-fgx-ent-button>
            </template>
        </div>
    </td>
    <td class="right-sticky">
        <div class="flex align-items-center justify-content-flex-end full-width">
            <c-fgx-ent-actions-menu
                    record-id={shipment.Id}
                    show-shadow={showTableShadow}
                    style-classes="shadow-left">
                <c-fgx-ent-button
                        type="text"
                        label="View"
                        text-class="p p--black pointer"
                        onclick={handleViewShipment}>
                </c-fgx-ent-button>
                <c-fgx-ent-link
                        link-label="Clone"
                        link-type="text"
                        link-text-class="p p--black pointer"
                        target={QUOTE_BOOKING}
                        params={cloneNavigationParams}>
                </c-fgx-ent-link>
            </c-fgx-ent-actions-menu>
        </div>
    </td>
</template>