/**
 * FgxEntConstants
 * @description: Constants class to centralize
 * consistently used event names as well as field
 * names and text constants
 * @author: <PERSON><PERSON>
 * @date: 08/02/23
 */
class FgxEntConstants { }

import ENTITY_OBJECT from '@salesforce/schema/Entity__c';
import SITE_OBJECT from '@salesforce/schema/Site__c';
import QUOTE_OBJECT from '@salesforce/schema/Quote__c';
import CASE_OBJECT from '@salesforce/schema/Case';

const getConstants = () =>{
    return {
        CASE_STATUS_RECEIVED: 'Received @ FGX',
        CASE_STATUS_PENDING_MANIFEST: 'Pending Manifest',
        CASE_STATUS_PENDING_DOC_APPROVAL: 'Pending Doc Approval',
        CASE_STATUS_PENDING_PERMIT: 'Pending Permit',
        CASE_STATUS_IN_TRANSIT: 'In Transit',
        CASE_STATUS_CLEARANCE_PROCESSING: 'Clearance Processing',
        CASE_STATUS_PERMIT_PROCESSING: 'Permit Processing',
        CASE_STATUS_OUT_FOR_DELIVERY: 'Out for Delivery',
        CASE_STATUS_BOOKED: 'Booked',
        CASE_STATUS_ENROUTE: 'Enroute to FGX',
        CASE_STATUS_SCHEDULING_PICKUP: 'Scheduling Pickup',
        CASE_STATUS_CRATING: 'Crating/Packing',
        CASE_STATUS_PENDING_PAYMENT: 'Pending Payment',
        CASE_STATUS_HOLD: 'On Hold',
        CASE_STATUS_PENDING_CLIENT: 'Pending Client Action',
        CASE_STATUS_QUOTE_REQUEST: 'Quote Request',
        CASE_STATUS_QUOTED: 'Quoted',
        CASE_STATUS_DELIVERED: 'Delivered',
        CASE_STATUS_CLOSED: 'Closed',
        CASE_MANIFEST_STATUS_READY_TO_MANIFEST: 'Ready to Manifest',
        CASE_MANIFEST_STATUS_IN_PROGRESS: 'In Progress',
        CASE_MANIFEST_STATUS_PENDING_REVIEW: 'Pending Review',
        CASE_MANIFEST_STATUS_COMPLETED: 'Completed',
        RECORD_STATUS_IN_DRAFT: 'In Draft',
        RECORD_STATUS_PENDING_REVIEW: 'Pending Review',
        RECORD_STATUS_PENDING_APPROVAL: 'Pending Approval',
        RECORD_STATUS_SUBMITTED: 'Submitted',
        RECORD_STATUS_APPROVED: 'Approved',
        RECORD_STATUS_NOTNULL: 'NOTNULL',
        FLOW_STEP_FIELD: 'Flow_Step__c',
        FLOW_STATUS_FIELD: 'Flow_Status__c',
        ACTION: {
            STEPCHANGE: 'stepchange',
            EXIT: 'exit'
        },
        SOBJECTTYPE: 'sobjectType',
        STATE_TO_ABBRV: {
            "alabama": "AL",
            "alaska": "AK",
            "arizona": "AZ",
            "arkansas": "AR",
            "california": "CA",
            "colorado": "CO",
            "connecticut": "CT",
            "delaware": "DE",
            "florida": "FL",
            "georgia": "GA",
            "hawaii": "HI",
            "idaho": "ID",
            "illinois": "IL",
            "indiana": "IN",
            "iowa": "IA",
            "kansas": "KS",
            "kentucky": "KY",
            "louisiana": "LA",
            "maine": "ME",
            "maryland": "MD",
            "massachusetts": "MA",
            "michigan": "MI",
            "minnesota": "MN",
            "mississippi": "MS",
            "missouri": "MO",
            "montana": "MT",
            "nebraska": "NE",
            "nevada": "NV",
            "new hampshire": "NH",
            "new jersey": "NJ",
            "new mexico": "NM",
            "new york": "NY",
            "north carolina": "NC",
            "north dakota": "ND",
            "ohio": "OH",
            "oklahoma": "OK",
            "oregon": "OR",
            "pennsylvania": "PA",
            "rhode island": "RI",
            "south carolina": "SC",
            "south dakota": "SD",
            "tennessee": "TN",
            "texas": "TX",
            "utah": "UT",
            "vermont": "VT",
            "virginia": "VA",
            "washington": "WA",
            "west Virginia": "WV",
            "wisconsin": "WI",
            "wyoming": "WY"
        },
        VARIANT: {
            MODIFY: 'modify',
            UPDATE: 'update',
            CLONE: 'clone',
            NEW: 'new',
        },
        SERVICE_LEVEL_ECONOMY: 'EconomyIT',
        SERVICE_LEVEL_PRIORITY: 'PriorityIT',
        MANIFEST_STATUS_FULLY_MANIFESTED: 'Fully Manifested',
        MANIFEST_STATUS_FULLY_MANIFESTED_EXCEPTIONS: 'Fully Manifested with Exceptions',
        MANIFEST_STATUS_PARTIALLY_RECEIVED: 'Partially Received',
        MANIFEST_STATUS_MISSING_QUANTITY: 'Manifested - Missing BOM Quantity',
        MANIFEST_STATUS_LOW_QUANTITY: 'Low Quantity',
        MANIFEST_STATUS_SURPLUS: 'Surplus Quantity',
        MANIFEST_STATUS_ABSENT: 'Absent',
        MANIFEST_STATUS_CONFIG_ITEM: 'Config Item',
        MANIFEST_STATUS_UNEXPECTED: 'Unexpected',
        WHI_STATUS_ENROUTE: 'Enroute to FGX',
        WHI_STATUS_CHECKED_IN: 'Checked In',
        WHI_STATUS_DEPARTED: 'Departed',
        WHI_STATUS_OFFSITE: 'Offsite Storage',
        UNACCEPTABLE_CONTENTS_DESCRIPTION: ['Consol', 'Consolidated', 'Consolidation', 'General Cargo', 'Cables', 'IT Equipment', 'I.T. Equipment', 'IT Goods', 'Electronic Equipment', 'Electronic Goods', 'Electronic Items', 'Spare Parts', 'Technology'],
        OUTBOUND_MANIFEST_RT_DIRECT: 'Direct',
        OUTBOUND_MANIFEST_RT_CONSOLIDATED: 'Consolidation'
    }
}

const getEvents = () =>{
    return {
        RECORD_CHANGE_EVT: 'recordchange',
        STEP_CHANGE_EVT: 'stepchange',
        CONFIRM_EVT: 'confirmevt',
        CLOSE_EVT: 'close',
        REFRESH_EVT: 'refresh',
        ACTION_EVT: 'action',
        FINISH_EVT: 'finished',
        SUBMIT_EVT: 'submit',
        RENDERED_EVT: 'rendered',
        TAB_CHANGE_EVT: 'tabchange'
    }
}

const getRenderMessages = () =>{
    return {
        MULTI_TAB_RENDERED: 'multi-tab-rendered',
        SECTION_POPUP_RENDERED: 'section-popup-rendered',
    }
}

const getActionMessages = () =>{
    return {
        EXPAND_ACTION: 'expand',
        COLLAPSE_ACTION:  'collapse',
        VALDITY_CHECK_ACTION: 'check-validity',
        TOGGLE_GLOBAL_SEARCH: 'toggle-global-search',
        SCROLL_TOP: 'scroll-top'
    }
}

const getObjectToPageTarget = () => {
    return {
        [ENTITY_OBJECT.objectApiName]: getPages().ENTITYDETAIL,
        [SITE_OBJECT.objectApiName]: getPages().SITEDETAIL,
        [CASE_OBJECT.objectApiName]: getPages().SHIPMENTDETAIL,
        [QUOTE_OBJECT.objectApiName]: getPages().QUOTEDETAIL
    }
}

const getPages = () => {
    return {
        HOME: 'Home',
        USERS: 'users',
        PROFILE: 'profile',
        ENTITIES: 'entities',
        QUOTES: 'quotes',
        SITES: 'sites',
        SHIPMENTS: 'shipments',
        INVENTORY: 'inventory',
        PULLREQUESTS: 'pullrequests',
        ECOSYSTEM: 'ecosystem',
        ORGANIZATION: 'organization',
        PARTNERS: 'partners',
        COMPLIANCE: 'compliance',
        SETTINGS: 'settings',
        ACTIONITEMS: 'actionitems',
        QUOTEREQUEST: 'quoterequest',
        QUOTEBOOKING: 'shipmentbooking',
        QUOTEDETAIL: 'quotedetail',
        ENTITYDETAIL: 'entitydetail',
        SITEDETAIL: 'sitedetail',
        SHIPMENTDETAIL: 'shipmentdetail',
        INVENTORYMANAGER: 'inventorymanager',
        PULLREQUESTDETAIL: 'pullrequestdetail',
        REPORTS: 'reports',
        REPORTDETAIL: 'reportdetail'
    }
}

const getQuoteBreakdown = () =>{
    return {
        INSURANCE: 'Insurance',
        TAX: 'Tax',
        DUTY: 'Duty',
        DTBUFFER: 'DTBuffer',
        OUTLAY: 'Outlay',
        IOREOR: 'IOR/EOR',
        PICKUP: 'Pickup',
        PACKING: 'Packing',
        EXPCLEARANCE: 'ExpClearance',
        AIRFREIGHT: 'AirFreight',
        CLEARANCE: 'Clearance',
        DELIVERY: 'Delivery',
        PERMIT: 'Permit',
        MISC: 'Misc',
        AFTERTOTAL: 'AfterTotal'
    }
}

const Linktypes = {
    Dropdown: 'dropdown',
    SubItem: 'subitem',
    Link: 'link'
}

export { getConstants, getEvents, getRenderMessages, getActionMessages, getPages, getObjectToPageTarget, getQuoteBreakdown, Linktypes};