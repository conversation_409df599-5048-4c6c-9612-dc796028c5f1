import { LightningElement, api, track, wire } from 'lwc';
import { refreshApex } from '@salesforce/apex';
import { updateRecord, getRecord } from 'lightning/uiRecordApi';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { getConstants } from 'c/fgxEntConstants';
import getRelatedCases from '@salesforce/apex/OutboundManifestDetailsController.getRelatedCases';
import publishRefreshEvent from '@salesforce/apex/OutboundManifestDetailsController.publishRefreshEvent';
import ORIGIN_FREIGHT_AGENT from '@salesforce/schema/Outbound_Manifest__c.Origin_Freight_Agent__c';
import DESTINATION_FREIGHT_AGENT from '@salesforce/schema/Outbound_Manifest__c.Destination_Freight_Agent__c';

const MANIFEST_FIELDS = [
    'Outbound_Manifest__c.Shipper_Consignment_Block__c',
    'Outbound_Manifest__c.Consignee_Consignment_Block__c',
    'Outbound_Manifest__c.Origin_Freight_Agent__c',
    'Outbound_Manifest__c.Destination_Freight_Agent__c',
    'Outbound_Manifest__c.Origin_Freight_Agent_Tax_Code__c',
    'Outbound_Manifest__c.Destination_Freight_Agent_Tax_Code__c',
    'Outbound_Manifest__c.Notify_Party__c',
    'Outbound_Manifest__c.RecordTypeId'
];

const ORIGIN_TAX_CODE_FIELD = {
    fieldApiName: 'Origin_Freight_Agent_Tax_Code__c'
};
const DESTINATION_TAX_CODE_FIELD = {
    fieldApiName: 'Destination_Freight_Agent_Tax_Code__c'
};

const CONSTANTS = getConstants();

const DIRECT_RECORD_TYPE_DEV_NAME = CONSTANTS .OUTBOUND_MANIFEST_RT_DIRECT;
const CONSOLIDATED_RECORD_TYPE_DEV_NAME = CONSTANTS .OUTBOUND_MANIFEST_RT_CONSOLIDATED;

export default class OutboundManifestConsignDetailsTab extends LightningElement {
    @api recordId;
    @track recordTypeId;
    @track recordTypeName;
    @track caseNumber = '';
    @track relatedCases = [];
    @track showFreightAgentMismatch = false;
    @track showMissingEntityAlert = false;
    @track originFreightAgent;
    @track destinationFreightAgent;
    @track exportingEntity = '';
    @track importingEntity = '';
    @track shipperConsignmentBlock = '';
    @track consigneeConsignmentBlock = '';
    @track showConsignmentDetails = true;
    @track originTaxCode = '';
    @track destinationTaxCode = '';
    @track notifyParty = '';
    @track showNotifyParty = true;
    @track isLoading = false;
    wiredManifestResult;

    //Timer variables - for automatic save/update
    taxCodeTimer;
    notifyPartyTimer;

    //Load manifest record data/track reactive changes
    @wire(getRecord, { recordId: '$recordId', fields: MANIFEST_FIELDS })
    wiredManifest(result) {
        this.wiredManifestResult = result;
        const { data, error } = result;
        
        if (data) {
            //Update if there's new data
            if (!this.recordTypeId || this.recordTypeId !== data.fields.RecordTypeId.value) {
                this.recordTypeId = data.fields.RecordTypeId.value;
                this.recordTypeName = data.recordTypeInfo?.name;
            }
            
            //Maintain existing values if new ones are empty
            this.shipperConsignmentBlock = data.fields.Shipper_Consignment_Block__c?.value || this.shipperConsignmentBlock;
            this.consigneeConsignmentBlock = data.fields.Consignee_Consignment_Block__c?.value || this.consigneeConsignmentBlock;
            this.originFreightAgent = data.fields.Origin_Freight_Agent__c?.value || this.originFreightAgent;
            this.destinationFreightAgent = data.fields.Destination_Freight_Agent__c?.value || this.destinationFreightAgent;
            this.originTaxCode = data.fields.Origin_Freight_Agent_Tax_Code__c?.value || this.originTaxCode;
            this.destinationTaxCode = data.fields.Destination_Freight_Agent_Tax_Code__c?.value || this.destinationTaxCode;
            this.notifyParty = data.fields.Notify_Party__c?.value || this.notifyParty;
            
            //Only load cases if they haven't loaded already
            if (this.relatedCases.length === 0) {
                this.loadRelatedCases();
            }
        } else if (error) {
            this.showToast('Error', 'Error loading manifest data', 'error');
        }
    }

    //HTML Labels
    get exportingEntityLabel() {
        return `Exporting Entity (defined on HAWB: ${this.caseNumber})`;
    }
    
    get importingEntityLabel() {
        return `Importing Entity (defined on HAWB: ${this.caseNumber})`;
    }

    get consignmentDetailsIcon() {
        return this.showConsignmentDetails ? 'utility:chevrondown' : 'utility:chevronright';
    }

    get notifyPartyIcon() {
        return this.showNotifyParty ? 'utility:chevrondown' : 'utility:chevronright';
    }

    //Record type checks
    get isDirect() {
        return this.recordTypeName === DIRECT_RECORD_TYPE_DEV_NAME;
    }

    get isConsolidated() {
        return this.recordTypeName === CONSOLIDATED_RECORD_TYPE_DEV_NAME;
    }

    //Collapsable tabs
    toggleConsignmentDetails() {
        this.showConsignmentDetails = !this.showConsignmentDetails;
    }

    toggleNotifyParty() {
        this.showNotifyParty = !this.showNotifyParty;
    }

    //Call apex to get details from associated cases
    //Determine if Direct/Consolidated
    async loadRelatedCases() {
        this.isLoading = true;
        try {
            this.relatedCases = await getRelatedCases({ manifestId: this.recordId });
            
            if (this.isDirect) {
                this.handleDirectRecordType();
            } else if (this.isConsolidated) {
                this.checkFreightAgentConsistency();
            }
        } catch (error) {
            this.showToast('Error', 'Failed to load related cases', 'error');
        } finally {
            this.isLoading = false;
        }
    }

    //Direct - only 1 case - default: origin/destination freight agent, export/import entity from case
    async handleDirectRecordType() {
        if (this.relatedCases.length > 0) {
            const singleCase = this.relatedCases[0];
            
            //Update all component properties from the case
            this.caseNumber = singleCase.CaseNumber;
            this.exportingEntity = singleCase.Agent_Export_Entity__r?.Legal_Name__c || '';
            this.importingEntity = singleCase.Agent_Import_Entity__r?.Legal_Name__c || '';
            this.showMissingEntityAlert = !this.exportingEntity || !this.importingEntity;
    
            //Store previous values for comparison
            const previousValues = {
                shipper: this.shipperConsignmentBlock,
                consignee: this.consigneeConsignmentBlock,
                originTax: this.originTaxCode,
                destinationTax: this.destinationTaxCode
            };
    
            //Update all fields from case data 
            this.shipperConsignmentBlock = singleCase.RS_Output_Exporter_Consignment__c || '';
            this.consigneeConsignmentBlock = singleCase.RS_Output_Importer_Consignment__c || '';
            this.originTaxCode = singleCase.RS_Output_Exporter_TaxID__c || '';
            this.destinationTaxCode = singleCase.RS_Output_Importer_TaxID__c || '';
    
            try {
                //Prepare fields for update
                const fieldsToUpdate = {
                    Shipper_Consignment_Block__c: this.shipperConsignmentBlock,
                    Consignee_Consignment_Block__c: this.consigneeConsignmentBlock,
                    Origin_Freight_Agent_Tax_Code__c: this.originTaxCode,
                    Destination_Freight_Agent_Tax_Code__c: this.destinationTaxCode
                };
                
                await updateRecord({
                    fields: {
                        Id: this.recordId,
                        ...fieldsToUpdate
                    }
                });
                publishRefreshEvent();
    
                //Force complete refresh
                await refreshApex(this.wiredManifestResult);
                this.relatedCases = await getRelatedCases({ manifestId: this.recordId });
                
                //UI update
                await this.refreshUI();
                
            } catch (error) {
                console.error('Update error:', error);
                this.showToast('Error', 'Failed to update manifest details', 'error');
            }
        } else {
            console.warn('No related cases found');
            this.showToast('Warning', 'No related shipments found', 'warning');
        }
    }
    
    async refreshUI() {
        //Re-render all fields
        this.shipperConsignmentBlock = this.shipperConsignmentBlock;
        this.consigneeConsignmentBlock = this.consigneeConsignmentBlock;
        this.originTaxCode = this.originTaxCode;
        this.destinationTaxCode = this.destinationTaxCode;
        
        //Force template binding updates
        await Promise.resolve();
        
        //Update input elements
        const elements = {
            shipperTextarea: this.refs.shipperTextarea,
            consigneeTextarea: this.refs.consigneeTextarea,
            originTaxInput: this.refs.originTaxInput,
            destTaxInput: this.refs.destTaxInput
        };
    }

    //Checks to see if consolidated shipment cases have same origin/destination freight agents 
    //Must be the same to proceed
    checkFreightAgentConsistency() {
        if (this.relatedCases.length === 0) return;
        
        const originAgents = new Set();
        const destinationAgents = new Set();
        const mismatchedCases = [];
        
        this.relatedCases.forEach(c => {
            if (c.Origin_Freight_Agent__c) originAgents.add(c.Origin_Freight_Agent__c);
            if (c.Destination_Freight_Agent__c) destinationAgents.add(c.Destination_Freight_Agent__c);
            
            //Track cases with mismatches - prep to throw error
            if (originAgents.size > 1 || destinationAgents.size > 1) {
                mismatchedCases.push(c.CaseNumber);
            }
        });
        
        this.showFreightAgentMismatch = originAgents.size > 1 || destinationAgents.size > 1;
    }

    async updateRecordFields(fields) {
        try {
            const recordInput = { 
                fields: {
                    Id: this.recordId,
                    ...fields
                }
            };
            
            await updateRecord(recordInput);
            publishRefreshEvent();
            await refreshApex(this.wiredManifestResult);
            
            //Update local values
            if (fields.Shipper_Consignment_Block__c !== undefined) {
                this.shipperConsignmentBlock = fields.Shipper_Consignment_Block__c;
            }
            if (fields.Consignee_Consignment_Block__c !== undefined) {
                this.consigneeConsignmentBlock = fields.Consignee_Consignment_Block__c;
            }
            if (fields.Origin_Freight_Agent_Tax_Code__c !== undefined) {
                this.originTaxCode = fields.Origin_Freight_Agent_Tax_Code__c;
            }
            if (fields.Destination_Freight_Agent_Tax_Code__c !== undefined) {
                this.destinationTaxCode = fields.Destination_Freight_Agent_Tax_Code__c;
            }
        } catch (error) {
            console.error('Error updating record fields:', error);
            throw error;
        }
    }

    async syncFromHAWB() {
        if (this.relatedCases.length === 0) return;
        
        this.isLoading = true;
        try {
            const singleCase = this.relatedCases[0];
            
            if (this.isDirect) {
                //For Direct records, map specific Case fields
                this.shipperConsignmentBlock = singleCase.RS_Output_Exporter_Consignment__c || '';
                this.consigneeConsignmentBlock = singleCase.RS_Output_Importer_Consignment__c || '';
                this.originTaxCode = singleCase.RS_Output_Exporter_TaxID__c || '';
                this.destinationTaxCode = singleCase.RS_Output_Importer_TaxID__c || '';
    
                await this.updateRecordFields({
                    Shipper_Consignment_Block__c: this.shipperConsignmentBlock,
                    Consignee_Consignment_Block__c: this.consigneeConsignmentBlock,
                    Origin_Freight_Agent_Tax_Code__c: this.originTaxCode,
                    Destination_Freight_Agent_Tax_Code__c: this.destinationTaxCode
                });
            } else {
                //For Consolidated records, use the existing logic
                await this.setFreightAgentsFromCases([singleCase]);
            }
            
            this.showToast('Success', 'Synced from HAWB successfully', 'success');
        } catch (error) {
            this.showToast('Error', 'Failed to sync from HAWB', 'error');
        } finally {
            this.isLoading = false;
        }
    }


    async loadFromHAWBs() {
        this.isLoading = true;
        try {
            if (this.relatedCases.length === 0) {
                this.showToast('Warning', 'No HAWBs found to load from', 'warning');
                return;
            }
            
            //Re-check to make sure info is the same between cases before loading
            this.checkFreightAgentConsistency();
            
            if (this.showFreightAgentMismatch) {
                return;
            }
            
            await this.setFreightAgentsFromCases(this.relatedCases);
            this.showToast('Success', 
                'Freight agents and consignment blocks updated from HAWBs', 
                'success');
        } catch (error) {
            console.error('Full error:', error); 
            let errorMessage = 'Failed to load from HAWBs';
            
            if (error.body && error.body.message) {
                errorMessage += ': ' + error.body.message;
            } else if (error.message) {
                errorMessage += ': ' + error.message;
            } else if (error.body && Array.isArray(error.body)) {
                errorMessage += ': ' + error.body.map(e => e.message).join(', ');
            }
            
            this.showToast('Error', errorMessage, 'error');
        } finally {
            this.isLoading = false;
        }
    }

    async setFreightAgentsFromCases(cases) {
        if (cases.length === 0) {
            console.error('No cases provided to setFreightAgentsFromCases');
            throw new Error('No cases provided');
        }
    
        const firstCase = cases[0];
    
        const newShipperBlock = firstCase.Origin_Freight_Agent__r?.Consignment_Info__c || '';
        const newConsigneeBlock = firstCase.Destination_Freight_Agent__r?.Consignment_Info__c || '';
    
        //Get tax codes from the freight agents with additional logging
        const newOriginTaxCode = firstCase.Origin_Freight_Agent__r?.Tax_ID__c || '';
        const newDestinationTaxCode = firstCase.Destination_Freight_Agent__r?.Tax_ID__c || '';
    
        const fields = {
            Origin_Freight_Agent__c: firstCase.Origin_Freight_Agent__c,
            Destination_Freight_Agent__c: firstCase.Destination_Freight_Agent__c,
            Shipper_Consignment_Block__c: newShipperBlock,
            Consignee_Consignment_Block__c: newConsigneeBlock,
            Origin_Freight_Agent_Tax_Code__c: newOriginTaxCode,
            Destination_Freight_Agent_Tax_Code__c: newDestinationTaxCode
        };
    
        if (!fields.Origin_Freight_Agent__c) {
            const missingCases = cases.filter(c => !c.Origin_Freight_Agent__c).map(c => c.CaseNumber);
            throw new Error(`The following HAWBs are missing Origin Freight Agent: ${missingCases.join(', ')}`);
        }
        
        if (!fields.Destination_Freight_Agent__c) {
            const missingCases = cases.filter(c => !c.Destination_Freight_Agent__c).map(c => c.CaseNumber);
            throw new Error(`The following HAWBs are missing Destination Freight Agent: ${missingCases.join(', ')}`);
        }
        
        try {
            const recordInput = { 
                fields: {
                    Id: this.recordId,
                    ...fields
                }
            };
            
            await updateRecord(recordInput);
            publishRefreshEvent();
            
            this.shipperConsignmentBlock = newShipperBlock;
            this.consigneeConsignmentBlock = newConsigneeBlock;
            this.originFreightAgent = fields.Origin_Freight_Agent__c;
            this.destinationFreightAgent = fields.Destination_Freight_Agent__c;
            this.originTaxCode = newOriginTaxCode;
            this.destinationTaxCode = newDestinationTaxCode;
            
            
            //Refresh the data
            await refreshApex(this.wiredManifestResult);
            
        } catch (error) {
            console.error('Error updating freight agents and consignment blocks:', {
                error: error,
                stack: error.stack,
                body: error.body
            });
            throw error;
        }
    }


    handleTaxCodeChange(event) {
        clearTimeout(this.taxCodeTimer);
        
        const field = event.target.dataset.field === 'origin' ? 
                     ORIGIN_TAX_CODE_FIELD.fieldApiName : 
                     DESTINATION_TAX_CODE_FIELD.fieldApiName;
        const value = event.target.value;
        
        //Update local value for UI-sake
        if (field === ORIGIN_TAX_CODE_FIELD.fieldApiName) {
            this.originTaxCode = value;
        } else {
            this.destinationTaxCode = value;
        }
        
        //Set timer to update after 2.5 seconds of inactivity
        this.taxCodeTimer = setTimeout(() => {
            this.updateTaxCodeField(field, value);
        }, 2500);
    }

    async updateTaxCodeField(field, value) {
        try {
            this.isLoading = true;
            const recordInput = { 
                fields: {
                    Id: this.recordId,
                    [field]: value
                }
            };
            
            await updateRecord(recordInput);
            publishRefreshEvent();
            
            //Refresh the view using refreshApex
            await refreshApex(this.wiredManifestResult);
            
            this.showToast('Success', 'Tax Code updated successfully', 'success');
        } catch (error) {
            this.showToast('Error', 'Failed to update Tax Code', 'error');
            console.error('Error updating Tax Code:', error);
            
            //Revert the local value if update fails
            if (field === ORIGIN_TAX_CODE_FIELD.fieldApiName) {
                this.originTaxCode = this.wiredManifestResult.data.fields.Origin_Freight_Agent_Tax_Code__c?.value || '';
            } else {
                this.destinationTaxCode = this.wiredManifestResult.data.fields.Destination_Freight_Agent_Tax_Code__c?.value || '';
            }
        } finally {
            this.isLoading = false;
        }
    }

    handleNotifyPartyChange(event) {
        //Clear any existing timer to prevent multiple updates
        clearTimeout(this.notifyPartyTimer);
        
        //Update local value for UI
        this.notifyParty = event.target.value;
        
        //Set a new timer to update the record after 2.5 seconds of inactivity
        this.notifyPartyTimer = setTimeout(() => {
            this.updateNotifyPartyField(this.notifyParty);
        }, 2500);
    }

    async updateNotifyPartyField(value) {
        try {
            this.isLoading = true;
            const recordInput = { 
                fields: {
                    Id: this.recordId,
                    Notify_Party__c: value
                }
            };
            
            await updateRecord(recordInput);
            publishRefreshEvent();
            await refreshApex(this.wiredManifestResult);
            
            this.showToast('Success', 'Notify Party updated successfully', 'success');
        } catch (error) {
            this.showToast('Error', 'Failed to update Notify Party', 'error');
            console.error('Error updating Notify Party:', error);
        } finally {
            this.isLoading = false;
        }
    }

    handleFreightAgentChange(event) {
        try {
            const fieldApiName = event.target.fieldName;
            const value = event.target.value;
            
            if (fieldApiName === ORIGIN_FREIGHT_AGENT.fieldApiName) {
                this.originFreightAgent = value;
            } else if (fieldApiName === DESTINATION_FREIGHT_AGENT.fieldApiName) {
                this.destinationFreightAgent = value;
            }
            
            //Update the record
            this.updateField(fieldApiName, value);
        } catch (error) {
            console.error('Error handling freight agent change:', error);
            this.showToast('Error', 'Failed to update freight agent', 'error');
        }
    }

    async updateField(field, value) {
        try {
            const fields = {};
            fields[field] = value;
            
            const recordInput = { 
                fields: {
                    Id: this.recordId,
                    ...fields
                }
            };
            
            await updateRecord(recordInput);
            publishRefreshEvent();
            await refreshApex(this.wiredManifestResult);
        } catch (error) {
            console.error('Error updating field:', error);
            this.showToast('Error', `Failed to update ${field}`, 'error');
        }
    }

    showToast(title, message, variant) {
        const evt = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
        });
        this.dispatchEvent(evt);
    }
}