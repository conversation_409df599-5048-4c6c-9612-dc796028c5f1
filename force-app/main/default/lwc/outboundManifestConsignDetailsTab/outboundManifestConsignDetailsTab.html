<template>
    <!--MAWB Consignment Details Section -->
    <div class="slds-section slds-is-open">
        <div class="slds-section__title">
            <button class="slds-button slds-section__title-action" onclick={toggleConsignmentDetails} aria-expanded="true">
                <lightning-icon class="slds-section__title-action-icon" icon-name={consignmentDetailsIcon} alternative-text="Toggle section"></lightning-icon>
                <span class="slds-truncate">MAWB Consignment Details </span>
            </button>
        </div>
        <div class="slds-section__content" lwc:if={showConsignmentDetails}>
            <div class="slds-p-around_medium">
                <div class="slds-grid slds-wrap slds-m-bottom_medium">
                    <!--First Row Direct - Import/Export Entity mismatch banner  -->
                    <template lwc:if={isDirect}>
                        <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium"> 
                            <template lwc:if={showMissingEntityAlert}>
                                <div class="slds-notify slds-notify_alert slds-theme_alert-texture slds-theme_warning custom-alert-white" role="alert">
                                    <span class="slds-assistive-text">warning</span>
                                    <span class="slds-icon_container slds-m-right_x-small" title="warning">
                                        <lightning-icon class="white-icon" icon-name="utility:warning" size="x-small"></lightning-icon>
                                    </span>
                                    <h2 class="alert-text-bold-white">Related shipment is missing import/export entity.</h2>
                                </div>
                            </template>
                        </div>
                    </template>

                    <!--First Row Consolidated - Load from HAWBs button & Freight Agent mismatch banner -->
                    <template lwc:if={isConsolidated}>
                        <div class="slds-col slds-size_1-of-6 slds-p-right_small">
                            <lightning-button 
                                label="Load from HAWBs" 
                                onclick={loadFromHAWBs}
                                variant="brand" 
                                icon-position="left"
                                class="slds-m-bottom_x-small">
                            </lightning-button>
                        </div>
                        <div class="slds-col slds-size_5-of-6 slds-p-left_small">
                            <template lwc:if={showFreightAgentMismatch}>
                                <div class="slds-notify slds-notify_alert slds-theme_alert-texture slds-theme_warning" role="alert">
                                    <span class="slds-assistive-text">warning</span>
                                    <span class="slds-icon_container slds-icon-utility-warning slds-m-right_x-small" title="warning">
                                        <lightning-icon icon-name="utility:warning" size="x-small"></lightning-icon>
                                    </span>
                                    <h2>One or more related shipments has a freight agent mismatch.</h2>
                                </div>
                            </template>
                        </div>
                    </template>

                   <!--Second Row - Freight Agents -->
                    <div class="slds-col slds-size_1-of-2 slds-p-right_small slds-form-element_stacked">
                        <lightning-record-edit-form record-id={recordId} object-api-name="Outbound_Manifest__c">
                            <lightning-input-field 
                                field-name="Origin_Freight_Agent__c"
                                label="Origin Freight Agent"
                                value={originFreightAgent}
                                onchange={handleFreightAgentChange}
                                disabled={isConsolidated}
                                class="slds-form-element_stacked">
                            </lightning-input-field>
                        </lightning-record-edit-form>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-left_small slds-form-element_stacked">
                        <lightning-record-edit-form record-id={recordId} object-api-name="Outbound_Manifest__c">
                            <lightning-input-field 
                                field-name="Destination_Freight_Agent__c"
                                label="Destination Freight Agent"
                                value={destinationFreightAgent}
                                onchange={handleFreightAgentChange}
                                disabled={isConsolidated}
                                class="slds-form-element_stacked">
                            </lightning-input-field>
                        </lightning-record-edit-form>
                    </div>

                    <!--Third Row (Direct) - Exporting/Importing Entities  -->
                    <template lwc:if={isDirect}>
                        <div class="slds-col slds-size_1-of-2 slds-p-right_small slds-form-element_stacked">
                            <lightning-input 
                                label={exportingEntityLabel} 
                                value={exportingEntity} 
                                disabled
                                class="slds-form-element_stacked">
                            </lightning-input>
                        </div>
                        <div class="slds-col slds-size_1-of-2 slds-p-left_small slds-form-element_stacked">
                            <lightning-input 
                                label={importingEntityLabel} 
                                value={importingEntity} 
                                disabled
                                class="slds-form-element_stacked">
                            </lightning-input>
                        </div>
                    </template>

                    <!--Fourth Row - Shipping/Consignee Consignment Blocks -->
                    <div class="slds-col slds-size_1-of-2 slds-p-right_small slds-form-element_stacked">
                        <lightning-textarea 
                            label="MAWB Shipper:" 
                            value={shipperConsignmentBlock} 
                            lwc:ref="shipperTextarea"
                            disabled
                            rows="6" 
                            class="full-height-textarea slds-form-element_stacked">
                        </lightning-textarea>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-left_small slds-form-element_stacked">
                        <lightning-textarea 
                            label="MAWB Consignee:" 
                            value={consigneeConsignmentBlock} 
                            lwc:ref="consigneeTextarea"
                            disabled
                            rows="6"
                            class="full-height-textarea slds-form-element_stacked">
                        </lightning-textarea>
                    </div>

                    <!--Fifth Row - Tax Codes -->
                    <div class="slds-col slds-size_1-of-2 slds-p-right_small slds-form-element_stacked">
                        <lightning-input 
                            label="Tax Code" 
                            value={originTaxCode} 
                            lwc:ref="originTaxInput"
                            maxlength="255" 
                            data-field="origin"
                            onchange={handleTaxCodeChange}
                            class="slds-form-element_stacked">
                        </lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-left_small slds-form-element_stacked">
                        <lightning-input 
                            label="Tax Code" 
                            value={destinationTaxCode} 
                            lwc:ref="destTaxInput"
                            maxlength="255" 
                            data-field="destination"
                            onchange={handleTaxCodeChange}
                            class="slds-form-element_stacked">
                        </lightning-input>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Notify Party Section -->
    <div class="slds-section slds-is-open">
        <div class="slds-section__title">
            <button class="slds-button slds-section__title-action" onclick={toggleNotifyParty} aria-expanded="true">
                <lightning-icon class="slds-section__title-action-icon" icon-name={notifyPartyIcon} alternative-text="Toggle section"></lightning-icon>
                <span class="slds-truncate">Notify Party</span>
            </button>
        </div>
        <div class="slds-section__content" lwc:if={showNotifyParty}>
            <div class="slds-p-around_small">
                <div class="slds-grid slds-wrap slds-m-bottom_medium">
                    <div class="slds-col slds-size_1-of-1 slds-form-element_stacked">
                        <lightning-textarea 
                            label="MAWB Notify Party Instructions" 
                            value={notifyParty} 
                            maxlength="255" 
                            onchange={handleNotifyPartyChange}
                            class="slds-form-element_stacked">
                        </lightning-textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>