/**
 * Created by re<PERSON><PERSON><PERSON> on 1/6/23.
 */

@IsTest
public with sharing class VendorInvoiceLinesEditorControllerTest {

    @TestSetup
    private static void setup() {
        Countries__c country = TestFactory.getCountries('United States', true);

        Account acct        = TestFactory.getAccount('Test Account', Constants.ACCOUNT_CUSTOMER_RT,  true);
        Contact cntct       = TestFactory.getContact('Terry', 'Testerson', acct, true);
        Case cse            = TestFactory.getCase(acct, cntct, country, Constants.CASE_STATUS_CLOSED, Constants.CASE_IT_SHIPMENT_RT, true);
        Quote__c qt         = TestFactory.getQuote(acct, cse, true);
        Quote_Version__c qv = TestFactory.getQuoteVersion(qt, true);

        cse.Related_Quote_Version__c = qv.Id;
        update cse;

        qt.Active_Version__c = qv.Id;
        update qt;

        Vendor_Invoice__c vi = new Vendor_Invoice__c(
            Shipment__c = cse.Id
        );
        insert vi;
    }

    @IsTest
    private static void testGetInvoices() {
        Vendor_Invoice__c vi = [SELECT Id FROM Vendor_Invoice__c];

        Vendor_Invoice_Line__c viLine = new Vendor_Invoice_Line__c(
            Vendor_Invoice__c = vi.Id
        );
        insert viLine;

        Test.startTest();
        List<Vendor_Invoice_Line__c> vendorInvoiceLines = VendorInvoiceLinesEditorController.getInvoiceLines(vi.Id);
        Test.stopTest();

        System.assertEquals(vendorInvoiceLines.size(), 1);
    }

    @IsTest
    private static void testHandleCaseUpdates_VIL() {
        Case cse = [SELECT Id FROM Case];
        cse.VI_DutyTax__c = true;
        cse.VI_Origin__c = true;
        cse.VI_Freight__c = true;
        cse.VI_Destination__c = true;
        cse.Destination_Country__c = null;
        update cse;

        Vendor_Invoice__c vi = [SELECT Id FROM Vendor_Invoice__c];

        Vendor_Invoice_Line__c viLine1 = new Vendor_Invoice_Line__c(
            Vendor_Invoice__c = vi.Id,
            Charge_Category__c = 'Tax'
        );

        Vendor_Invoice_Line__c viLine2 = new Vendor_Invoice_Line__c(
                Vendor_Invoice__c = vi.Id,
                Charge_Category__c = 'Pickup'
        );

        Vendor_Invoice_Line__c viLine3 = new Vendor_Invoice_Line__c(
                Vendor_Invoice__c = vi.Id,
                Charge_Category__c = 'Air Freight'
        );

        Vendor_Invoice_Line__c viLine4 = new Vendor_Invoice_Line__c(
                Vendor_Invoice__c = vi.Id,
                Charge_Category__c = 'Clearance'
        );

        insert new List<Vendor_Invoice_Line__c>{viLine1, viLine2, viLine3, viLine4};

        Test.startTest();
        VendorInvoiceLinesEditorController.handleCaseUpdates(vi.Id);
        Test.stopTest();

        cse = [SELECT VI_DutyTax__c, VI_Origin__c, VI_Freight__c, VI_Destination__c FROM Case];

        System.assertEquals(false, cse.VI_DutyTax__c);
        System.assertEquals(false, cse.VI_Origin__c);
        System.assertEquals(false, cse.VI_Freight__c);
        System.assertEquals(false, cse.VI_Destination__c);

    }

    @IsTest
    private static void testHandleCaseUpdates_Case() {
        Case cse = [SELECT Id FROM Case];
        cse.VI_DutyTax__c = true;
        cse.VI_Origin__c = true;
        update cse;

        Vendor_Invoice__c vi = [SELECT Id FROM Vendor_Invoice__c];

        Vendor_Invoice_Line__c viLine = new Vendor_Invoice_Line__c(
            Vendor_Invoice__c = vi.Id,
            Charge_Category__c = 'Air Freight'
        );
        insert viLine;

        VendorInvoiceLinesEditorController.handleCaseUpdates(vi.Id);
        cse = [SELECT VI_DutyTax__c, VI_Origin__c FROM Case];
        System.assertEquals(false, cse.VI_DutyTax__c);

        cse.Destination_Country__c = null;
        cse.Drop_Ship_to_FGX__c = true;
        update cse;

        Test.startTest();
        VendorInvoiceLinesEditorController.handleCaseUpdates(vi.Id);
        Test.stopTest();

        cse = [SELECT VI_DutyTax__c, VI_Origin__c FROM Case];
        System.assertEquals(false, cse.VI_Origin__c);

    }
}