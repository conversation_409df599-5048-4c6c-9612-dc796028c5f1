/**
 * Created by re<PERSON><PERSON><PERSON> on 11/15/22.
 */

public with sharing class VendorInvoiceLinesEditorController {

    @AuraEnabled(cacheable=false)
    public static List<Vendor_Invoice_Line__c> getInvoiceLines(Id vendorInvoiceId) {
        return [
                SELECT Id,
                        Vendor_Invoice__c,
                        Charge_Description__c,
                        Charge_Category__c,
                        Charge_Amount__c,
                        Vendor_Invoice__r.CurrencyIsoCode,
                        CurrencyIsoCode
                FROM Vendor_Invoice_Line__c
                WHERE Vendor_Invoice__c =: vendorInvoiceId
                ORDER BY CreatedDate ASC
        ];
    }

    @AuraEnabled(cacheable=false)
    public static void handleCaseUpdates(Id vendorInvoiceId) {
        List<Vendor_Invoice_Line__c> invoiceLines = [
                SELECT Id,
                        Vendor_Invoice__c,
                        Charge_Description__c,
                        Charge_Category__c,
                        Charge_Amount__c,
                        Vendor_Invoice__r.CurrencyIsoCode,
                        Vendor_Invoice__r.Shipment__r.Id,
                        Vendor_Invoice__r.Shipment__r.Origin_Country__r.Name,
                        Vendor_Invoice__r.Shipment__r.Destination_Country__r.Name,
                        Vendor_Invoice__r.Shipment__r.Origin_Country__c,
                        Vendor_Invoice__r.Shipment__r.Destination_Country__c,
                        Vendor_Invoice__r.Shipment__r.Drop_Ship_to_FGX__c,
                        Vendor_Invoice__r.Shipment__r.VI_DutyTax__c,
                        Vendor_Invoice__r.Shipment__r.VI_Origin__c,
                        Vendor_Invoice__r.Shipment__r.VI_Freight__c,
                        Vendor_Invoice__r.Shipment__r.VI_Destination__c
                FROM Vendor_Invoice_Line__c
                WHERE Vendor_Invoice__c =: vendorInvoiceId
                ORDER BY CreatedDate ASC
        ];

        if(!invoiceLines.isEmpty()) {
            Case c = invoiceLines.get(0).Vendor_Invoice__r.Shipment__r;

            for(Vendor_Invoice_Line__c vil : invoiceLines) {

                if(c.Origin_Country__c == c.Destination_Country__c) {
                    c.VI_DutyTax__c = false;
                }

                if(
                    'United States'.equalsIgnoreCase(c.Origin_Country__r?.Name) &&
                    !('United States'.equalsIgnoreCase(c.Destination_Country__r?.Name)) &&
                    c.Drop_Ship_to_FGX__c == true
                ) {
                    c.VI_Origin__c = false;
                }

                if(
                    'Tax'.equalsIgnoreCase(vil.Charge_Category__c) ||
                    'Duty'.equalsIgnoreCase(vil.Charge_Category__c)
                ) {
                    c.VI_DutyTax__c = false;
                }

                if(
                    'Pickup'.equalsIgnoreCase(vil.Charge_Category__c) ||
                    'Packing'.equalsIgnoreCase(vil.Charge_Category__c)
                ) {
                    c.VI_Origin__c = false;
                }

                if('Air Freight'.equalsIgnoreCase(vil.Charge_Category__c)) {
                    c.VI_Freight__c = false;
                }

                if(
                    'Clearance'.equalsIgnoreCase(vil.Charge_Category__c) ||
                    'Delivery'.equalsIgnoreCase(vil.Charge_Category__c) ||
                    'Permit'.equalsIgnoreCase(vil.Charge_Category__c)
                ) {
                    c.VI_Destination__c = false;
                }
            }

            SObjectService.upsertRecord(c);
        }
    }
}