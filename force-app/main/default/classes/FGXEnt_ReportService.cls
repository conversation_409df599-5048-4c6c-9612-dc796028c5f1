/**
 * FGXEnt_ReportService
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 10/4/24
 */

public without sharing class FGXEnt_ReportService {
	/**
	 * @description Takes in a serialized FGXEnt_ReportModel to act as a request
	 * to start the report execution process. This lets us pass in filters and everything
	 * else we will need later down the line when we start implementing the UI. It also
	 * makes it very simple to take the return from the Retrieve Report command and dump it
	 * in here with filter values
	 * @param reportPayload Serialized FGXEnt_ReportModel payload
	 *
	 * @return FGXPlatformEventQueue__c Id
	 */
	public static Id initiateReportExecutionByReportPayload(String reportPayload) {
		FGXEnt_ReportModel reportModel = (FGXEnt_ReportModel) JSON.deserialize(reportPayload, FGXEnt_ReportModel.class);
		PlatformEventQueuePayloadExecuteReport reportRequest = new PlatformEventQueuePayloadExecuteReport(reportModel);

		return initiateReportExecution(JSON.serialize(reportRequest));
	}

	/**
	 * @description
	 * @param reportRequestPayload: PlatformEventQueuePayloadExecuteReport
	 * @return FGXPlatformEventQueue__c Id
	 */
	public static Id initiateReportExecution(String reportRequestPayload) {
		User u = FGXEnt_UserService.getCurrentUser();

		FGXPlatformEventQueue__c queueItem = new FGXPlatformEventQueue__c();
		queueItem.Payload__c = reportRequestPayload;
		queueItem.Account__c = u.AccountId;
		queueItem.Requestor__c = u.ContactId;
		queueItem.Status__c = PlatformEventQueue.EventStatus.Unprocessed.name();
		queueItem.PayloadEventCode__c = PlatformEventQueue.PayloadEventCode.execute_report.name();
		insert queueItem;

		return queueItem.Id;
	}

	/**
	 * @description Begins the process of running a report
	 * retrieval asynchronously. We have to do this since community users
	 * can't run reports. This function generates a queue item that
	 * will then be picked up and processed accordingly
	 * @return FGXPlatformEventQueue__c Id
	 */
	public static Id initiateReportRetrieval() {
		User u = FGXEnt_UserService.getCurrentUser();

		PlatformEventQueuePayloadRetrieveReports reportRequest = new PlatformEventQueuePayloadRetrieveReports();

		FGXPlatformEventQueue__c queueItem = new FGXPlatformEventQueue__c();
		queueItem.Payload__c = JSON.serialize(reportRequest);
		queueItem.Account__c = u.AccountId;
		queueItem.Requestor__c = u.ContactId;
		queueItem.Status__c = PlatformEventQueue.EventStatus.Unprocessed.name();
		queueItem.PayloadEventCode__c = PlatformEventQueue.PayloadEventCode.retrieve_reports.name();
		insert queueItem;

		return queueItem.Id;
	}

	/**
	 * @description Returns a recent report retrieval queue item Id to act as a cached run
	 * @return Return the Id of a FGXPlatformEventQueue__c record that was recently
	 * pulled within the last X hours based on the configuration value
	 */
	public static Id getRecentReportRetrieval() {
		List<FGXPlatformEventQueue__c> requests = new List<FGXPlatformEventQueue__c>();

		if (FGXEnt_GeneralConfig.REPORT_RETRIEVAL_CACHE_ENABLED) {
			User u = FGXEnt_UserService.getCurrentUser();
			Datetime cutoffTime = Datetime.now().addHours(-Integer.valueOf(FGXEnt_GeneralConfig.REPORT_RETRIEVAL_CACHE_THRESHOLD));

			requests = [
					SELECT Id
					FROM FGXPlatformEventQueue__c
					WHERE Requestor__c = :u.ContactId
					AND Account__c = :u.AccountId
					AND CreatedDate >= :cutoffTime
					AND PayloadEventCode__c = :PlatformEventQueue.PayloadEventCode.retrieve_reports.name()
					AND Status__c = :PlatformEventQueue.EventStatus.Processed.name()
					ORDER BY CreatedDate DESC
					LIMIT 1
			];
		}
		return requests.isEmpty() ? null : requests[0].Id;
	}

	/**
	 * @description Retrieves the content version data
	 * attached to the given queue record and then returns
	 * a string converted from the blob
	 * @param queueId
	 * @return All of the data attached on the blob to the FGXPlatformEventQueue__c.
	 * This would be a map of maps to represent a table
	 */
	public static String getReportData(String queueId) {
		List<FGXPlatformEventQueue__c> queues = [
				SELECT Id, (
						SELECT Id,
								ContentDocument.LatestPublishedVersionId
						FROM ContentDocumentLinks
				)
				FROM FGXPlatformEventQueue__c
				WHERE Id = :queueId
		];
		if (!queues.isEmpty()) {
			if (!queues.get(0).ContentDocumentLinks.isEmpty()) {
				Id versionId = queues.get(0).ContentDocumentLinks.get(0).ContentDocument.LatestPublishedVersionId;
				ContentVersion version = [
						SELECT Id, VersionData
						FROM ContentVersion
						WHERE Id = :versionId
				];
				return version.VersionData.toString();
			}
		}
		return null;
	}

	/**
	 * @description Takes in the PlatformEventQueuePayloadExecuteReport payload
	 * and returns a FGXEnt_ReportRunModel which is our basic representation of table data and columns. Eventually the FGXEnt_ReportRunModel
	 * will then be serialized and dumped into a blob attached to the queue record. The data returned here is what getReportData above
	 * will get when it retrieves the contents of the ContentVersion
	 * @param payloadEvent
	 * @return FGXEnt_ReportRunModel - the table columns and data needed to assemble the table
	 */
	public static FGXEnt_ReportRunModel doReportRun(PlatformEventQueue payloadEvent) {
		PlatformEventQueuePayloadExecuteReport requestedReport = (PlatformEventQueuePayloadExecuteReport) payloadEvent.payload;

		if (String.isBlank(requestedReport.retrieveByField) || String.isBlank(requestedReport.retrieveByValue)) {
			throw new FGXEnt_ReportServiceException('Unable to retrieve and build report. Missing report id or report name.');
		}

		Report report = fetchReportByAttribute(requestedReport.retrieveByField, requestedReport.retrieveByValue);
		requestedReport.setReport(report);

		if (String.isBlank(requestedReport.report.accountFilterField)) {
			throw new FGXEnt_ReportServiceException('Unable to retrieve and build report. Missing Account Filter Field(s).');
		}
		String reportId = report.Id;

		Reports.ReportDescribeResult describe = Reports.ReportManager.describeReport(reportId);
		Reports.ReportMetadata reportMd = describe.getReportMetadata();
		List<Reports.ReportFilter> filters = reportMd.getReportFilters();
		Map<String, FGXEnt_ReportModel.FGXEnt_ReportFilter> reportFilterMap = requestedReport.report.reportFilterMapByColumn;

		applyDateFiltering(reportMd, payloadEvent.account, reportFilterMap);

		List<Reports.ReportFilter> filtersToInclude = new List<Reports.ReportFilter>();
		Boolean setAccountFilter = false;

		for (Reports.ReportFilter filter : filters) {
			String filterColumn = filter.getColumn();

			if (reportFilterMap.containsKey(filterColumn)) {
				FGXEnt_ReportModel.FGXEnt_ReportFilter requestedFilter = reportFilterMap.get(filterColumn);

				if (filterColumn.equalsIgnoreCase(requestedReport.report.accountFilterField)) {

					// if this is null that means we need to pull the account from the queue record
					if (String.isBlank(requestedFilter.value)) {
						if (String.isNotBlank(requestedReport.report.accountFilterId)) {
							filter.setValue(requestedReport.report.accountFilterId);
							filter.setOperator(FGXEnt_ReportModel.Operators.equals.name());
							setAccountFilter = true;

						} else if (String.isNotBlank(payloadEvent.account.Id)) {
							filter.setValue(payloadEvent.account.Id);
							filter.setOperator(FGXEnt_ReportModel.Operators.equals.name());
							setAccountFilter = true;
						}
					} else if ((requestedFilter.value.equalsIgnoreCase(payloadEvent.account.Id) || requestedFilter.value.equalsIgnoreCase(requestedReport.report.accountFilterId))) {
						filter.setValue(requestedFilter.value);
						filter.setOperator(FGXEnt_ReportModel.Operators.equals.name());
						setAccountFilter = true;
					}
				} else {
					if (requestedFilter.visible ?? false) {
						filter.setValue(requestedFilter.value);

						if (String.isNotBlank(requestedFilter.operator)) {
							if (requestedFilter.operator != filter.getOperator()) {
								filter.setOperator(requestedFilter.operator);
							}
						}
					}
				}
				filtersToInclude.add(filter);
			}
		}
		reportMd.setReportFilters(filtersToInclude);

		if (!setAccountFilter && !Test.isRunningTest()) {
			throw new FGXEnt_ReportServiceException('Unable to retrieve and build report. Missing Account Filter Field(s).');
		}

		Reports.ReportResults reportResults = Reports.ReportManager.runReport(reportId, reportMd, true);
		Reports.ReportFactWithDetails factDetails = (Reports.ReportFactWithDetails) reportResults.getFactMap().get('T!T');
		List<Reports.ReportDetailRow> rows = factDetails.getRows();

		Reports.ReportExtendedMetadata extendedMetadata = describe.getReportExtendedMetadata();
		Map<String, Reports.DetailColumn> detailedColumnInfo = extendedMetadata.getDetailColumnInfo();

		Map<String, FGXEnt_ReportMapping.ReportMappingItem> getReportColumnMappingForReport = FGXEnt_ReportMapping.getInstance().getColumnMappingsMapByReportId(reportId);

		List<FGXEnt_ReportRunModelColumn> columns = new List<FGXEnt_ReportRunModelColumn>();
		for (String columnName : detailedColumnInfo.keySet()) {
			FGXEnt_ReportRunModelColumn column = new FGXEnt_ReportRunModelColumn(detailedColumnInfo.get(columnName));
			if (getReportColumnMappingForReport.containsKey(columnName)) {
				column.setReportMappingValues(getReportColumnMappingForReport.get(columnName));
			}
			columns.add(column);
		}

		List<Map<String, Object>> table = new List<Map<String, Object>>();
		for (Reports.ReportDetailRow row : rows) {
			List<Reports.ReportDataCell> rowCells = row.getDataCells();
			Map<String, Object> record = new Map<String, Object>();
			for (Integer i = 0; i < rowCells.size(); i++) {
				Reports.ReportDataCell cell = rowCells.get(i);
				FGXEnt_ReportRunModelColumn info = columns.get(i);

				Map<String, Object> fieldMap = new Map<String, Object>{
						'label' => cell.getLabel(),
						'value' => cell.getValue()
				};
				record.put(info.fieldName, fieldMap);
			}
			table.add(record);
		}
		return new FGXEnt_ReportRunModel(table, columns);
	}

	/**
	 * @description Retrieves all available reports associated to an Account
	 * @param accountId
	 * @return
	 */
	public static List<FGXEnt_ReportModel> getAvailableReports(Id accountId) {

		Map<Id, FGXEnt_ReportMapping.ReportMapping> mappings = FGXEnt_ReportMapping.getInstance().getReportsByAccountId(accountId);
		List<Report> reportList = [
				SELECT Id,
						Name,
						DeveloperName,
						Description
				FROM Report
				WHERE Id IN :mappings.keySet()
		];
		List<FGXEnt_ReportModel> reportModels = new List<FGXEnt_ReportModel>();

		for (Report report : reportList) {
			if (mappings.containsKey(report.Id)) {
				reportModels.add(new FGXEnt_ReportModel(report, mappings.get(report.Id)));
			}
		}
		return reportModels;
	}

	/**
	 * @param fieldName
	 * @param fieldValue
	 *
	 * @return
	 */
	public static Report fetchReportByAttribute(String fieldName, String fieldValue) {
		String retrieveByField = fieldName;
		String retrieveByValue = fieldValue;

		String query = 'SELECT Id, Name, Description, DeveloperName FROM Report WHERE {0} =: retrieveByValue';
		query = String.format(query, new List<String>{
				retrieveByField
		});

		List<Report> reportList = Database.query(query);

		if (reportList.isEmpty()) {
			throw new FGXEnt_ReportServiceException('No Reports found for ' + fieldName + ': ' + fieldValue);
		}

		return reportList.get(0);
	}

	/**
	 * @description Retrieves metadata for related files, including ContentVersions and legacy Attachments,
	 * associated with the given record IDs. Ensures that the current user has access to the records
	 * before retrieving file information.
	 *
	 * @param recordIds A list of record IDs for which related file metadata should be retrieved.
	 * @return A map where each key is a record ID and the value is a list of FileWrapper objects
	 *         representing related files.
	 */
	public static Map<Id, List<FGXEnt_Msgs.FileWrapper>> getRelatedFileMetadata(List<Id> recordIds) {
		if (recordIds.isEmpty()) {
			return new Map<Id, List<FGXEnt_Msgs.FileWrapper>>();
		}

		User currentUser = FGXEnt_UserService.getCurrentUser();
		Id accountId = currentUser.AccountId;

		// Group recordIds by object type
		Map<String, Set<Id>> recordsByType = new Map<String, Set<Id>>();
		for (Id recordId : recordIds) {
			String objectType = recordId.getSObjectType().getDescribe().getName();
			if (!recordsByType.containsKey(objectType)) {
				recordsByType.put(objectType, new Set<Id>());
			}
			recordsByType.get(objectType).add(recordId);
		}

		// Prepare final result map
		Map<Id, List<FGXEnt_Msgs.FileWrapper>> fileMap = new Map<Id, List<FGXEnt_Msgs.FileWrapper>>();

		// Process each object type separately
		for (String objectType : recordsByType.keySet()) {
			Set<Id> objectRecordIds = recordsByType.get(objectType);

			List<String> contentFields = OBJECT_DOCUMENT_FIELDS.get(objectType);
			List<String> accountFields = OBJECT_ACCOUNT_RELATIONSHIPS.get(objectType);

			if (accountFields == null || accountFields.isEmpty()) {
				throw new IllegalArgumentException('No account relationship fields defined for object: ' + objectType);
			}

			// Construct access control query dynamically
			List<String> accessFilters = new List<String>();
			for (String field : accountFields) {
				accessFilters.add(field + ' = :accountId');
			}
			String accessQuery = 'SELECT Id FROM ' + objectType +
					' WHERE Id IN :objectRecordIds AND (' + String.join(accessFilters, ' OR ') + ')';

			Set<Id> accessibleRecordIds = new Set<Id>();
			for (SObject record : Database.query(accessQuery)) {
				accessibleRecordIds.add((Id) record.get('Id'));
			}

			// Initialize fileMap for each record
			for (Id recordId : accessibleRecordIds) {
				fileMap.put(recordId, new List<FGXEnt_Msgs.FileWrapper>());
			}

			// Query for document IDs from the object's specific fields
			Map<Id, Set<String>> recordToDocIds = new Map<Id, Set<String>>();
			if (contentFields != null && !contentFields.isEmpty()) {
				String contentQuery = 'SELECT Id, ' + String.join(contentFields, ',') +
						' FROM ' + objectType +
						' WHERE Id IN :accessibleRecordIds';

				for (SObject record : Database.query(contentQuery)) {
					Id recordId = (Id) record.get('Id');
					recordToDocIds.put(recordId, new Set<String>());

					for (String field : contentFields) {
						String fieldValue = (String) record.get(field);
						if (fieldValue != null) {
							splitAndAddToSet(fieldValue, recordToDocIds.get(recordId));
						}
					}
				}
				// If contentFields is null or empty it means this is probably a Site, Entity, Action_Item__c, Asset_Unit__c or Asset_Type__c record.
				// In that case we can just pull all the records related that the user has access to
			} else if (!accessibleRecordIds.isEmpty()) {
				for (ContentDocumentLink cdl : [
						SELECT ContentDocumentId, LinkedEntityId
						FROM ContentDocumentLink
						WHERE LinkedEntityId IN :accessibleRecordIds
				]) {
					Id recordId = cdl.LinkedEntityId;
					if (!recordToDocIds.containsKey(recordId)) {
						recordToDocIds.put(recordId, new Set<String>());
					}
					recordToDocIds.get(recordId).add(cdl.ContentDocumentId);
				}
			}


			// Fetch ContentVersions for the collected ContentDocument IDs
			if (!recordToDocIds.isEmpty()) {
				Map<Id, Id> docIdToRecordMap = new Map<Id, Id>();
				List<String> allDocIds = new List<String>();

				for (Id recordId : recordToDocIds.keySet()) {
					for (String docId : recordToDocIds.get(recordId)) {
						docIdToRecordMap.put((Id) docId, recordId);
						allDocIds.add(docId);
					}
				}

				if (!allDocIds.isEmpty()) {
					for (ContentVersion cv : [
							SELECT Id, Title, ContentDocumentId, VersionData, FileExtension
							FROM ContentVersion
							WHERE ContentDocumentId IN :allDocIds
							ORDER BY Title ASC
					]) {
						Id recordId = docIdToRecordMap.get(cv.ContentDocumentId);
						if (recordId != null) {
							if (cv.VersionData != null) {
								fileMap.get(recordId).add(new FGXEnt_Msgs.FileWrapper(cv, recordId));
							}
						}
					}
				}

				// Retrieve Attachments (for legacy files)
				for (Attachment att : [
						SELECT Id, Name, Body, ParentId
						FROM Attachment
						WHERE Id IN :allDocIds
				]) {
					if (att.Body != null) {
						fileMap.get(att.ParentId).add(new FGXEnt_Msgs.FileWrapper(att, att.ParentId));
					}
				}
			}
		}

		return fileMap;
	}

	/**
	 * @description Retrieves file contents (ContentVersion + Attachment) for a given set of IDs.
	 * Ensures the user has access to the provided record IDs.
	 *
	 * @param contentVersionIds List of ContentVersion IDs
	 * @param recordIds List of record IDs to verify access
	 * @return List of FGXEnt_Msgs.FileWrapper containing file data
	 */
	public static List<FGXEnt_Msgs.FileWrapper> getFileContentsByBatch(List<Id> contentVersionIds, List<Id> attachmentIds, List<Id> recordIds) {
		// Ensure user has access
		Map<Id, List<FGXEnt_Msgs.FileWrapper>> fileMap = getRelatedFileMetadata(recordIds);

		// Build allowed document IDs
		Set<Id> allowedDocIds = new Set<Id>();
		Set<Id> allowedAttachmentIds = new Set<Id>();
		for (List<FGXEnt_Msgs.FileWrapper> fileList : fileMap.values()) {
			for (FGXEnt_Msgs.FileWrapper file : fileList) {
				if (file.contentDocumentId != null) {
					allowedDocIds.add(file.contentDocumentId);
				}

				if (file.attachmentId != null) {
					allowedAttachmentIds.add(file.attachmentId);
				}
			}
		}

		if (allowedDocIds.isEmpty() && allowedAttachmentIds.isEmpty()) {
			throw new IllegalArgumentException('User does not have access to any of the requested records.');
		}

		List<FGXEnt_Msgs.FileWrapper> fileDataList = new List<FGXEnt_Msgs.FileWrapper>();

		// Fetch ContentVersion file data
		if (!contentVersionIds.isEmpty() && !allowedDocIds.isEmpty()) {
			for (ContentVersion cv : [
					SELECT Id, Title, VersionData, ContentDocumentId
					FROM ContentVersion
					WHERE Id IN :contentVersionIds
					AND ContentDocumentId IN :allowedDocIds
			]) {
				if (cv.VersionData != null) {
					fileDataList.add(new FGXEnt_Msgs.FileWrapper(cv));
				}
			}
		}

		Set<Id> requestedAttachmentIdSet = new Set<Id>(attachmentIds);

		// Only keep the attachment Id's that getRelatedFileMetadata deemed the user had access to
		requestedAttachmentIdSet.retainAll(allowedAttachmentIds);

		// Fetch Attachment file data
		if (!requestedAttachmentIdSet.isEmpty()) {
			for (Attachment att : [
					SELECT Id, Name, Body, ParentId
					FROM Attachment
					WHERE Id IN :requestedAttachmentIdSet
			]) {
				if (att.Body != null) {
					fileDataList.add(new FGXEnt_Msgs.FileWrapper(att));
				}
			}
		}

		return fileDataList;
	}

	/**
	 * @description Splits a semicolon-separated string and adds the first element to the list.
	 *
	 * @param str The semicolon-separated string
	 * @param lst The list to append to
	 */
	private static void splitAndAddToSet(String str, Set<String> lst) {
		if (String.isNotBlank(str)) {
			List<String> splitStr = str.split(';');
			if (!splitStr.isEmpty()) {
				lst.add(splitStr[0]);
			}
		}
	}

	private static final Map<String, List<String>> OBJECT_DOCUMENT_FIELDS = new Map<String, List<String>>{
			Case.SObjectType.toString() => new List<String>{
					'POD_Document__c',
					'POD_Signature_Image__c',
					'Export_Documents__c',
					'Import_Documents__c',
					'Final_CI_Document__c'
			},
			Quote_Version__c.SObjectType.toString() => new List<String>{
					'RS_Rate_Sheet_ID__c'
			},
			Billing_Invoice__c.SObjectType.toString() => new List<String>{
					'Invoice_PDF_ID__c'
			}
	};

	private static final Map<String, List<String>> OBJECT_ACCOUNT_RELATIONSHIPS = new Map<String, List<String>>{
			Case.SObjectType.toString() => new List<String>{
					Case.AccountId.toString(), 'Account.ParentId'
			},
			Quote_Version__c.SObjectType.toString() => new List<String>{
					'Quote__r.Client_Account__c', 'Quote__r.Client_Account__r.ParentId'
			},
			Quote__c.SObjectType.toString() => new List<String>{
					Quote__c.Client_Account__c.toString(), 'Client_Account__r.ParentId'
			},
			Billing_Invoice__c.SObjectType.toString() => new List<String>{
					Billing_Invoice__c.Client_Account__c.toString(), 'Client_Account__r.ParentId'
			},
			Entity__c.SObjectType.toString() => new List<String>{
					Entity__c.Client_Account__c.toString(), 'Client_Account__r.ParentId'
			},
			Site__c.SObjectType.toString() => new List<String>{
					Site__c.Client_Account__c.toString(), 'Client_Account__r.ParentId'
			},
			Action_Item__c.SObjectType.toString() => new List<String>{
					'Assign_To_Client__r.AccountId', 'Assign_To_Client__r.Account.ParentId'
			},
			Asset_Unit__c.SObjectType.toString() => new List<String>{
					'Asset_Type__r.Account__c', 'Asset_Type__r.Account__r.ParentId'
			},
			Asset_Type__c.SObjectType.toString() => new List<String>{
					Asset_Type__c.Account__c.toString(), 'Account__r.ParentId'
			}
	};

	private class FGXEnt_ReportServiceException extends Exception {
	}

	/**
	 * @description Applies date filtering to reports based on account visibility settings
	 * This consolidated method enforces a strict priority order for date filtering
	 *
	 * @param reportMd Report metadata to modify
	 * @param account Account record with visibility settings
	 * @param reportFilterMap Optional map of report filters from the request
	 */
	private static void applyDateFiltering(Reports.ReportMetadata reportMd, Account account, Map<String, FGXEnt_ReportModel.FGXEnt_ReportFilter> reportFilterMap) {
		// Get the standard date filter
		Reports.StandardDateFilter standardDateFilter = reportMd.getStandardDateFilter();
		if (standardDateFilter == null) {
			return; // No date filter available on this report
		}

		// 1. Get platform-wide visibility date from config
		Date platformWideVisibilityDate = FGXEnt_GeneralConfig.PLATFORM_WIDE_VISIBILITY_DATE;

		// 2. Determine the effective minimum visibility date
		Date effectiveMinVisibilityDate;

		// If account has a specific visibility date, use it regardless of whether it's before or after the platform-wide date
		if (account != null && account.Platform_Visibility_Date__c != null) {
				effectiveMinVisibilityDate = account.Platform_Visibility_Date__c;
		} else {
			// Fall back to platform-wide setting if no account-specific date exists
			effectiveMinVisibilityDate = platformWideVisibilityDate;
		}

		// Format the effective minimum date for the Reports API
		String effectiveMinDateStr = effectiveMinVisibilityDate.year() + '-' +
				String.valueOf(effectiveMinVisibilityDate.month()).leftPad(2, '0') + '-' +
				String.valueOf(effectiveMinVisibilityDate.day()).leftPad(2, '0');

		// Check if there are user-specified filters for this date field
		Boolean userSpecifiedValidDateFilter = false;
		if (reportFilterMap != null && reportFilterMap.containsKey(standardDateFilter.getColumn())) {
			FGXEnt_ReportModel.FGXEnt_ReportFilter requestedFilter = reportFilterMap.get(standardDateFilter.getColumn());
			if (requestedFilter.visible ?? false) {
				// 3. Process user-specified date filters
				if (String.isNotBlank(requestedFilter.startDate)) {
					Date userStartDate = Date.valueOf(requestedFilter.startDate);

					// 4. Only honor user date if it's equal to or later than the effective minimum date
					if (userStartDate >= effectiveMinVisibilityDate) {
						standardDateFilter.setStartDate(requestedFilter.startDate);
						userSpecifiedValidDateFilter = true;
					} else {
						// Override with the effective minimum date if user specified an earlier date
						standardDateFilter.setStartDate(effectiveMinDateStr);
						userSpecifiedValidDateFilter = true;
					}
				} else {
					// If no start date specified, use the effective minimum date
					standardDateFilter.setStartDate(effectiveMinDateStr);
					userSpecifiedValidDateFilter = true;
				}

				// Always honor end date if specified (no restrictions on end date)
				if (String.isNotBlank(requestedFilter.endDate)) {
					standardDateFilter.setEndDate(requestedFilter.endDate);
				}
				standardDateFilter.setDurationValue('CUSTOM');
			}
		}

		// If no valid user filter was applied, set the default filter using the effective minimum date
		if (!userSpecifiedValidDateFilter) {
			standardDateFilter.setStartDate(effectiveMinDateStr);
			standardDateFilter.setDurationValue('CUSTOM');
		}

		// Apply the modified filter back to the report metadata
		reportMd.setStandardDateFilter(standardDateFilter);
	}
}
