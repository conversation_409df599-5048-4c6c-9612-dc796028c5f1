/**
 * FGXEnt_UserService
 * @description: Service to handle all the user
 * related functionality
 * @author: <PERSON><PERSON>
 * @date: 6/30/23
 */

public without sharing class FGXEnt_UserService {
    private static User userInstance = null;

    @AuraEnabled
    public static User getCurrentUser() {
        if(userInstance == null){
            userInstance = [
                    SELECT Id,
                            AccountId,
                            ContactId,
                            Email,
                            Name,
                            FirstName,
                            LastName,
                            FGXEnt_Product_Access_Level__c,
                            FGXEnt_Record_Visibility__c,
                            FGXEnt_Approval_Settings__c,
                            Account.Name,
                            Account.Advanced_Logistics_Mode__c,
                            Account.Advanced_Commercialization_Mode__c,
                            Account.Advanced_Billing_Mode__c,
                            Account.Account_Manager__c,
                            Account.Account_Manager__r.Name,
                            Account.Account_Manager__r.FirstName,
                            Account.Account_Manager__r.LastName,
                            Account.Account_Manager__r.FullPhotoUrl,
                            Account.Account_Manager__r.Email,
                            Account.Advanced_Logistics_HSCode_Required__c,
                            Account.Advanced_Logistics_ECCN_Required__c,
                            Account.Advanced_Logistics_COO_Required__c,
                            Account.Platform_Visibility_Date__c
                    FROM User
                    WHERE Id = :UserInfo.getUserId()
            ][0];
        }
        return userInstance;
    }

    @AuraEnabled
    public static Boolean isAdvancedLogistics() {
        return Constants.ENABLED.equalsIgnoreCase(FGXEnt_UserService.getCurrentUser()?.Account?.Advanced_Logistics_Mode__c);
    }

    /**
     * @description Returns the effective platform visibility date based on priority logic:
     * 1. Account-specific visibility date if present
     * 2. Global platform-wide visibility date from config if no account-specific date
     * @return Date The effective visibility date to use for filtering
     */
    @AuraEnabled
    public static Date getPlatformVisibilityDate() {
        User currentUser = getCurrentUser();

        // First check if the account has a specific visibility date
        if (currentUser?.Account?.Platform_Visibility_Date__c != null) {
            return currentUser.Account.Platform_Visibility_Date__c;
        }

        // If not, use the global default from config
        return FGXEnt_GeneralConfig.PLATFORM_WIDE_VISIBILITY_DATE;
    }

    public static FGXEnt_Msgs.UserTableList getUsers() {
        String currentUser = UserInfo.getUserId();
        Id customerEmployeeRTID = FGXEnt_Constants.CONTACT_CUSTOMER_EMPLOYEE_RT;

        String query = 'SELECT ' + String.join(FGXEnt_Constants.CONTACT_FIELDS, ',') + ' ' +
                ', (SELECT ' + String.join(FGXEnt_Constants.USER_FIELDS, ',') + ' FROM Users) ' +
                'FROM Contact ' +
                'WHERE AccountId IN ' +
                '(SELECT AccountId FROM User WHERE Id =: currentUser) ' +
                'AND RecordTypeId =: customerEmployeeRTID ';

        List<Contact> userList = (List<Contact>) Database.query(query);
        return new FGXEnt_Msgs.UserTableList(userList);
    }

    public static FGXEnt_Msgs.UserCreateResult createUser(String userRecord) {
        FGXEnt_Msgs.UserCreateRequest userCreateRequest = new FGXEnt_Msgs.UserCreateRequest().parse(userRecord);
        FGXEnt_Msgs.UserCreateResult result;

        Contact c = userCreateRequest.getContact();
        User u = userCreateRequest.getUser();

        Savepoint sp = Database.setSavepoint();

        Database.DMLOptions dlo = new Database.DMLOptions();
        dlo.emailHeader.triggerUserEmail = false;

        try {
            if (checkUserOnAccount(u)) {
                throw new UsernameDuplicateException('Related Duplicate Found');
            }

            Database.SaveResult saveResult = Database.insert(c);
            if (saveResult.isSuccess()) {
                u.ContactId = c.Id;
                saveResult = Database.insert(u, dlo);
            } else {
                throw new UsernameDuplicateException('Failed to create user');
            }
            result = new FGXEnt_Msgs.UserCreateResult(saveResult);

        } catch (Exception ex) {
            Database.rollback(sp);
            result = new FGXEnt_Msgs.UserCreateResult(ex);

        }

        return result;
    }

    public static FGXEnt_Msgs.UserCreateResult updateUser(String userRecord) {
        FGXEnt_Msgs.UserCreateRequest userCreateRequest = (FGXEnt_Msgs.UserCreateRequest) System.JSON.deserialize(userRecord, FGXEnt_Msgs.UserCreateRequest.class);
        FGXEnt_Msgs.UserCreateResult result;

        User u = userCreateRequest.user;
        Contact c = userCreateRequest.getContact(u.ContactId);
        Savepoint sp = Database.setSavepoint();

        try {
            Database.SaveResult userSaveResult = Database.update(u);
            if (userSaveResult.isSuccess()) {
                Database.SaveResult contactSaveResult = Database.update(c);

                if (!contactSaveResult.isSuccess()) {
                    Database.rollback(sp);
                }
            } else {
                Database.rollback(sp);
            }
            result = new FGXEnt_Msgs.UserCreateResult();
        } catch (Exception ex) {
            Database.rollback(sp);
            result = new FGXEnt_Msgs.UserCreateResult(ex);
        }

        return result;
    }

    public static FGXEnt_Msgs.UserCreateResult updateContact(String userRecord) {
        FGXEnt_Msgs.UserCreateResult result;

        Contact c = (Contact) JSON.deserialize(userRecord, Contact.class);
        Savepoint sp = Database.setSavepoint();

        try {
            Database.SaveResult contactSaveResult = Database.update(c);

            if (!contactSaveResult.isSuccess()) {
                Database.rollback(sp);
            }
            result = new FGXEnt_Msgs.UserCreateResult();
        } catch (Exception ex) {
            Database.rollback(sp);
            result = new FGXEnt_Msgs.UserCreateResult(ex);
        }

        return result;
    }

    public static FGXEnt_Msgs.UserCreateResult inviteUser(String userRecord) {
        FGXEnt_Msgs.UserTableItem userCreateRequest = new FGXEnt_Msgs.UserTableItem().parse(userRecord);
        FGXEnt_Msgs.UserCreateResult result;

        Contact c = userCreateRequest.contact;
        User u = userCreateRequest.getUser();

        Database.DMLOptions dlo = new Database.DMLOptions();
        dlo.emailHeader.triggerUserEmail = false;

        try {
            u.ContactId = c.Id;
            Database.SaveResult saveResult = Database.insert(u, dlo);
            result = new FGXEnt_Msgs.UserCreateResult(saveResult);

        } catch (Exception ex) {
            result = new FGXEnt_Msgs.UserCreateResult(ex);
        }

        return result;
    }

    public static FGXEnt_Msgs.UserResetResult resetPortalUserPassword(String userRecords) {
        List<User> users = (List<User>) System.JSON.deserialize(userRecords, List<User>.class);
        return handleReset(users, false);
    }

    public static FGXEnt_Msgs.UserResetResult resendInvitation(String userRecords) {
        List<User> users = (List<User>) System.JSON.deserialize(userRecords, List<User>.class);
        return handleReset(users, true);
    }

    public static FGXEnt_Msgs.UserDeactivateResult deactivatePortalUser(String userRecords) {
        List<User> users = (List<User>) System.JSON.deserialize(userRecords, List<User>.class);
        List<User> usersToUpdate = new List<User>();
        for(User u : users) {
            usersToUpdate.add(new User(Id = u.Id, IsActive = false));
        }
        return new FGXEnt_Msgs.UserDeactivateResult(Database.update(usersToUpdate));
    }

    public static void sendWelcomeEmail(List<User> users) {
        Set<Id> usersToSendTo = new Set<Id>();
        for(User u : users) {
            if(u.ProfileId == FGXEnt_Constants.FGX_ENTERPRISE_STARTER_PROFILE) {
                usersToSendTo.add(u.Id);
            }
        }

        if(!usersToSendTo.isEmpty()) {
            for(Id userId: usersToSendTo) {
                System.resetPassword(userId, true);
            }
            FGXEnt_EmailService.sendWelcomeEmail(usersToSendTo);
        }
    }

    private static FGXEnt_Msgs.UserResetResult handleReset(List<User> users, Boolean sendWelcomeEmail) {
        FGXEnt_Msgs.UserResetResult result;

        try {
            Boolean allReset = true;
            Integer successCount = 0;

            for (User user : users) {
                Boolean isSuccess = Site.forgotPassword(user.Username);

                if (!isSuccess) {
                    allReset = isSuccess;
                } else {
                    successCount++;
                }
            }

            if(sendWelcomeEmail) {
                FGXEnt_EmailService.sendWelcomeEmail(new Map<Id, User>(users).keySet());
            }

            result = new FGXEnt_Msgs.UserResetResult(allReset, successCount);
        } catch (Exception ex) {
            result = new FGXEnt_Msgs.UserResetResult(ex);
        }
        return result;
    }

    private static Boolean checkUserOnAccount(User record) {
        FGXEnt_Msgs.UserTableList userList = getUsers();

        for (FGXEnt_Msgs.UserTableItem ut : userList.contacts) {
            if (record.Username == ut.userColumnEmail && ut.statusColumn == FGXEnt_Constants.USER_STATUS_DEACTIVATED) {
                return true;
            }
        }
        return false;
    }

    @AuraEnabled(Cacheable=true)
    public static Boolean hasAccountShipmentApprover() {
        User currentUser = getCurrentUser();
        return hasShipmentApprover(currentUser.AccountId);
    }

    @AuraEnabled(Cacheable=true)
    public static Boolean hasShipmentApprover(Id accountId) {
        List<User> accountUsers = getShipmentApprover(accountId);
        return !accountUsers.isEmpty();
    }

    @AuraEnabled
    public static List<User> getShipmentApprover(Id accountId) {
        List<User> accountUsers = [
                SELECT Id, Email
                FROM User
                WHERE AccountId = :accountId
                AND FGXEnt_Approval_Settings__c = 'Shipment Approver'
                LIMIT 1
        ];
        return accountUsers;
    }

    private FGXEnt_UserService() {}
    public class UsernameDuplicateException extends Exception {}
}