/**
 * FGXEnt_QuoteBookingContentService
 * @description: Service for the Quote Booking stepper
 * to handle all the different actions the Controller needs
 * @author: <PERSON><PERSON>
 * @date: 4/29/24
 */

public without sharing class FGXEnt_QuoteBookingContentService {

    /**
     * @description Returns the Quote, Quote Version and all related
     * BOM and Package Lines for the Quote Request Flow
     * @param recordId: Case record id
     * @param relatedRecordId: Quote record id if we
     * are doing a conversion
     * @return
     */
    public static QuoteBookingStepperData getData(String recordId, String relatedRecordId, String relatedVersionRecordId) {
        User u = FGXEnt_UserService.getCurrentUser();
        Date visibilityDate = FGXEnt_UserService.getPlatformVisibilityDate();
        String contactId = u.ContactId;
        String accountId = u.AccountId;

        Case caseRecord = new Case(CurrencyIsoCode = 'USD', Drop_Ship_to_FGX__c = true);
        Quote__c quoteRecord;

        List<Commercial_Invoice_Line__c> commercialInvoiceLines = new List<Commercial_Invoice_Line__c>();
        List<Shipment_Package_Line__c> shipmentPackageLines = new List<Shipment_Package_Line__c>();

        if(String.isBlank(recordId) && String.isNotBlank(relatedRecordId) && String.isNotBlank(relatedVersionRecordId)) {

            String relatedQuery = 'SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Quote_Version__c') +
                    ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'BOM_Line__c') +
                    ' FROM BOM_Lines__r ORDER BY CreatedDate ASC) ' +
                    ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'PackageLine__c') +
                    ' FROM Packages__r ORDER BY CreatedDate ASC) ' +
                    ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Quote_Line__c') +
                    ' FROM Quote_Lines__r ORDER BY SortOrder__c ASC) ' +
                    ' FROM Quote_Version__c ';

            List<String> qvFilters = new List<String>{
                'Id =: relatedVersionRecordId'
            };

            if (String.isNotBlank(u.AccountId)) {
                qvFilters.add('Quote__r.Client_Account__c  =: accountId');
            }

            if(FGXEnt_Utilities.isUserRecordOnlyRestricted(u)) {
                qvFilters.add('Quote__r.Client_Contact__c =: contactId');
            }

            if(visibilityDate != null) {
                qvFilters.add('CreatedDate >=: visibilityDate ');
            }

            if (!qvFilters.isEmpty()) {
                relatedQuery += 'WHERE ' + String.join(qvFilters, ' AND ');
            }

            List<Quote_Version__c> quoteVersions = (List<Quote_Version__c>) Database.query(relatedQuery);

            Quote_Version__c selectedQuoteVersion = quoteVersions.get(0);
            caseRecord = getShipmentFromQuote(relatedRecordId, selectedQuoteVersion);
            caseRecord.Target_Date__c = null; // users must re-confirm target date
            quoteRecord = selectedQuoteVersion.Quote__r;
            commercialInvoiceLines = getCommercialInvoiceLines(selectedQuoteVersion);
            shipmentPackageLines = getShipmentPackageLines(selectedQuoteVersion);

            return new QuoteBookingStepperData(
                    caseRecord,
                    quoteRecord,
                    selectedQuoteVersion,
                    commercialInvoiceLines,
                    shipmentPackageLines
            );

        } else {
            String query = 'SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Case') +
                    ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Commercial_Invoice_Line__c') +
                    ' FROM Commercial_Invoice_Lines__r WHERE Deleted__c != true ORDER BY CreatedDate ASC) ' +
                    ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Shipment_Package_Line__c') +
                    ' FROM Shipment_Package_Lines__r ORDER BY CreatedDate ASC) ' +
                    ' FROM Case ';

            List<String> caseFilters = new List<String>{
                    'Id =: recordId'
            };

            if (String.isNotBlank(accountId)) {
                caseFilters.add('AccountId  =: accountId');
            }

            if(FGXEnt_Utilities.isUserRecordOnlyRestricted(u)) {
                caseFilters.add('ContactId =: contactId');
            }

            if (!caseFilters.isEmpty()) {
                query += 'WHERE ' + String.join(caseFilters, ' AND ');
            }

            List<Case> cases = (List<Case>) Database.query(query);
            if(!cases.isEmpty()) {
                caseRecord = cases.get(0);
                // these fields should not be copied on clone
                caseRecord.Outbound_Ticket_Num__c = null;
                caseRecord.Inbound_Ticket_Num__c  = null;
                caseRecord.Target_Date__c = null;
                caseRecord.Client_Approval_Status__c = null;
                caseRecord.Client_Approval_Details__c = null;
                caseRecord.Slack_Channel__c = null;

                commercialInvoiceLines = caseRecord.Commercial_Invoice_Lines__r;
                shipmentPackageLines = caseRecord.Shipment_Package_Lines__r;

                if(caseRecord.CurrencyIsoCode != caseRecord.BOM_Currency_Code__c && String.isNotBlank(caseRecord.BOM_Currency_Code__c)) {
                    caseRecord.CurrencyIsoCode = caseRecord.BOM_Currency_Code__c;
                }
            }

            return new QuoteBookingStepperData(
                    caseRecord,
                    quoteRecord,
                    null,
                    commercialInvoiceLines,
                    shipmentPackageLines
            );
        }
    }

    private static Case getShipmentFromQuote(String quoteId, Quote_Version__c selectedQuoteVersion) {
        User u = FGXEnt_UserService.getCurrentUser();

        Quote__c qt = [
            SELECT Id,
                Client_Account__c,
                Client_Account__r.Billing_E_mail__c,
                Client_Account__r.Default_Billing_Address__c,
                Client_Contact__c,
                Client_Ref__c,
                Client_Ref2__c,
                Export_Entity__c,
                Import_Entity__c,
                DIMWeightEntryType__c,
                Destination_Site__c,
                Destination_Site__r.Address_Line_1__c,
                Destination_Site__r.Address_Line_2__c,
                Destination_Site__r.Address_Line_3__c,
                Destination_Site__r.City__c,
                Destination_Site__r.Post_Code__c,
                Destination_Site__r.State__c,
                Destination_Site__r.State_US__c,
                Destination_Site__r.Country__c,
                Destination_Site__r.County_Province_Other__c,
                Destination_Site__r.Company_Name__c,
                Destination_Site__r.Site_Contact_First_Name__c,
                Destination_Site__r.Site_Contact_Last_Name__c,
                Destination_Site__r.Site_Contact_Email__c,
                Destination_Site__r.Site_Contact_Phone__c,
                Destination_Address_Line_1__c,
                Destination_Address_Line_2__c,
                Destination_Address_Line_3__c,
                Destination_City__c,
                Destination_Company_Name__c,
                Destination_Country__c,
                Destination_County_Province_Other__c,
                Destination_Google_Address__c,
                Destination_Location_Type__c,
                Destination_Post_Code__c,
                Destination_Search_Choice__c,
                Destination_SFDC_Selected_Address__c,
                Destination_State__c,
                Destination_US_State__c,
                Drop_Ship_to_FGX__c,
                EOR_Needed__c,
                Equipment_Condition__c,
                Active_QV_HWValue__c,
                Active_Version__r.Hardware_Value_Only__c,
                Active_Version__r.Include_Insurance__c,
                IOR_Needed__c,
                Origin_Site__c,
                Origin_Address_Cache__c,
                Origin_Site__r.Address_Line_1__c,
                Origin_Site__r.Address_Line_2__c,
                Origin_Site__r.Address_Line_3__c,
                Origin_Site__r.City__c,
                Origin_Site__r.Post_Code__c,
                Origin_Site__r.State__c,
                Origin_Site__r.State_US__c,
                Origin_Site__r.Country__c,
                Origin_Site__r.County_Province_Other__c,
                Origin_Site__r.Company_Name__c,
                Origin_Site__r.Site_Contact_First_Name__c,
                Origin_Site__r.Site_Contact_Last_Name__c,
                Origin_Site__r.Site_Contact_Email__c,
                Origin_Site__r.Site_Contact_Phone__c,
                Origin_Address_Line_1__c,
                Origin_Address_Line_2__c,
                Origin_Address_Line_3__c,
                Origin_City__c,
                Origin_Company_Name__c,
                Origin_Country__c,
                Origin_County_Province_Other__c,
                Origin_Google_Address__c,
                Origin_Location_Type__c,
                Origin_Post_Code__c,
                Origin_Search_Choice__c,
                Origin_SFDC_Selected_Address__c,
                Origin_State__c,
                Origin_US_State__c,
                Packing_Configuration__c,
                Packing_Required__c,
                Requested_Service_Level__c,
                Summary__c,
                Active_QV_ShipmentValue__c,
                Short_Description__c,
                Show_VAT_Reclaim__c,
                Active_QV_SWValue__c,
                Special_Economic_Zone__c,
                Target_Delivery_Date__c,
                Transaction_Type__c,
                Billing_Account__c,
                Billing_Account__r.Billing_E_mail__c,
                Billing_Account__r.Default_Billing_Address__c,
                Active_Version__c,
                Origin_SFDC_Selected_Address__r.Email__c,
                Origin_SFDC_Selected_Address__r.First_Name__c,
                Origin_SFDC_Selected_Address__r.Last_Name__c,
                Origin_SFDC_Selected_Address__r.Mobile_Phone__c,
                Origin_SFDC_Selected_Address__r.Office_Phone__c,
                Origin_SFDC_Selected_Address__r.Office_Phone_Extension__c,
                Origin_SFDC_Selected_Address__r.Known_Shipper_ID__c,
                Origin_SFDC_Selected_Address__r.Tax_Id__c,
                Destination_SFDC_Selected_Address__r.Email__c,
                Destination_SFDC_Selected_Address__r.First_Name__c,
                Destination_SFDC_Selected_Address__r.Last_Name__c,
                Destination_SFDC_Selected_Address__r.Mobile_Phone__c,
                Destination_SFDC_Selected_Address__r.Office_Phone__c,
                Destination_SFDC_Selected_Address__r.Office_Phone_Extension__c,
                TxnSurvey_Related_Entity__c,
                TxnSurvey_Asset_Transfer__c,
                TxnSurvey_Funds_Transfer__c
            FROM Quote__c
            WHERE Id =: quoteId
            //TODO: check account, contact, and visibility date here
        ];

        Case c = new Case(
            AccountId = qt.Client_Account__c,
            ContactId = qt.Client_Contact__c,
            Client_Ref__c = selectedQuoteVersion.Client_Ref__c,
            Client_Ref2__c = selectedQuoteVersion.Client_Ref2__c,
            Billing_Override_Account__c = qt.Billing_Account__c,
            DIMWeightEntryType__c = qt.DIMWeightEntryType__c,
            Destination_Site__c = qt.Destination_Site__c,
            Destination_Address_Line_1__c = qt.Destination_Address_Line_1__c,
            Destination_Address_Line_2__c = qt.Destination_Address_Line_2__c,
            Destination_Address_Line_3__c = qt.Destination_Address_Line_3__c,
            Destination_City__c = qt.Destination_City__c,
            Destination_Company_Name__c = qt.Destination_Company_Name__c,
            Destination_Country__c = qt.Destination_Country__c,
            Destination_County_Province_Other__c = qt.Destination_County_Province_Other__c,
            Destination_Google_Address__c = qt.Destination_Google_Address__c,
            Destination_Location_Type__c = qt.Destination_Location_Type__c,
            Destination_Post_Code__c = qt.Destination_Post_Code__c,
            Destination_Search_Choice__c = qt.Destination_Search_Choice__c,
            Destination_SFDC_Address_New__c = qt.Destination_SFDC_Selected_Address__c,
            Destination_State__c = qt.Destination_State__c,
            Destination_US_State__c = qt.Destination_US_State__c,
            Destination_Email__c = qt.Destination_Site__r.Site_Contact_Email__c,
            Destination_Mobile_Phone__c = qt.Destination_Site__r.Site_Contact_Phone__c,
            Drop_Ship_to_FGX__c = selectedQuoteVersion.Drop_Ship_to_FGX__c,
            EOR_Needed__c = qt.EOR_Needed__c,
            Equipment_Condition__c = qt.Equipment_Condition__c,
            Hardware_Value__c = selectedQuoteVersion.Hardware_Value__c,
            Hardware_Value_Only__c = selectedQuoteVersion.Hardware_Value_Only__c,
            Insured_Value__c = qt.Active_QV_ShipmentValue__c,
            IOR_Needed__c = qt.IOR_Needed__c,
            Origin_Site__c = qt.Origin_Site__c,
            Origin_Address_Line_1__c = qt.Origin_Address_Line_1__c,
            Origin_Address_Line_2__c = qt.Origin_Address_Line_2__c,
            Origin_Address_Line_3__c = qt.Origin_Address_Line_3__c,
            Origin_City__c = qt.Origin_City__c,
            Origin_Company_Name__c = qt.Origin_Company_Name__c,
            Origin_Country__c = qt.Origin_Country__c,
            Origin_County_Province_Other__c = qt.Origin_County_Province_Other__c,
            Origin_Google_Address__c = qt.Origin_Google_Address__c,
            Origin_Location_Type__c = qt.Origin_Location_Type__c,
            Origin_Post_Code__c = qt.Origin_Post_Code__c,
            Origin_Search_Choice__c = qt.Origin_Search_Choice__c,
            Origin_SFDC_Address_New__c = qt.Origin_SFDC_Selected_Address__c,
            Origin_State__c = qt.Origin_State__c,
            Origin_US_State__c = qt.Origin_US_State__c,
            Origin_Email__c = qt.Origin_Site__r.Site_Contact_Email__c,
            Origin_Mobile_Phone__c = qt.Origin_Site__r.Site_Contact_Phone__c,
            Current_Packaging__c = qt.Packing_Configuration__c,
            Packing_Crating_Required__c = qt.Packing_Required__c,
            Service_Level__c = qt.Requested_Service_Level__c,
            Summary__c = selectedQuoteVersion.Client_Comments__c,
            Shipment_Value__c = qt.Active_QV_ShipmentValue__c,
            Show_VAT_Reclaim__c = qt.Show_VAT_Reclaim__c,
            Software_Value__c = selectedQuoteVersion.Software_Value__c,
            Special_Economic_Zone__c = qt.Special_Economic_Zone__c,
            Target_Date__c = selectedQuoteVersion.Target_Delivery_Date__c != null ? selectedQuoteVersion.Target_Delivery_Date__c : qt.Target_Delivery_Date__c,
            Transaction_Type__c = selectedQuoteVersion.Transaction_Type__c,
            Related_Quote_Version__c = selectedQuoteVersion.Id,
            BOM_Conversion_Date__c = selectedQuoteVersion.BOM_Conversion_Date__c,
            BOM_Conversion_Rate__c = selectedQuoteVersion.BOM_Conversion_Rate__c,
            BOM_Currency_Code__c = selectedQuoteVersion.BOM_Currency_Code__c,
            CurrencyIsoCode = selectedQuoteVersion.BOM_Currency_Code__c,
            Packing_Type__c = selectedQuoteVersion.Packing_Type__c,
            Show_Duties__c = selectedQuoteVersion.Itemized_Duties__c,
            Export_Entity__c = selectedQuoteVersion.Export_Entity__c,
            Import_Entity__c = selectedQuoteVersion.Import_Entity__c,
            Purchasing_Entity__c = selectedQuoteVersion.Purchasing_Entity__c,
            Ultimate_Owner_Entity__c = selectedQuoteVersion.Ultimate_Owner_Entity__c,
            TxnSurvey_Related_Entity__c = selectedQuoteVersion.TxnSurvey_Related_Entity__c,
            TxnSurvey_Asset_Transfer__c = selectedQuoteVersion.TxnSurvey_Asset_Transfer__c,
            TxnSurvey_Funds_Transfer__c = selectedQuoteVersion.TxnSurvey_Funds_Transfer__c,
            Short_Description__c = selectedQuoteVersion.Short_Description__c,
            Subject = selectedQuoteVersion.Short_Description__c,
            Destination_First_Name__c = qt.Destination_Site__r.Site_Contact_First_Name__c,
            Destination_Last_Name__c = qt.Destination_Site__r.Site_Contact_Last_Name__c,
            Origin_First_Name__c = qt.Origin_Site__r.Site_Contact_First_Name__c,
            Origin_Last_Name__c = qt.Origin_Site__r.Site_Contact_Last_Name__c,
            Origin = 'Booked by FGX',
            Status = Constants.CASE_STATUS_BOOKED,
            Contents_Description__c = '',
            RecordTypeId = Constants.CASE_IT_SHIPMENT_RT
        );

        if(!qt.Drop_Ship_to_FGX__c && selectedQuoteVersion.Drop_Ship_to_FGX__c) {
            c.Origin_Country__c = Constants.COUNTRIES_BY_CODE_MAP.get('US').Id;
            c.Origin_City__c = 'Livingston';
            c.Origin_US_State__c = 'New Jersey';
            c.Origin_Google_Address__c = u.Account.Name + ' c/o First Global Xpress, 7 Industrial Parkway, Unit #10, Livingston, NJ 07039 USA';
            c.Origin_Company_Name__c = u.Account.Name + ' c/o First Global Xpress';
            c.Origin_Address_Line_1__c = '7 Industrial Parkway';
            c.Origin_Address_Line_2__c = 'Unit #10';
            c.Origin_Post_Code__c = '07039';
            c.Origin_Location_Type__c = 'Warehouse';
            c.Origin_Site__c = null;
            c.EOR_Needed__c = false;
        }

        if(qt.Billing_Account__c != null){
            c.Billing_Address__c = qt.Billing_Account__r.Default_Billing_Address__c;
            c.Billing_Email__c = qt.Billing_Account__r.Billing_E_mail__c;
        }
        else{
            c.Billing_Address__c = qt.Client_Account__r.Default_Billing_Address__c;
            c.Billing_Email__c = qt.Client_Account__r.Billing_E_mail__c;
        }

        if(!qt.Active_Version__r.Include_Insurance__c){
            c.Insured_Value__c = 0;
        }

        if(qt.Drop_Ship_to_FGX__c && !selectedQuoteVersion.Drop_Ship_to_FGX__c) {
            if(String.isNotBlank(qt.Origin_Address_Cache__c)) {
                FGXEnt_OriginAddressCache cache = new FGXEnt_OriginAddressCache(qt.Origin_Address_Cache__c);
                if(String.isNotBlank(cache.site)) {
                    List<Site__c> cachedSite = [
                        SELECT Id,
                                Address_Line_1__c,
                                Address_Line_2__c,
                                Address_Line_3__c,
                                City__c,
                                Post_Code__c,
                                State__c,
                                State_US__c,
                                Country__c,
                                County_Province_Other__c,
                                Company_Name__c,
                                Site_Contact_First_Name__c,
                                Site_Contact_Last_Name__c,
                                Site_Contact_Email__c,
                                Site_Contact_Phone__c
                        FROM Site__c
                        WHERE Id =: cache.site
                    ];

                    if(!cachedSite.isEmpty()) {
                        c.Origin_Site__c = cache.site;
                        c.Origin_Address_Line_1__c = cachedSite[0].Address_Line_1__c;
                        c.Origin_Address_Line_2__c = cachedSite[0].Address_Line_2__c;
                        c.Origin_Address_Line_3__c = cachedSite[0].Address_Line_3__c;
                        c.Origin_City__c = cachedSite[0].City__c;
                        c.Origin_Post_Code__c = cachedSite[0].Post_Code__c;
                        c.Origin_State__c = cachedSite[0].State__c;
                        c.Origin_US_State__c = cachedSite[0].State_US__c;
                        c.Origin_Country__c = cachedSite[0].Country__c;
                        c.Origin_County_Province_Other__c = cachedSite[0].County_Province_Other__c;
                        c.Origin_Company_Name__c = cachedSite[0].Company_Name__c;
                        c.Origin_First_Name__c = cachedSite[0].Site_Contact_First_Name__c;
                        c.Origin_Last_Name__c = cachedSite[0].Site_Contact_Last_Name__c;
                        c.Origin_Email__c = cachedSite[0].Site_Contact_Email__c;
                        c.Origin_Mobile_Phone__c = cachedSite[0].Site_Contact_Phone__c;
                        c.Origin_Search_Choice__c = 'Site';
                    }

                } else if(String.isNotBlank(cache.city) || String.isNotBlank(cache.country)) {
                    c.Origin_City__c = cache.city;
                    c.Origin_Country__c = cache.country;
                    if(String.isNotBlank(cache.state)) {
                        c.Origin_US_State__c = cache.state;
                    }
                    c.Origin_Search_Choice__c = 'Google';
                    c.Origin_Company_Name__c = null;
                    c.Origin_Address_Line_1__c = null;
                    c.Origin_Address_Line_2__c = null;
                    c.Origin_Address_Line_3__c = null;
                    c.Origin_County_Province_Other__c = null;
                    c.Origin_Post_Code__c = null;
                    c.Origin_First_Name__c = null;
                    c.Origin_Last_Name__c = null;
                    c.Origin_Email__c = null;
                    c.Origin_Mobile_Phone__c = null;
                }
            }
        }
        return c;
    }

    private static List<Commercial_Invoice_Line__c> getCommercialInvoiceLines(Quote_Version__c qv) {
        List<Commercial_Invoice_Line__c> commercialInvoiceLines = new List<Commercial_Invoice_Line__c>();

        for (BOM_Line__c bomLine : qv.BOM_Lines__r) {
            commercialInvoiceLines.add(
                new Commercial_Invoice_Line__c(
                    AutoComplete__c = bomLine.AutoComplete__c,
                    Autofill_Result__c = bomLine.Autofill_Result__c,
                    Category__c = bomLine.Category__c,
                    Condition__c = bomLine.Condition__c,
                    Country_of_Origin__c = bomLine.Country_of_Origin__c,
                    Data_Source__c = bomLine.Data_Source__c,
                    Description__c = bomLine.Description__c,
                    HS_Code__c = bomLine.HS_Code__c,
                    Duty_Rate__c = bomLine.Duty_Rate__c,
                    ECCN__c = bomLine.ECCN__c,
                    Manufacturer__c = bomLine.Manufacturer__c,
                    Orig_HS_Code__c = bomLine.Orig_HS_Code__c,
                    Part_Number__c = bomLine.Part_Number__c,
                    Quantity__c = bomLine.Quantity__c,
                    Product_Type__c = bomLine.Product_Type__c,
                    Value__c = bomLine.Value__c,
                    Created_By_Type__c = bomLine.Created_By_Type__c,
                    CurrencyIsoCode = bomLine.CurrencyIsoCode
                )
            );
        }
        return commercialInvoiceLines;
    }

    private static List<Shipment_Package_Line__c> getShipmentPackageLines(Quote_Version__c qv) {
        List<Shipment_Package_Line__c> shipmentPackageLines = new List<Shipment_Package_Line__c>();

        for (PackageLine__c packageLine : qv.Packages__r) {
            shipmentPackageLines.add(
                new Shipment_Package_Line__c(
                    Height__c = packageLine.Height__c,
                    Length__c = packageLine.Length__c,
                    Length_Unit__c = packageLine.Length_Unit__c,
                    Num_Pcs__c = packageLine.Num_Pcs__c,
                    Packing_Type__c = packageLine.Packing_Type__c,
                    Unit_Weight__c = packageLine.Unit_Weight__c,
                    Weight_Unit__c = packageLine.Weight_Unit__c,
                    Width__c = packageLine.Width__c
                )
            );
        }
        return shipmentPackageLines;
    }

    /**
     * @description Handles taking in the raw
     * BOM data that is parsed from a PDF and
     * passing it to OpenAI to get the BOMGPTResponse
     * @param data
     * @param recordId
     * @return
     */
    public static FGXEnt_OpenAIService.BOMGPTResponse getBOMLinesGPT(String data, Id recordId) {
        String prompt = FGXEnt_UserService.isAdvancedLogistics() ? 'QuoteRequestBOMInputAL' : 'QuoteRequestBOMInput';
        OpenAIConfig config = OpenAIConfig.getConfig(prompt);
        FGXEnt_OpenAIService.BOMGPTResponse response = FGXEnt_OpenAIService.invokeRequest(
                new OpenAIClient.OpenAIRequest(
                        String.join(new List<String>{config.prompt, data}, ' : '),
                        config
                )
        );

        if(response.parsingSuccess) {
            if(recordId != null){
                List<Commercial_Invoice_Line__c> bomLinesToDelete = [SELECT Id FROM Commercial_Invoice_Line__c WHERE Shipment__c =: recordId AND Deleted__c != true];
                if(!bomLinesToDelete.isEmpty()) {
                    List<Database.DeleteResult> sr = Database.delete(bomLinesToDelete);
                    response.bomLineDeleteErrors = FGXEnt_Utilities.getErrors(sr);
                }
                if(response.bomLineDeleteErrors.isEmpty()) {
                    update new Case(Id = recordId, BOM_CSV_Ingested__c = false, BOM_AI_Ingested__c = true);
                }
            }
        }

        return response;
    }

    /**
     * @description Handles taking in the raw
     * CSV data that is parsed in JS.
     * @param data
     * @param recordId
     * @return
     */
    public static FGXEnt_CSVParserService.BOMCSVResponse getBOMLinesCSV(String data, Id recordId) {
        FGXEnt_CSVParserService.BOMCSVResponse response = FGXEnt_CSVParserService.getBOMLines(
                data,
                recordId,
                FGXEnt_UserService.isAdvancedLogistics()
        );

        if(response.parsingSuccess) {
            if(recordId != null) {
                List<Commercial_Invoice_Line__c> bomLinesToDelete = [SELECT Id FROM Commercial_Invoice_Line__c WHERE Shipment__c =: recordId AND Deleted__c != true];
                if(!bomLinesToDelete.isEmpty()) {
                    List<Database.DeleteResult> sr = Database.delete(bomLinesToDelete);
                    response.bomLineDeleteErrors = FGXEnt_Utilities.getErrors(sr);
                }
                if(response.bomLineDeleteErrors.isEmpty()) {
                    update new Case(Id = recordId, BOM_CSV_Ingested__c = true, BOM_AI_Ingested__c = false);
                }
            }
        }
        return response;
    }

    /**
     * @description Handles updating the quote and quote version
     * record when changing steps in the quote request flow. It also
     * handles twinning fields and conditionally setting fields based
     * on others.
     * @param record
     * @param relatedRecord
     * @return
     */
    public static FlowInterviewDMLResult updateRecord(Case record, Quote__c relatedRecord, String relatedVersionRecordId) {
        User u = FGXEnt_UserService.getCurrentUser();

        Case caseBefore;

        if(record.Id != null)  {
            caseBefore = [
                    SELECT Id, Target_Date__c, Origin_Country__c, Destination_Country__c
                    FROM Case
                    WHERE Id =: record.Id
            ][0];

            if(caseBefore != null) { }
        } else {
            record.OwnerId = Constants.FGX_SERVICE_QUEUE;
        }

        if(record.AccountId == null) {
            record.AccountId = u.AccountId;
            record.ContactId = u.ContactId;
        }

        Id billingAcct = Constants.ENABLED.equalsIgnoreCase(u.Account.Advanced_Billing_Mode__c) ? record.Billing_Override_Account__c : record.AccountId;
        List<Account> billingAccount = [SELECT Id, Billing_E_mail__c, Default_Billing_Address__c FROM Account WHERE Id =: billingAcct];

        if(!billingAccount.isEmpty()) {
            record.Billing_Address__c = billingAccount[0].Default_Billing_Address__c;
            record.Billing_Email__c = billingAccount[0].Billing_E_mail__c;
        }

        if(record.Drop_Ship_to_FGX__c || (String.isBlank(record.Origin_City__c) && String.isBlank(record.Origin_Country__c) && String.isBlank(record.Origin_Site__c) )) {
            record.Drop_Ship_to_FGX__c = true;
            record.Origin_Country__c = Constants.COUNTRIES_BY_CODE_MAP.get('US').Id;
            record.Origin_City__c = 'Livingston';
            record.Origin_US_State__c = 'New Jersey';
            record.Origin_Google_Address__c = u.Account.Name + ' c/o First Global Xpress, 7 Industrial Parkway, Unit #10, Livingston, NJ 07039 USA';
            record.Origin_Company_Name__c = u.Account.Name + ' c/o First Global Xpress';
            record.Origin_Address_Line_1__c = '7 Industrial Parkway';
            record.Origin_Address_Line_2__c = 'Unit #10';
            record.Origin_Post_Code__c = '07039';
            record.Origin_First_Name__c = 'Christian';
            record.Origin_Last_Name__c = 'Ruiz';
            record.Origin_Mobile_Phone__c = '****** 352 9390';
            record.Origin_Email__c = '<EMAIL>';
            record.Origin_Location_Type__c = 'Warehouse';
            record.Origin_Site__c = null;
            record.EOR_Needed__c = false;

        } else {
            record.Drop_Ship_to_FGX__c = false;
        }

        if(String.isNotBlank(record.Origin_Site__c)) {
            List<Site__c> originSite = [
                    SELECT Id, Address_Line_1__c, Address_Line_2__c,
                            Address_Line_3__c, City__c, Post_Code__c,
                            State__c, State_US__c, Country__c,
                            County_Province_Other__c, Company_Name__c
                    FROM Site__c
                    WHERE Id =: record.Origin_Site__c
            ];

            record.Origin_Address_Line_1__c = originSite[0].Address_Line_1__c;
            record.Origin_Address_Line_2__c = originSite[0].Address_Line_2__c;
            record.Origin_Address_Line_3__c = originSite[0].Address_Line_3__c;
            record.Origin_City__c = originSite[0].City__c;
            record.Origin_Post_Code__c = originSite[0].Post_Code__c;
            record.Origin_State__c = originSite[0].State__c;
            record.Origin_US_State__c = originSite[0].State_US__c;
            record.Origin_Country__c = originSite[0].Country__c;
            record.Origin_County_Province_Other__c = originSite[0].County_Province_Other__c;
            record.Origin_Company_Name__c = originSite[0].Company_Name__c;
            record.Origin_Search_Choice__c = 'Site';
        } else {
            record.Origin_Search_Choice__c = null;
        }

        if(String.isNotBlank(record.Destination_Site__c)) {
            List<Site__c> destinationSite = [
                SELECT Id, Address_Line_1__c, Address_Line_2__c,
                        Address_Line_3__c, City__c, Post_Code__c,
                        State__c, State_US__c, Country__c,
                        County_Province_Other__c, Company_Name__c
                FROM Site__c
                WHERE Id =: record.Destination_Site__c
            ];

            record.Destination_Address_Line_1__c = destinationSite[0].Address_Line_1__c;
            record.Destination_Address_Line_2__c = destinationSite[0].Address_Line_2__c;
            record.Destination_Address_Line_3__c = destinationSite[0].Address_Line_3__c;
            record.Destination_City__c = destinationSite[0].City__c;
            record.Destination_Post_Code__c = destinationSite[0].Post_Code__c;
            record.Destination_State__c = destinationSite[0].State__c;
            record.Destination_US_State__c = destinationSite[0].State_US__c;
            record.Destination_Country__c = destinationSite[0].Country__c;
            record.Destination_County_Province_Other__c = destinationSite[0].County_Province_Other__c;
            record.Destination_Company_Name__c = destinationSite[0].Company_Name__c;
            record.Destination_Search_Choice__c = 'Site';
        } else {
            record.Destination_Search_Choice__c = null;
        }

        if(record.Export_Entity__c != null) {
            if(record.Export_Entity__c == FGXEnt_EntityService.getInternalEOREntity().Id) {
                record.EOR_Needed__c = true;
                relatedRecord.EOR_Needed__c = true;
            } else {
                record.EOR_Needed__c = false;
                relatedRecord.EOR_Needed__c = false;
            }
        }

        if(record.Import_Entity__c != null) {
            if(record.Import_Entity__c == FGXEnt_EntityService.getInternalIOREntity().Id) {
                record.IOR_Needed__c = true;
            } else {
                record.IOR_Needed__c = false;
            }
        }

        // if changing from non domestic to domestic then we need to clear out import/export and
        // commercialization
        if(
            record.Origin_Country__c == record.Destination_Country__c &&
            caseBefore?.Origin_Country__c != caseBefore?.Destination_Country__c
        ) {
            record.Export_Entity__c = null;
            record.Import_Entity__c = null;
        }

        if(
            record.Target_Date__c != null &&
            (caseBefore?.Target_Date__c != record.Target_Date__c ||
                caseBefore?.Origin_Country__c != record.Origin_Country__c ||
                caseBefore?.Destination_Country__c != record.Destination_Country__c)
        ) {
            Boolean isDomestic = record.Origin_Country__c == record.Destination_Country__c;

            if(isDomestic) {
                if(Date.today().daysBetween(record.Target_Date__c) <= 3) {
                    record.Service_Level__c = 'PriorityIT';
                } else {
                    record.Service_Level__c = 'EconomyIT';
                }

            } else {
                List<AggregateResult> ar = [SELECT SUM(Days_Max__c) maxDaysSum FROM Timeline__c WHERE Country__c =: record.Destination_Country__c];
                Decimal maxDaysSum = (Decimal) ar[0].get('maxDaysSum') != null ? (Decimal) ar[0].get('maxDaysSum') : 0;
                if(Date.today().daysBetween(record.Target_Date__c) < maxDaysSum) {
                    record.Service_Level__c = 'PriorityIT';
                } else {
                    record.Service_Level__c = 'EconomyIT';
                }
            }
        }

        record.RS_Output_Origin_Address__c = getOutputAddressString(record, 'Origin_');
        record.RS_Output_Destination_Address__c = getOutputAddressString(record, 'Destination_');

        if(record.Export_Entity__c != null) {
            Entity__c exportEntity = [SELECT Id, Entity_Consignment_Info__c FROM Entity__c WHERE Id =: record.Export_Entity__c];

            if(String.isNotBlank(exportEntity.Entity_Consignment_Info__c)){
                record.RS_Output_Exporter_Consignment__c = exportEntity.Entity_Consignment_Info__c;
            } else {
                record.RS_Output_Exporter_Consignment__c = '';
            }
        }

        if(record.Import_Entity__c != null) {
            Entity__c importEntity = [SELECT Id, Entity_Consignment_Info__c FROM Entity__c WHERE Id =: record.Import_Entity__c];

            if(String.isNotBlank(importEntity.Entity_Consignment_Info__c)){
                record.RS_Output_Importer_Consignment__c = importEntity.Entity_Consignment_Info__c;
            } else {
                record.RS_Output_Importer_Consignment__c = '';
            }
        }

        if (record.BOM_Currency_Code__c != null && record.BOM_Currency_Code__c != Constants.CURRENCY_USD &&
                record.BOM_Conversion_Rate__c != null && record.BOM_Conversion_Rate__c > 0) {

            if (record.Hardware_Value__c != null && record.Software_Value__c != null) {
                Decimal hardwareValueUSD = record.Hardware_Value__c / record.BOM_Conversion_Rate__c;
                Decimal softwareValueUSD = record.Software_Value__c / record.BOM_Conversion_Rate__c;
                record.Shipment_Value__c = hardwareValueUSD + softwareValueUSD;
            }
            else if (record.Shipment_Value__c != null) {
                record.Shipment_Value__c = record.Shipment_Value__c / record.BOM_Conversion_Rate__c;
            }
        }

        if(
            ((relatedRecord.Id != null && relatedRecord.Shipment__c == null) || relatedRecord.Id == null) &&
            FGXEnt_UserService.hasShipmentApprover(u.AccountId) &&
            String.isBlank(record.Client_Approval_Status__c)
        ) {
            record.Client_Approval_Status__c = FGXEnt_Constants.CLIENT_APPROVAL_STATUS_PENDING_REVIEW;
            record.Status = Constants.CASE_STATUS_ON_HOLD;
        }

        Logger logger = Logger.getInstance(true, true);
        Database.UpsertResult recordUpsertResult = Database.upsert(record, false);
        logger.log(
                FGXEnt_QuoteBookingContentService.class.toString(),
                'updateRecord',
                new List<Database.UpsertResult> {recordUpsertResult}
        );

        if(relatedRecord.Id != null && relatedRecord.Shipment__c == null) {
            relatedRecord.Shipment__c = recordUpsertResult.getId();
            relatedRecord.Status__c = Constants.QUOTE_STATUS_BOOKED;

            if(relatedVersionRecordId != null) {
                relatedRecord.Active_Version__c = relatedVersionRecordId;
            }

            Database.SaveResult saveResult = Database.update(relatedRecord, false);
            logger.log(
                FGXEnt_QuoteBookingContentService.class.toString(),
                'updateRecord',
                new List<Database.SaveResult> {saveResult}
            );
        }

        logger.finalize();
        return new FlowInterviewDMLResult(recordUpsertResult);
    }

    public static FlowInterviewDMLResult updateLines(List<SObject> records) {
        for(SObject record : records) {
            if((String)record.get('CurrencyIsoCode') == null) {
                record.put('CurrencyIsoCode', FGXEnt_CurrencyService.DEFAULT_CURRENCY_ISO);
            }

            for(String field : fieldLengthMap.keySet()) {
                try {
                    String value = (String) record.get(field);
                    if(String.isNotBlank(value)) {
                        if(value.length() > fieldLengthMap.get(field)) {
                            value = value.abbreviate(fieldLengthMap.get(field));
                            record.put(field, value);
                        }
                    }
                } catch(Exception e) {}
            }
        }
        Logger logger = Logger.getInstance(false, true);
        List<Database.UpsertResult> upsertResults = Database.upsert(records, false);
        logger.log(
                FGXEnt_QuoteBookingContentService.class.toString(),
                'updateLines',
                upsertResults
        );

        return new FlowInterviewDMLResult(records, upsertResults);
    }

    public static void deleteExistingUploads(Id linkedEntityId, Id contentDocumentLink) {
        if(linkedEntityId != null && contentDocumentLink != null) {
            List<ContentDocumentLink> cdls = [
                    SELECT Id
                    FROM ContentDocumentLink
                    WHERE LinkedEntityId =: linkedEntityId
                    AND Id !=: contentDocumentLink
                    AND ContentDocument.Title LIKE 'ClientUpload%'];

            delete cdls;
        }
    }

    private static final Map<String, Integer> fieldLengthMap = new Map<String, Integer> {
            'Manufacturer__c' => 255,
            'Part_Number__c' => 255,
            'Description__c' => 255,
            'HS_Code__c' => 50,
            'ECCN__c' => 20
    };

    public class QuoteBookingStepperData {
        @AuraEnabled
        public Case caseRecord { get; set; }

        @AuraEnabled
        public Quote__c quoteRecord { get; set; }

        @AuraEnabled
        public Quote_Version__c quoteVersionRecord { get; set; }

        @AuraEnabled
        public List<Commercial_Invoice_Line__c> bomLines { get; set; }

        @AuraEnabled
        public List<Shipment_Package_Line__c> pieceLines { get; set; }

        public QuoteBookingStepperData(
            Case caseRecord,
            Quote__c quoteRecord,
            Quote_Version__c quoteVersionRecord,
            List<Commercial_Invoice_Line__c> commercialInvoiceLines,
            List<Shipment_Package_Line__c> shipmentPackageLines
        ) {
            this.caseRecord = caseRecord;
            this.quoteRecord = quoteRecord;
            this.quoteVersionRecord = quoteVersionRecord;
            this.bomLines = commercialInvoiceLines.isEmpty() ? new List<Commercial_Invoice_Line__c> {
                    new Commercial_Invoice_Line__c()
            } : commercialInvoiceLines;
            this.pieceLines = shipmentPackageLines;
        }
    }

    public class FlowInterviewDMLResult {
        @AuraEnabled
        public Boolean success;
        @AuraEnabled
        public String errorString;
        @AuraEnabled
        public String recordId;
        @AuraEnabled
        public String relatedRecordId;
        @AuraEnabled
        public List<SObject> relatedRecordLineData;
        @AuraEnabled
        public String exceptionString {
            get {
                String value;
                if(e != null) {
                    value = e.getMessage();
                }
                return value;
            }
        }
        public Exception e;

        public FlowInterviewDMLResult(Exception e) {
            this.e = e;
            this.success = false;
        }

        public FlowInterviewDMLResult(Database.UpsertResult quoteUpsertResult) {
            List<String> errorList = FGXEnt_Utilities.getErrors(new List<Database.UpsertResult> { quoteUpsertResult });
            this.success = errorList.isEmpty() ? true : false ;
            this.recordId = quoteUpsertResult.getId();

            this.errorString = String.join(errorList, ', ');
        }

        public FlowInterviewDMLResult(List<Commercial_Invoice_Line__c> relatedRecordLineData, List<Database.UpsertResult> lineUpsertResults) {
            List<String> errorList = FGXEnt_Utilities.getErrors(lineUpsertResults);
            this.relatedRecordLineData = relatedRecordLineData;
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }

        public FlowInterviewDMLResult(List<Commercial_Invoice_Line__c> relatedRecordLineData, List<Database.DeleteResult> lineDeleteResults) {
            List<String> errorList = FGXEnt_Utilities.getErrors(lineDeleteResults);
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }
    }

    private static String getOutputAddressString(Case record, String fieldPrefix) {
        List<String> addrOutputList = new List<String>();

        String companyName = (String) record.get(fieldPrefix + 'Company_Name__c');
        String addrLine1 = (String) record.get(fieldPrefix + 'Address_Line_1__c');
        String addrLine2 = (String) record.get(fieldPrefix + 'Address_Line_2__c');
        String addrLine3 = (String) record.get(fieldPrefix + 'Address_Line_3__c');
        String city = (String) record.get(fieldPrefix + 'City__c');
        String country = (String) record.get(fieldPrefix + 'Country__c');
        String countryName = (String) record.getSObject(fieldPrefix + 'Country__r')?.get('Name');
        String postalCode = (String) record.get(fieldPrefix + 'Post_Code__c');
        String countryProvOthr = (String) record.get(fieldPrefix + 'County_Province_Other__c');
        String taxId = (String) record.get(fieldPrefix + 'Tax_Id__c');
        String firstName = (String) record.get(fieldPrefix + 'First_Name__c');
        String lastName = (String) record.get(fieldPrefix + 'Last_Name__c');
        String email = (String) record.get(fieldPrefix + 'Email__c');
        String mobilePhone = (String) record.get(fieldPrefix + 'Mobile_Phone__c');
        String officePhone = (String) record.get(fieldPrefix + 'Office_Phone__c');
        String officePhoneExt = (String) record.get(fieldPrefix + 'Office_Phone_Extension__c');

        if (String.isNotBlank(companyName)) {
            addrOutputList.add(companyName);
        }

        if (String.isNotBlank(addrLine1)) {
            addrOutputList.add(addrLine1);
        }

        if (String.isNotBlank(addrLine2) ||
                String.isNotBlank(addrLine3)) {
            List<String> arr = new List<String>();

            if (String.isNotBlank(addrLine2)) {
                arr.add(addrLine2);
            }

            if (String.isNotBlank(addrLine3)) {
                arr.add(addrLine3);

            }
            addrOutputList.add(String.join(arr, ', '));
        }

        if (String.isNotBlank(city) ||
                String.isNotBlank(country) ||
                String.isNotBlank(postalCode)) {
            List<String> arr = new List<String>();

            if (String.isNotBlank(city)) {
                arr.add(city);
            }

            String state = 'United States'.equalsIgnoreCase(countryName) ?
                    (String) record.get(fieldPrefix + 'US_State__c') :
                    (String) record.get(fieldPrefix + 'State__c');

            if (String.isNotBlank(state)) {
                arr.add(', ' + state);
            }

            if (String.isNotBlank(postalCode)) {
                arr.add(' ' + postalCode);
            }

            addrOutputList.add(String.join(arr, ''));
        }

        if (String.isNotBlank(countryProvOthr) ||
                String.isNotBlank(country)) {
            List<String> arr = new List<String>();

            if (String.isNotBlank(countryProvOthr)) {
                arr.add(countryProvOthr);
            }

            if (String.isNotBlank(country)) {
                arr.add(countryName);
            }
            addrOutputList.add(String.join(arr, ', '));
        }

        if (String.isNotBlank(taxId)) {
            addrOutputList.add(taxId);
        } else {
            addrOutputList.add('');
        }

        if (String.isNotBlank(firstName) ||
                String.isNotBlank(lastName) ||
                String.isNotBlank(email)) {
            List<String> arr = new List<String>();

            if (String.isNotBlank(firstName)) {
                arr.add(firstName + ' ');
            }

            if (String.isNotBlank(lastName)) {
                arr.add(lastName);
            }

            if (!arr.isEmpty()) {
                arr.add(', ');
            }

            if (String.isNotBlank(email)) {
                arr.add(email);
            }

            addrOutputList.add(String.join(arr, ''));
        }

        if (String.isNotBlank(mobilePhone)) {
            addrOutputList.add(mobilePhone);

        } else if (String.isNotBlank(officePhone)) {
            String ext = String.isNotBlank(officePhoneExt) ? officePhoneExt : null;
            addrOutputList.add(officePhone + (String.isNotBlank(ext) ? ' x' + ext : ''));
        }

        return String.join(addrOutputList, '\n').toUpperCase();
    }

}