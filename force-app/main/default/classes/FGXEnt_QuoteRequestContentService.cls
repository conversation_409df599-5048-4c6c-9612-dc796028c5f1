/**
 * FGXEnt_QuoteRequestContentService
 * @description: Service for the Quote Request stepper
 * to handle all the different actions the Controller needs
 * @author: <PERSON><PERSON>
 * @date: 1/2/24
 */

public without sharing class FGXEnt_QuoteRequestContentService {

    /**
     * @description Returns the Quote, Quote Version and all related
     * BOM and Package Lines for the Quote Request Flow
     * @param recordId quoteId
     * @param relatedRecordId quoteVersionId
     * @return
     */
    public static QuoteRequestStepperData getData(String recordId, String relatedRecordId) {
        User u = FGXEnt_UserService.getCurrentUser();
        Date visibilityDate = FGXEnt_UserService.getPlatformVisibilityDate();
        String accountId = u.AccountId;
        String contactId = u.ContactId;

        String query = 'SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Quote__c') +
                ' FROM Quote__c ';

        List<String> quoteFilters = new List<String>{
            'Id =: recordId'
        };

        if (String.isNotBlank(u.AccountId)) {
            quoteFilters.add('Client_Account__c  =: accountId');
        }

        if(FGXEnt_Utilities.isUserRecordOnlyRestricted(u)) {
            quoteFilters.add('Client_Contact__c =: contactId');
        }

        if(visibilityDate != null) {
            quoteFilters.add('CreatedDate >=: visibilityDate ');
        }

        if (!quoteFilters.isEmpty()) {
            query += 'WHERE ' + String.join(quoteFilters, ' AND ');
        }

        String relatedQuery = 'SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Quote_Version__c') +
                ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'BOM_Line__c') +
                ' FROM BOM_Lines__r ORDER BY CreatedDate ASC) ' +
                ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'PackageLine__c') +
                ' FROM Packages__r ORDER BY CreatedDate ASC) ' +
                ' ,(SELECT ' + FGXEnt_FieldSetService.getQueryFields('FGXEnt_StepperFields', 'Quote_Line__c') +
                ' FROM Quote_Lines__r ORDER BY SortOrder__c ASC) ' +
                ' FROM Quote_Version__c ';

        List<String> qvFilters = new List<String>{
            'Quote__c =: recordId'
        };

        if (String.isNotBlank(accountId)) {
            qvFilters.add('Quote__r.Client_Account__c =: accountId');
        }

        if(FGXEnt_Utilities.isUserRecordOnlyRestricted(u)) {
            qvFilters.add('Quote__r.Client_Contact__c =: contactId');
        }

        if (!qvFilters.isEmpty()) {
            relatedQuery += 'WHERE ' + String.join(qvFilters, ' AND ');
        }
        relatedQuery += ' ORDER BY CreatedDate DESC';

        List<Quote__c> quotes = (List<Quote__c>) Database.query(query);
        List<Quote_Version__c> quoteVersions = new List<Quote_Version__c>();

        if(!quotes.isEmpty()) {
            quoteVersions = (List<Quote_Version__c>) Database.query(relatedQuery);
        } else {
            return null;
        }
        return new QuoteRequestStepperData(quotes.get(0), quoteVersions, relatedRecordId);
    }

    /**
     * @description Handles taking in the raw
     * BOM data that is parsed from a PDF and
     * passing it to OpenAI to get the BOMGPTResponse
     * @param data
     * @param recordId
     * @return
     */
    public static FGXEnt_OpenAIService.BOMGPTResponse getBOMLinesGPT(String data, Id recordId, Id contentDocumentLink) {
        String prompt = FGXEnt_UserService.isAdvancedLogistics() ? 'QuoteRequestBOMInputAL' : 'QuoteRequestBOMInput';
        OpenAIConfig config = OpenAIConfig.getConfig(prompt);
        FGXEnt_OpenAIService.BOMGPTResponse response = FGXEnt_OpenAIService.invokeRequest(
            new OpenAIClient.OpenAIRequest(
                String.join(new List<String>{config.prompt, data}, ' : '),
                config
            )
        );

        if(response.parsingSuccess) {
            List<BOM_Line__c> bomLinesToDelete = [SELECT Id FROM BOM_Line__c WHERE Quote_Version__c =: recordId];
            if(!bomLinesToDelete.isEmpty()) {
                List<Database.DeleteResult> sr = Database.delete(bomLinesToDelete);
                response.bomLineDeleteErrors = FGXEnt_Utilities.getErrors(sr);
            }
            if(response.bomLineDeleteErrors.isEmpty()) {
                update new Quote_Version__c(Id = recordId, BOM_CSV_Ingested__c = false, BOM_AI_Ingested__c = true);
            }

            FGXEnt_QuoteRequestContentService.deleteExistingUploads(recordId, contentDocumentLink);
        }

        return response;
    }

    /**
     * @description Handles taking in the raw
     * CSV data that is parsed in JS.
     * @param data
     * @param recordId
     * @return
     */
    public static FGXEnt_CSVParserService.BOMCSVResponse getBOMLinesCSV(String data, Id recordId) {
        FGXEnt_CSVParserService.BOMCSVResponse response = FGXEnt_CSVParserService.getBOMLines(
            data,
            recordId,
            FGXEnt_UserService.isAdvancedLogistics()
        );

        if(response.parsingSuccess) {
            List<BOM_Line__c> bomLinesToDelete = [SELECT Id FROM BOM_Line__c WHERE Quote_Version__c =: recordId];
            if(!bomLinesToDelete.isEmpty()) {
                List<Database.DeleteResult> sr = Database.delete(bomLinesToDelete);
                response.bomLineDeleteErrors = FGXEnt_Utilities.getErrors(sr);
            }
            if(response.bomLineDeleteErrors.isEmpty()) {
                update new Quote_Version__c(Id = recordId, BOM_CSV_Ingested__c = true, BOM_AI_Ingested__c = false);
            }
        }
        return response;
    }

    /**
     * @description Determines a Origin address cache
     * depending on the current state of a Quote__c before
     * being saved
     * @param record
     * @return
     * Origin_Address_Cache__c is a string field that should be formatted like this:
     * 'searchChoice:' + {Quote__c.Origin_Search_Choice__c} + ';location:' +
     *  {IF (Quote__c.Origin_Search_Choice__c == 'SFDC') { Quote__c.Origin_SFDC_Address__c }
     *  ELSE IF (Quote__c.Origin_Search_Choice__c == 'Site') { Quote__c.Origin_Site__c }
     *  ELSE IF (Quote__c.Origin_Search_Choice__c) { Quote__c.Origin_City__c + ',' + Quote__c.Origin_Country__c}
     *  searchChoice:Google;location:NY,a0V8L000000hltVUAQ
     *  searchChoice:Site;location:a088K000000yPddQAE
     *  searchChoice:SFDC;location:a088K000000yPddQAE (this shouldn't be relevant for the platform, just something we are doing to support older quotes)
     */
    public static String getOriginAddressCache(Quote__c record) {
        String cache = 'searchChoice:' + record.Origin_Search_Choice__c + ';location:';

        if(String.isNotBlank(record.Origin_Site__c)) {
            cache += record.Origin_Site__c;
        }
        else if(String.isNotBlank(record.Origin_City__c) || String.isNotBlank(record.Origin_Country__c)) {
            cache += record.Origin_City__c + ',' + record.Origin_Country__c + ',' + record.Origin_US_State__c;
        }
        return cache;
    }

    /**
     * @description Handles updating the quote and quote version
     * record when changing steps in the quote request flow. It also
     * handles twinning fields and conditionally setting fields based
     * on others.
     * @param record
     * @param relatedRecord
     * @return
     */
    public static FlowInterviewDMLResult updateRecord(Quote__c record, Quote_Version__c relatedRecord, String mode) {
        User u = FGXEnt_UserService.getCurrentUser();

        Quote__c quoteBefore;

        // Quote
        if(record.Id == null) {
            record.Version_Count__c = 0;
            record.Status__c = 'New';
            record.Submitted_By__c = 'Client';
            record.Shipment__c = null;
        } else {
            quoteBefore = [
                SELECT Id,
                        Flow_Step__c,
                        Target_Delivery_Date__c,
                        Origin_Site__c,
                        Origin_City__c,
                        Origin_Country__c,
                        Origin_US_State__c,
                        Origin_Search_Choice__c,
                        Destination_Country__c,
                        Submitted_By__c,
                        Drop_Ship_to_FGX__c
                FROM Quote__c
                WHERE Id =: record.Id
            ][0];
            if(quoteBefore != null) {
                if(record.Flow_Step__c < quoteBefore.Flow_Step__c || record.Flow_Step__c >= 10) {
                    record.Flow_Step__c = quoteBefore.Flow_Step__c;
                }

                if(!'Client'.equalsIgnoreCase(quoteBefore.Submitted_By__c)) {
                    record.Submitted_By__c = 'Client';
                }

                if(
                    (
                        record.Origin_Site__c != quoteBefore.Origin_Site__c && String.isNotBlank(record.Origin_Site__c) ||
                        record.Origin_City__c != quoteBefore.Origin_City__c && String.isNotBlank(record.Origin_City__c) ||
                        record.Origin_Country__c != quoteBefore.Origin_Country__c && String.isNotBlank(record.Origin_Country__c)
                    ) && String.isBlank(record.Origin_Address_Cache__c)
                ){
                    record.Origin_Address_Cache__c = getOriginAddressCache(record);
                }
            }
        }

        if(record.Client_Account__c == null) {
            record.Client_Account__c = u.AccountId;
            record.Client_Contact__c = u.ContactId;
        }

        // Quote with a cached site or city/country is reverting from drop-ship back to the site
        if(record.Drop_Ship_to_FGX__c && !relatedRecord.Drop_Ship_to_FGX__c) {
            if(String.isNotBlank(record.Origin_Address_Cache__c)) {
                FGXEnt_OriginAddressCache cache = new FGXEnt_OriginAddressCache(record.Origin_Address_Cache__c);
                if(String.isNotBlank(cache.site)) {
                    record.Origin_Site__c = cache.site;
                }
                else if(String.isNotBlank(cache.city) || String.isNotBlank(cache.country)) {
                    record.Origin_City__c = cache.city;
                    record.Origin_Country__c = cache.country;
                    if(String.isNotBlank(cache.state)) {
                        record.Origin_US_State__c = cache.state;
                    }
                    record.Origin_Search_Choice__c = 'Google';
                    record.Origin_Company_Name__c = null;
                    record.Origin_Address_Line_1__c = null;
                    record.Origin_Address_Line_2__c = null;
                    record.Origin_Address_Line_3__c = null;
                    record.Origin_County_Province_Other__c = null;
                    record.Origin_Post_Code__c = null;
                }
            }
        }

        // Quote Version drop ship selected
        if(relatedRecord.Drop_Ship_to_FGX__c || (String.isBlank(record.Origin_City__c) && String.isBlank(record.Origin_Country__c) && String.isBlank(record.Origin_Site__c))) {
            relatedRecord.Drop_Ship_to_FGX__c = true;
            record.Drop_Ship_to_FGX__c = true;
            record.Origin_Site__c = null;
            record.Origin_Country__c = Constants.COUNTRIES_BY_CODE_MAP.get('US').Id;
            record.Origin_City__c = 'Livingston';
            record.Origin_US_State__c = 'New Jersey';
            record.Origin_Google_Address__c = u.Account.Name + ' c/o First Global Xpress, 7 Industrial Parkway, Unit #10, Livingston, NJ 07039 USA';
            record.Origin_Company_Name__c = u.Account.Name + ' c/o First Global Xpress';
            record.Origin_Address_Line_1__c = '7 Industrial Parkway';
            record.Origin_Address_Line_2__c = 'Unit #10';
            record.Origin_Post_Code__c = '07039';
            record.Origin_Location_Type__c = 'Warehouse';
            record.EOR_Needed__c = false;
        } else {
            relatedRecord.Drop_Ship_to_FGX__c = false;
            record.Drop_Ship_to_FGX__c = false;
        }

        if(String.isNotBlank(record.Origin_Site__c)) {
            List<Site__c> originSite = [
                SELECT Id, Address_Line_1__c, Address_Line_2__c,
                        Address_Line_3__c, City__c, Post_Code__c,
                        State__c, State_US__c, Country__c,
                        County_Province_Other__c, Company_Name__c
                FROM Site__c
                WHERE Id =: record.Origin_Site__c
            ];

            record.Origin_Address_Line_1__c = originSite[0].Address_Line_1__c;
            record.Origin_Address_Line_2__c = originSite[0].Address_Line_2__c;
            record.Origin_Address_Line_3__c = originSite[0].Address_Line_3__c;
            record.Origin_City__c = originSite[0].City__c;
            record.Origin_Post_Code__c = originSite[0].Post_Code__c;
            record.Origin_State__c = originSite[0].State__c;
            record.Origin_US_State__c = originSite[0].State_US__c;
            record.Origin_Country__c = originSite[0].Country__c;
            record.Origin_County_Province_Other__c = originSite[0].County_Province_Other__c;
            record.Origin_Company_Name__c = originSite[0].Company_Name__c;
            record.Origin_Search_Choice__c = 'Site';
            record.Origin_Google_Address__c = null;
            record.Drop_Ship_to_FGX__c = false;
            relatedRecord.Drop_Ship_to_FGX__c = false;
        } else {
            record.Origin_Search_Choice__c = 'Google';
        }

        if(String.isNotBlank(record.Destination_Site__c)) {

            List<Site__c> destinationSite = [
                SELECT Id, Address_Line_1__c, Address_Line_2__c,
                        Address_Line_3__c, City__c, Post_Code__c,
                        State__c, State_US__c, Country__c,
                        County_Province_Other__c, Company_Name__c
                FROM Site__c
                WHERE Id =: record.Destination_Site__c
            ];

            record.Destination_Address_Line_1__c = destinationSite[0].Address_Line_1__c;
            record.Destination_Address_Line_2__c = destinationSite[0].Address_Line_2__c;
            record.Destination_Address_Line_3__c = destinationSite[0].Address_Line_3__c;
            record.Destination_City__c = destinationSite[0].City__c;
            record.Destination_Post_Code__c = destinationSite[0].Post_Code__c;
            record.Destination_State__c = destinationSite[0].State__c;
            record.Destination_US_State__c = destinationSite[0].State_US__c;
            record.Destination_Country__c = destinationSite[0].Country__c;
            record.Destination_County_Province_Other__c = destinationSite[0].County_Province_Other__c;
            record.Destination_Company_Name__c = destinationSite[0].Company_Name__c;
            record.Destination_Search_Choice__c = 'Site';
        } else {
            record.Destination_Search_Choice__c = 'Google';
        }

        if(record.Export_Entity__c != null) {
            relatedRecord.Export_Entity__c = record.Export_Entity__c;
            if(record.Export_Entity__c == FGXEnt_EntityService.getInternalEOREntity().Id) {
                record.EOR_Needed__c = true;
                relatedRecord.EOR_Needed__c = true;
            } else {
                record.EOR_Needed__c = false;
                relatedRecord.EOR_Needed__c = false;
            }
        }

        if(record.Import_Entity__c != null) {
            relatedRecord.Import_Entity__c = record.Import_Entity__c;
            if(record.Import_Entity__c == FGXEnt_EntityService.getInternalIOREntity().Id) {
                record.IOR_Needed__c = true;
                relatedRecord.IOR_Needed__c = true;
            } else {
                record.IOR_Needed__c = false;
                relatedRecord.IOR_Needed__c = false;
            }
        }

        // if changing from non domestic to domestic then we need to clear out import/export and
        // commercialization
        if(
            record.Origin_Country__c == record.Destination_Country__c &&
            quoteBefore?.Origin_Country__c != quoteBefore?.Destination_Country__c
        ) {
            record.Export_Entity__c = null;
            record.Import_Entity__c = null;
            relatedRecord.Transaction_Type__c = null;
            relatedRecord.Purchasing_Entity__c = null;
            relatedRecord.TxnSurvey_Related_Entity__c = null;
            relatedRecord.Ultimate_Owner_Entity__c = null;
            relatedRecord.TxnSurvey_Asset_Transfer__c = null;
            relatedRecord.TxnSurvey_Funds_Transfer__c = null;
        }

        if(
            record.Target_Delivery_Date__c != null &&
            (quoteBefore?.Target_Delivery_Date__c != record.Target_Delivery_Date__c ||
            quoteBefore?.Origin_Country__c != record.Origin_Country__c ||
            quoteBefore?.Destination_Country__c != record.Destination_Country__c)
        ) {
            Boolean isDomestic = record.Origin_Country__c == record.Destination_Country__c;

            if(isDomestic) {
                if(Date.today().daysBetween(record.Target_Delivery_Date__c) <= 3) {
                    record.Requested_Service_Level__c = 'PriorityIT';
                    relatedRecord.Service_Level__c = 'PriorityIT';
                } else {
                    record.Requested_Service_Level__c = 'EconomyIT';
                    relatedRecord.Service_Level__c = 'EconomyIT';
                }

            } else {
                List<AggregateResult> ar = [SELECT SUM(Days_Max__c) maxDaysSum FROM Timeline__c WHERE Country__c =: record.Destination_Country__c];
                Decimal maxDaysSum = (Decimal) ar[0].get('maxDaysSum') != null ? (Decimal) ar[0].get('maxDaysSum') : 0;
                if(Date.today().daysBetween(record.Target_Delivery_Date__c) < maxDaysSum) {
                    record.Requested_Service_Level__c = 'PriorityIT';
                    relatedRecord.Service_Level__c = 'PriorityIT';
                } else {
                    record.Requested_Service_Level__c = 'EconomyIT';
                    relatedRecord.Service_Level__c = 'EconomyIT';
                }
            }
        }

        if(String.isBlank(record.Packing_Configuration__c)) {
            record.Packing_Configuration__c = relatedRecord.Packing_Configuration__c;
        }

        if(String.isBlank(record.DIMWeightEntryType__c)) {
            record.DIMWeightEntryType__c = relatedRecord.DIMWeightEntryType__c;
        }

        // On clones clear the origin address cache after we use it above
        if(record.Id == null) {
            record.Origin_Address_Cache__c = null;
        }

        if (relatedRecord.BOM_Currency_Code__c != null && relatedRecord.BOM_Currency_Code__c != Constants.CURRENCY_USD &&
                relatedRecord.BOM_Conversion_Rate__c != null && relatedRecord.BOM_Conversion_Rate__c > 0) {

            if (relatedRecord.Hardware_Value__c != null && relatedRecord.Software_Value__c != null) {
                Decimal hardwareValueUSD = relatedRecord.Hardware_Value__c / relatedRecord.BOM_Conversion_Rate__c;
                Decimal softwareValueUSD = relatedRecord.Software_Value__c / relatedRecord.BOM_Conversion_Rate__c;
                relatedRecord.Shipment_Value__c = hardwareValueUSD + softwareValueUSD;
            }
            else if (relatedRecord.Shipment_Value__c != null) {
                relatedRecord.Shipment_Value__c = relatedRecord.Shipment_Value__c / relatedRecord.BOM_Conversion_Rate__c;
            }
        }

        Logger logger = Logger.getInstance(true, true);

        Database.UpsertResult recordUpsertResult = Database.upsert(record, false);
        logger.log(
            FGXEnt_QuoteRequestContentService.class.toString(),
            'updateRecord',
            new List<Database.UpsertResult> {recordUpsertResult}
        );

        Database.UpsertResult relatedRecordUpsertResult;

        // Quote Version
        if(recordUpsertResult.isSuccess()) {

            if(relatedRecord.Quote__c == null) {
                relatedRecord.Quote__c = recordUpsertResult.getId();
            }

            if(relatedRecord.Id == null) {
                Quote__c quote = [SELECT Name, Version_Count__c FROM Quote__c WHERE Id =: relatedRecord.Quote__c];
                Decimal versionCount = (quote.Version_Count__c == null ? 0 : quote.Version_Count__c ) + 1;

                relatedRecord.Name = String.join(new List<String>{quote.Name, String.valueOf(versionCount)}, '.');
                relatedRecord.Version_Number__c = versionCount;

                if(relatedRecord.Status__c != Constants.QUOTE_VERSION_STATUS_IN_DRAFT) {
                    relatedRecord.Status__c = Constants.QUOTE_VERSION_STATUS_NOT_STARTED;
                }

                // In a clone situation we want to make sure these are cleared out
                relatedRecord.Approval_Status__c = 'Not Submitted';
                relatedRecord.Approval_Submission_Date__c = null;
                relatedRecord.Approved_By__c = null;

                quote.Version_Count__c = versionCount;
                record.Version_Count__c = versionCount;
                Database.SaveResult quoteUpdateResult = Database.update(quote, false);
                logger.log(
                    FGXEnt_QuoteRequestContentService.class.toString(),
                    'updateRecord',
                    new List<Database.SaveResult> {quoteUpdateResult}
                );


            } else {
                Quote_Version__c quoteVersionBefore = [
                        SELECT Id, Flow_Step__c
                        FROM Quote_Version__c
                        WHERE Id =: relatedRecord.Id
                ][0];
                if(quoteVersionBefore != null) {
                    if(relatedRecord.Flow_Step__c < quoteVersionBefore.Flow_Step__c || relatedRecord.Flow_Step__c >= 10) {
                        relatedRecord.Flow_Step__c = quoteVersionBefore.Flow_Step__c;
                    }
                }
            }

            if(relatedRecord.CurrencyIsoCode == null) {
                relatedRecord.CurrencyIsoCode = FGXEnt_CurrencyService.DEFAULT_CURRENCY_ISO;
            }

            if(relatedRecord.BOM_Currency_Code__c == null) {
                relatedRecord.BOM_Currency_Code__c = FGXEnt_CurrencyService.DEFAULT_CURRENCY_ISO;
            }

            relatedRecordUpsertResult = Database.upsert(relatedRecord, false);
            logger.log(
                FGXEnt_QuoteRequestContentService.class.toString(),
                'updateRecord',
                new List<Database.UpsertResult> {relatedRecordUpsertResult}
            );

        }

        if('Estimate For Client'.equalsIgnoreCase(record.DIMWeightEntryType__c)) {
            delete [SELECT Id FROM PackageLine__c WHERE Quote_Version__c =: relatedRecord.Id];
        }

        logger.finalize();
        return new FlowInterviewDMLResult(recordUpsertResult, relatedRecordUpsertResult);
    }

    public static FlowInterviewDMLResult updateLines(List<SObject> records) {
        for(SObject record : records) {
            if((String)record.get('CurrencyIsoCode') == null) {
                record.put('CurrencyIsoCode', FGXEnt_CurrencyService.DEFAULT_CURRENCY_ISO);
            }

            for(String field : fieldLengthMap.keySet()) {
                try {
                    String value = (String) record.get(field);
                    if(String.isNotBlank(value)) {
                        if(value.length() > fieldLengthMap.get(field)) {
                            value = value.abbreviate(fieldLengthMap.get(field));
                            record.put(field, value);
                        }
                    }
                } catch(Exception e) {}
            }
        }

        Logger logger = Logger.getInstance(false, true);
        List<Database.UpsertResult> upsertResults = Database.upsert(records);
        logger.log(
            FGXEnt_QuoteRequestContentService.class.toString(),
            'updateLines',
            upsertResults
        );
        return new FlowInterviewDMLResult(records, upsertResults);
    }

    public static void deleteExistingUploads(Id linkedEntityId, Id contentDocumentLink) {
        List<ContentDocumentLink> cdls = [
            SELECT Id
            FROM ContentDocumentLink
            WHERE LinkedEntityId =: linkedEntityId
            AND Id !=: contentDocumentLink
            AND (ContentDocument.Title LIKE 'AIUpload_%' OR ContentDocument.Title LIKE 'ClientUploadCSV_%')];

        delete cdls;
    }

    private static final Map<String, Integer> fieldLengthMap = new Map<String, Integer> {
        'Manufacturer__c' => 255,
        'Part_Number__c' => 255,
        'Description__c' => 255,
        'HS_Code__c' => 50,
        'ECCN__c' => 20
    };

    public class QuoteRequestStepperData {
        @AuraEnabled
        public Quote__c quote { get; set; }
        @AuraEnabled
        public List<Quote_Version__c> quoteVersions { get; set; }
        @AuraEnabled
        public String requestedQuoteVersionId { get; set; }
        @AuraEnabled
        public Quote_Version__c activeQuoteVersion {
            get {
                if(quote != null) {
                    if(!quoteVersions.isEmpty()) {
                        Map<Id, Quote_Version__c> quoteVersionMap = new Map<Id, Quote_Version__c>(quoteVersions);

                        if(String.isNotBlank(requestedQuoteVersionId)){
                            if(quoteVersionMap.containsKey(requestedQuoteVersionId)) {
                                return quoteVersionMap.get(requestedQuoteVersionId);
                            }
                        }

                        if(quote.Active_Version__c != null) {
                            return quoteVersionMap.get(quote.Active_Version__c);
                        } else {
                            return quoteVersions[0];
                        }
                    }
                }
                return new Quote_Version__c();
            }
            set;
        }
        @AuraEnabled
        public List<BOM_Line__c> bomLines {
            get {
                if(!activeQuoteVersion.BOM_Lines__r.isEmpty()) {
                    return activeQuoteVersion.BOM_Lines__r;
                } else {
                    return new List<BOM_Line__c>{
                            new BOM_Line__c(
                                    Quote_Version__c = activeQuoteVersion != null ? activeQuoteVersion.Id : quoteVersions[0].Id
                            )
                    };
                }
            } set;
        }
        @AuraEnabled
        public List<Quote_Line__c> quoteLines {
            get {
                if(!activeQuoteVersion.Quote_Lines__r.isEmpty()) {
                    return activeQuoteVersion.Quote_Lines__r;
                } else {
                    return new List<Quote_Line__c>();
                }
            } set;
        }
        @AuraEnabled
        public List<PackageLine__c> pieceLines {
            get {
                if(!activeQuoteVersion.Packages__r.isEmpty()) {
                    return activeQuoteVersion.Packages__r;
                } else {
                    return new List<PackageLine__c>();
                }
            } set;
        }

        public QuoteRequestStepperData(Quote__c quote, List<Quote_Version__c> quoteVersions, String relatedRecordId) {
            this.quote = quote;
            this.quoteVersions = quoteVersions;
            this.requestedQuoteVersionId = relatedRecordId;

            if(this.quote != null) {
                if(String.isNotBlank(this.quote.Summary__c)) {
                    this.quote.Summary__c = this.quote.Summary__c.unescapeHtml4();
                }
            }

            if(this.activeQuoteVersion != null) {
                if(String.isNotBlank(this.activeQuoteVersion.Client_Comments__c)) {
                    this.activeQuoteVersion.Client_Comments__c = this.activeQuoteVersion.Client_Comments__c.unescapeHtml4();
                }
            }
        }
    }

    public class FlowInterviewDMLResult {
        @AuraEnabled
        public Boolean success;
        @AuraEnabled
        public String errorString;
        @AuraEnabled
        public String recordId;
        @AuraEnabled
        public String relatedRecordId;
        @AuraEnabled
        public List<SObject> relatedRecordLineData;

        @AuraEnabled
        public List<BOM_Line__c> relatedBomLines {
            get {
                List<BOM_Line__c> lines = new List<BOM_Line__c>();
                if(this.relatedRecordLineData != null) {
                    for(SObject line : relatedRecordLineData) {
                        if(line.getSObjectType() === BOM_Line__c.SObjectType ) {
                            lines.add((BOM_Line__c) line);
                        }
                    }
                }
                return lines;
            }
        }

        @AuraEnabled
        public List<PackageLine__c> relatedPackageLines {
            get {
                List<PackageLine__c> lines = new List<PackageLine__c>();
                if(this.relatedRecordLineData != null) {
                    for(SObject line : relatedRecordLineData) {
                        if(line.getSObjectType() === PackageLine__c.SObjectType ) {
                            lines.add((PackageLine__c) line);
                        }
                    }
                }
                return lines;
            }
        }

        @AuraEnabled
        public List<Quote_Line__c> relatedQuoteLines {
            get {
                List<Quote_Line__c> lines = new List<Quote_Line__c>();
                if(this.relatedRecordLineData != null) {
                    for(SObject line : relatedRecordLineData) {
                        if(line.getSObjectType() === Quote_Line__c.SObjectType ) {
                            lines.add((Quote_Line__c) line);
                        }
                    }
                }
                return lines;
            }
        }
        @AuraEnabled
        public String exceptionString {
            get {
                String value;
                if(e != null) {
                    value = e.getMessage();
                }
                return value;
            }
        }
        public Exception e;

        public FlowInterviewDMLResult(Exception e) {
            this.e = e;
            this.success = false;
        }

        public FlowInterviewDMLResult(Database.UpsertResult quoteUpsertResult, Database.UpsertResult quoteVersionUpsertResult) {
            List<String> errorList = FGXEnt_Utilities.getErrors(new List<Database.UpsertResult> { quoteUpsertResult, quoteVersionUpsertResult });
            this.success = errorList.isEmpty() ? true : false ;
            this.recordId = quoteUpsertResult.getId();
            this.relatedRecordId = quoteVersionUpsertResult.getId();
            this.errorString = String.join(errorList, ', ');
        }

        public FlowInterviewDMLResult(List<SObject> relatedRecordLineData, List<Database.UpsertResult> lineUpsertResults) {
            List<String> errorList = FGXEnt_Utilities.getErrors(lineUpsertResults);
            this.relatedRecordLineData = relatedRecordLineData;
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }

        public FlowInterviewDMLResult(List<SObject> relatedRecordLineData, List<Database.DeleteResult> lineDeleteResults) {
            List<String> errorList = FGXEnt_Utilities.getErrors(lineDeleteResults);
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }
    }
}