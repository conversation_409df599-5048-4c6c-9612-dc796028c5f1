/**
 * FGXEnt_CSVParserService
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 2/28/24
 */

public with sharing class FGXEnt_CSVParserService {

    private static final List<String> headerListAdvancedLogistics = new List<String>{'Manufacturer', 'Part Number', 'Description', 'Product Type', 'HS Code', 'ECCN', 'Country of Origin', 'Condition', 'Quantity', 'Unit Value', 'Total Value'};
    private static final List<String> headerListBasic = new List<String>{'Manufacturer', 'Part Number', 'Description', 'Product Type', 'Condition', 'Quantity', 'Unit Value', 'Total Value'};
    private static final Map<String, String> productTypeToCategoryMap = new Map<String, String>{
            'Other Hardware' => 'Hardware',
            'Software/License' => 'Software/License',
            'Warranty/Support' => 'Warranty/Support',
            'Other Intangible' => 'Other'
    };

    public static BOMCSVResponse getBOMLines(String data, Id relatedRecordId, Boolean advancedLogisticsEnabled) {
        List<String> headerList = advancedLogisticsEnabled ? headerListAdvancedLogistics : headerListBasic;

        BOMCSVResponse response = new BOMCSVResponse();
        List<String> csvRecordData = new List<String>();

        String csvAsString = data;
        CSVParser parser = new CSVParser(csvAsString);

        if (parser.buffer.size() <= 1) {
            response.addFatalError('Error: File does not have any data. Double check the template.');
            return response;
        } else {
            csvRecordData = parser.readLine();

            if (!headersValid(csvRecordData, headerList)) {
                response.addFatalError('Error: Invalid header found. Double check the template.');
                return response;

            } else {
                Integer rowCount = parser.buffer.size();
                for (Integer rowNum = 0; rowNum < rowCount; rowNum++) {
                    BOM_Line__c bomOBJ = new BOM_Line__c();
                    csvRecordData?.clear();
                    csvRecordData = parser.readLine();

                    if (!parser.isLineEmpty()) {
                        bomOBJ.Quote_Version__c = relatedRecordId;
                        bomOBJ.CurrencyIsoCode = 'USD';

                        String manufacturer = csvRecordData[headerList.indexOf('Manufacturer')].replaceAll('(\\r|\\n)+', '').toUpperCase();
                        if(String.isBlank(manufacturer)) {
                            response.addRowError(rowNum, 'Manufacturer is blank.');
                        } else {
                            bomOBJ.Manufacturer__c = manufacturer;
                        }

                        String partNumber = csvRecordData[headerList.indexOf('Part Number')].replaceAll('(\\r|\\n)+', '');
                        if(String.isBlank(partNumber)) {
                            response.addRowError(rowNum, 'Part Number is blank.');
                        } else {
                            bomOBJ.Part_Number__c = partNumber;
                        }

                        String description = csvRecordData[headerList.indexOf('Description')];
                        if(String.isBlank(description)) {
                            response.addRowError(rowNum, 'Description is blank.');
                        } else {
                            bomOBJ.Description__c = description;
                        }

                        String productType = csvRecordData[headerList.indexOf('Product Type')].replaceAll('(\\r|\\n)+', '');
                        if (!Constants.BOM_PRODUCT_TYPE_OPTIONS.values().contains(productType)) {
                            if(!advancedLogisticsEnabled) {
                                response.addRowError(rowNum, 'Invalid Product Type detected. Please make sure to use an entry from the picklist on the template.');
                            }
                        } else {
                            bomOBJ.Product_Type__c = productType;
                            String category = productTypeToCategoryMap.get(productType);
                            if(String.isBlank(category)) {
                                bomOBJ.Category__c = 'Hardware';
                            } else {
                                bomOBJ.Category__c = productTypeToCategoryMap.get(productType);
                            }

                        }

                        if(advancedLogisticsEnabled) {
                            /** HS CODE **/
                            String hsCode = csvRecordData[headerList.indexOf('HS Code')].replaceAll('(\\r|\\n)+', '');
                            if(String.isBlank(hsCode)) {
                                response.addRowError(rowNum, 'HS Code is blank.');
                            } else {
                                bomOBJ.HS_Code__c = hsCode;
                            }

                            /** ECCN **/
                            String eccn = csvRecordData[headerList.indexOf('ECCN')].replaceAll('(\\r|\\n)+', '');
                            if(String.isBlank(eccn)) {
                                response.addRowError(rowNum, 'ECCN is blank.');
                            } else {
                                bomOBJ.ECCN__c = eccn;
                            }

                            /** COUNTRY OF ORIGIN **/
                            String country = csvRecordData[headerList.indexOf('Country of Origin')].replaceAll('(\\r|\\n)+', '');
                            if (Constants.COUNTRIES_BY_CODE_MAP.get(country) == null) {
                                if (Constants.COUNTRIES_BY_NAME_MAP.get(country) == null) {
                                    bomOBJ.Country_of_Origin__c = null;
                                    response.addRowError(rowNum, 'Unable to match country name or code. Please make sure you use the 2 letter ISO country code from the template.');
                                } else {
                                    bomOBJ.Country_of_Origin__c = Constants.COUNTRIES_BY_NAME_MAP.get(country).Id;
                                }
                            } else {
                                bomOBJ.Country_of_Origin__c = Constants.COUNTRIES_BY_CODE_MAP.get(country).Id;
                            }
                        }

                        /** CONDITION **/
                        String condition = csvRecordData[headerList.indexOf('Condition')].replaceAll('(\\r|\\n)+', '');
                        if(String.isBlank(condition)) {
                            response.addRowError(rowNum, 'Condition is blank, replaced with "New".');
                            bomOBJ.Condition__c = 'New';
                        } else if (!Constants.BOM_CONDITION_OPTIONS.values().contains(condition)) {
                            response.addRowError(rowNum, 'Invalid Condition detected, replaced with "New". Please make sure to use an entry from the picklist on the template.');
                            bomOBJ.Condition__c = 'New';
                        } else {
                            bomOBJ.Condition__c = condition;
                        }

                        /** QUANTITY **/
                        String quantityString = csvRecordData[headerList.indexOf('Quantity')].replaceAll('(\\r|\\n)+', '')?.trim();
                        if (String.isBlank(quantityString)) {
                            response.addRowError(rowNum, 'Quantity is blank.');
                        } else {
                            if(quantityString.isNumeric()) {
                                Integer quantity = Integer.valueOf(quantityString);
                                if(quantity < 0) {
                                    response.addRowError(rowNum, 'Quantity is negative.');
                                } else {
                                    bomOBJ.Quantity__c = quantity;
                                }
                            } else {
                                response.addRowError(rowNum, 'Quantity is not numeric.');
                            }
                        }

                        /** VALUE **/
                        String unitValueString = csvRecordData[headerList.indexOf('Unit Value')].replaceAll('(\\r|\\n|\\$|\\,|\"|\\“|\\”)+', '').trim();
                        if (String.isBlank(unitValueString)) {
                            response.addRowError(rowNum, 'Unit Value is blank, replaced with 0.');
                            bomOBJ.Value__c = 0;
                        } else {
                            if(FGXEnt_Utilities.isNumber(unitValueString)) {
                                Decimal unitValue = Decimal.valueOf(unitValueString);
                                if(unitValue < 0) {
                                    response.addRowError(rowNum, 'Unit Value is negative.');
                                } else {
                                    bomOBJ.Value__c = unitValue;
                                }
                            } else {
                                response.addRowError(rowNum, 'Unit Value is not numeric.');
                            }
                        }

                        /** CREATED BY TYPE. Set to platform **/
                        bomOBJ.Created_By_Type__c = 'Platform';

                        
                        /** SORT ORDER. Set to rowNum+1 */
                        bomOBJ.SortOrder__c = rowNum + 1;
                        
                        response.addLine(bomOBJ);
                    }
                }
            }
        }

        System.debug(response);

        return response;
    }

    private static Boolean headersValid(List<String> parsedHeaders, List<String> expectedHeaders) {
        Integer col = 0;
        for(String expectedHeader : expectedHeaders) {
            String parsedHeader = parsedHeaders[col]?.replaceAll('(\\r|\\n)+', '');
            if(!expectedHeader.equalsIgnoreCase(parsedHeader)) {
                return false;
            }
            col++;
        }
        return true;
    }

    public class BOMCSVResponse {
        @AuraEnabled
        public List<BOM_Line__c> responseLines;
        @AuraEnabled
        public List<String> fatalErrors;
        @AuraEnabled
        public Map<Integer, List<String>> rowErrors;
        @AuraEnabled
        public List<String> bomLineDeleteErrors;
        @AuraEnabled
        public Boolean deleteExistingLinesSuccess {
            get {
                return bomLineDeleteErrors.isEmpty();
            }
        }
        @AuraEnabled
        public Boolean parsingSuccess {
            get {
                return fatalErrors.isEmpty() && !responseLines.isEmpty();
            }
        }

        public BOMCSVResponse() {
            this.responseLines = new List<BOM_Line__c>();
            this.fatalErrors = new List<String>();
            this.rowErrors = new Map<Integer, List<String>>();
            this.bomLineDeleteErrors = new List<String>();
        }

        public void addFatalError(String error) {
            fatalErrors.add(error);
        }

        public void addRowError(Integer row, String error) {
            if(this.rowErrors.containsKey(row)) {
                this.rowErrors.get(row).add(error);
            } else {
                this.rowErrors.put(row, new List<String> {error});
            }
        }

        public void addLine(BOM_Line__c line) {
            responseLines.add(line);
        }
    }

    public class BOMCSVResponseLine {
        @AuraEnabled
        public BOM_Line__c line;
        @AuraEnabled
        public List<String> errors;

        public BOMCSVResponseLine(BOM_Line__c line) {
            this.line = line;
            this.errors = new List<String>();
        }

        public void addRowError(String error) {
            errors.add(error);
        }
    }
}