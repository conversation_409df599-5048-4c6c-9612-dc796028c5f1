public with sharing class OutboundManifestDetailsController {
    //Associating a Case # with the Outbound Manifest
    @AuraEnabled
    public static List<Case> associateCaseWithManifest(Id caseId, Id outboundManifestId) {
        try {
            //Validate input parameters
            if (String.isBlank(caseId) || String.isBlank(outboundManifestId)) {
                throw new AuraHandledException('Both Case ID and Outbound Manifest ID are required.');
            }

            //Query the case
            Case c = [SELECT Id, CaseNumber, Outbound_Manifest__c FROM Case WHERE Id = :caseId LIMIT 1];

            //Update case with manifest reference
            c.Outbound_Manifest__c = outboundManifestId;
            update c;

            //Return updated list of cases for this manifest
            return [
                SELECT Id, CaseNumber, Account.Name, To__c, Manifest_Status__c, Owner.Alias
                FROM Case
                WHERE Outbound_Manifest__c = :outboundManifestId
            ];

        } catch (Exception e) {
            throw new AuraHandledException('An unexpected error occurred: ' + e.getMessage());
        }
    }

    //Gets Case #s for the Manifest
    @AuraEnabled
    public static List<Case> getCasesForManifest(Id outboundManifestId) {
        return [
            SELECT Id, CaseNumber, Account.Name, To__c, Manifest_Status__c, Owner.Alias, Origin_Country__c, Origin_Country__r.Name
            FROM Case
            WHERE Outbound_Manifest__c = :outboundManifestId
        ];
    }

    @AuraEnabled
    public static void removeCaseFromManifest(Id caseId) {
        try {
            //Validate input parameter
            if (String.isBlank(caseId)) {
                throw new AuraHandledException('Case ID is required.');
            }

            //Check if case linked to a manifest
            Case caseToUpdate = [SELECT Id, Outbound_Manifest__c, CaseNumber 
                                FROM Case 
                                WHERE Id = :caseId 
                                LIMIT 1];
            
            if (caseToUpdate.Outbound_Manifest__c == null) {
                return; //Not linked to any manifest
            }

            //Find all package lines that reference this case in HAWB__c
            List<Manifest_Package_Line__c> packageLinesToUpdate = [
                SELECT Id 
                FROM Manifest_Package_Line__c 
                WHERE Outbound_Manifest__c = :caseToUpdate.Outbound_Manifest__c
                AND HAWB__c = :caseId
            ];

            //Clear HAWB references
            for (Manifest_Package_Line__c line : packageLinesToUpdate) {
                line.HAWB__c = null;
            }

            //Update the case to remove manifest reference
            caseToUpdate.Outbound_Manifest__c = null;
            
            //Perform all updates in one transaction
            List<SObject> objectsToUpdate = new List<SObject>();
            objectsToUpdate.add(caseToUpdate);
            objectsToUpdate.addAll(packageLinesToUpdate);
            
            update objectsToUpdate;
            publishRefreshEvent();
            
        } catch (DmlException e) {
            throw new AuraHandledException('Error removing case from manifest: ' + e.getMessage());
        } catch (Exception e) {
            throw new AuraHandledException('An error occurred while removing case from manifest: ' + e.getMessage());
        }
    }
    
    @AuraEnabled(cacheable=true)
    public static List<Map<String, String>> getPackingTypeOptions() {
        List<Map<String, String>> options = new List<Map<String, String>>();
        
        Schema.DescribeFieldResult fieldResult = Manifest_Package_Line__c.Packing_Type__c.getDescribe();
        for (Schema.PicklistEntry entry : fieldResult.getPicklistValues()) {
            if (entry.isActive()) {
                options.add(new Map<String, String>{
                    'label' => entry.getLabel(),
                    'value' => entry.getValue()
                });
            }
        }
        
        return options;
    }

    //Gets package lines and piece information for each associated case 
    @AuraEnabled
    public static List<Map<String, Object>> getPackageLines(Id outboundManifestId) {
        List<Map<String, Object>> result = new List<Map<String, Object>>();
    
        //Check if any manifest lines exist first
        List<Manifest_Package_Line__c> manifestLines = [
            SELECT Id, Packing_Type__c, Length__c, Width__c, Height__c, 
                Unit_Weight__c, Num_Pcs__c, Total_Weight__c, Source_Line_Id__c,
                HAWB__c, HAWB__r.CaseNumber, HAWB__r.Id
            FROM Manifest_Package_Line__c
            WHERE Outbound_Manifest__c = :outboundManifestId
            ORDER BY CreatedDate ASC
        ];
    
        //If manifest lines exist, return them
        if (!manifestLines.isEmpty()) {
            for (Manifest_Package_Line__c line : manifestLines) {
                result.add(createManifestLineMap(line));
            }
            return result;
        }
        
        //Otherwise, get all shipment lines for linked cases
        for (Shipment_Package_Line__c shipLine : [
            SELECT Id, Packing_Type__c, Length__c, Width__c, Height__c, 
                Unit_Weight__c, Num_Pcs__c, Total_Weight__c, Shipment__c,
                Shipment__r.CaseNumber
            FROM Shipment_Package_Line__c
            WHERE Shipment__c IN (
                SELECT Id FROM Case 
                WHERE Outbound_Manifest__c = :outboundManifestId
            )
        ]) {
            result.add(createShipmentLineMap(shipLine));
        }
    
        return result;
    }

    //Helper method to create manifest line map
    private static Map<String, Object> createManifestLineMap(Manifest_Package_Line__c line) {
        Map<String, Object> lineMap = new Map<String, Object>{
            'Id' => line.Id,
            'Packing_Type__c' => line.Packing_Type__c,
            'Length__c' => line.Length__c,
            'Width__c' => line.Width__c,
            'Height__c' => line.Height__c,
            'Unit_Weight__c' => line.Unit_Weight__c,
            'Num_Pcs__c' => line.Num_Pcs__c,
            'Total_Weight__c' => line.Total_Weight__c,
            'Length_Unit__c' => 'in',
            'Weight_Unit__c' => 'kg',
            'isManifestLine' => true,
            'sourceLineId' => line.Source_Line_Id__c,
            'HAWB__c' => line.HAWB__c,
            'HAWB__r.CaseNumber' => line.HAWB__r?.CaseNumber,
            'HAWB__r.Id' => line.HAWB__r?.Id
        };
        
        if (line.HAWB__c != null) {
            lineMap.put('linkedHawbPills', new List<Map<String, String>>{
                new Map<String, String>{
                    'label' => line.HAWB__r.CaseNumber,
                    'name' => line.HAWB__r.CaseNumber,
                    'id' => line.HAWB__r.Id,
                    'href' => '/' + line.HAWB__r.Id
                }
            });
        }
        
        return lineMap;
    }

     //Helper method to create shipment line map
     private static Map<String, Object> createShipmentLineMap(Shipment_Package_Line__c shipLine) {
        return new Map<String, Object>{
            'Id' => shipLine.Id,
            'Packing_Type__c' => shipLine.Packing_Type__c,
            'Length__c' => shipLine.Length__c,
            'Width__c' => shipLine.Width__c,
            'Height__c' => shipLine.Height__c,
            'Unit_Weight__c' => shipLine.Unit_Weight__c,
            'Num_Pcs__c' => shipLine.Num_Pcs__c,
            'Total_Weight__c' => shipLine.Total_Weight__c,
            'Length_Unit__c' => 'in',
            'Weight_Unit__c' => 'kg',
            'isManifestLine' => false,
            'HAWB__c' => shipLine.Shipment__c,
            'HAWB__r.CaseNumber' => shipLine.Shipment__r.CaseNumber,
            'Source_Line_Id__c' => shipLine.Id
        };
    }
    
    @AuraEnabled
    public static List<Manifest_Package_Line__c> convertShipmentToManifestLines(Id manifestId) {
        try {
            //Get linked cases
            Map<Id, Case> caseMap = new Map<Id, Case>([
                SELECT Id, CaseNumber 
                FROM Case 
                WHERE Outbound_Manifest__c = :manifestId
            ]);

            if (caseMap.isEmpty()) {
                return new List<Manifest_Package_Line__c>();
            }

            //Block reconversion if manifest lines already exist
            Integer manifestLineCount = [
                SELECT COUNT()
                FROM Manifest_Package_Line__c
                WHERE Outbound_Manifest__c = :manifestId
            ];
            if (manifestLineCount > 0) {
                return new List<Manifest_Package_Line__c>();
            }

            //Get shipment lines that aren't already referenced by manifest lines
            Set<Id> alreadyConvertedIds = new Set<Id>();
            for (Manifest_Package_Line__c mpl : [
                SELECT Source_Line_Id__c 
                FROM Manifest_Package_Line__c 
                WHERE Outbound_Manifest__c = :manifestId
                AND Source_Line_Id__c != null
            ]) {
                alreadyConvertedIds.add(mpl.Source_Line_Id__c);
            }

            List<Shipment_Package_Line__c> shipmentLinesToConvert = [
                SELECT Id, Packing_Type__c, Length__c, Width__c, Height__c, 
                    Unit_Weight__c, Num_Pcs__c, Total_Weight__c, Shipment__c
                FROM Shipment_Package_Line__c
                WHERE Shipment__c IN :caseMap.keySet()
                AND Id NOT IN :alreadyConvertedIds //Only get unconverted
            ];

            if (shipmentLinesToConvert.isEmpty()) {
                return new List<Manifest_Package_Line__c>();
            }

            //Build and insert new manifest lines
            List<Manifest_Package_Line__c> newManifestLines = new List<Manifest_Package_Line__c>();
            
            for (Shipment_Package_Line__c shipLine : shipmentLinesToConvert) {
                newManifestLines.add(new Manifest_Package_Line__c(
                    Outbound_Manifest__c = manifestId,
                    Source_Line_Id__c = shipLine.Id, //tracking original shipment line
                    Packing_Type__c = shipLine.Packing_Type__c,
                    Length__c = shipLine.Length__c,
                    Width__c = shipLine.Width__c,
                    Height__c = shipLine.Height__c,
                    Unit_Weight__c = shipLine.Unit_Weight__c,
                    Num_Pcs__c = shipLine.Num_Pcs__c,
                    Length_Unit__c = 'in',
                    Weight_Unit__c = 'kg',
                    HAWB__c = shipLine.Shipment__c
                ));
            }

            insert newManifestLines;
            publishRefreshEvent();
            return newManifestLines;

        } catch (Exception e) {
            throw new AuraHandledException('Error converting shipment to manifest lines: ' + e.getMessage());
        }
    }


    //Update Packing Lines from Override Weight/Dim & Save, Refresh/Update
    @AuraEnabled
    public static List<Manifest_Package_Line__c> updateManifestPackageLines(List<Map<String, Object>> updates) {
        try {
            List<Manifest_Package_Line__c> recordsToUpsert = new List<Manifest_Package_Line__c>();
            Id manifestId = null;
            
            //Gets manifest ID from existing records
            for (Map<String, Object> line : updates) {
                if (line.get('Id') != null && !String.valueOf(line.get('Id')).startsWith('tmp')) {
                    try {
                        Manifest_Package_Line__c existing = [SELECT Outbound_Manifest__c 
                                                            FROM Manifest_Package_Line__c 
                                                            WHERE Id = :(Id)line.get('Id') 
                                                            LIMIT 1];
                        if (existing != null) {
                            manifestId = existing.Outbound_Manifest__c;
                            break;
                        }
                    } catch (QueryException e) {
                    }
                }
            }

            //Process each update with proper type conversion
            for (Map<String, Object> line : updates) {
                Manifest_Package_Line__c record = new Manifest_Package_Line__c();
                
                if (line.get('Id') != null && !String.valueOf(line.get('Id')).startsWith('tmp')) {
                    record.Id = (Id)line.get('Id');
                }
                if (record.Id == null && manifestId != null) {
                    record.Outbound_Manifest__c = manifestId;
                }
                if (line.get('isManifestLine') == false && line.get('sourceLineId') != null) {
                    record.Source_Line_Id__c = (String)line.get('sourceLineId');
                }

                record.Packing_Type__c = (String)line.get('Packing_Type__c');
                
                if (line.get('Box_Count__c') != null && record.Packing_Type__c == 'Pallet') {
                    record.Box_Count__c = Decimal.valueOf(String.valueOf(line.get('Box_Count__c')));
                    record.Num_Pcs__c = record.Box_Count__c;
                } else {
                    record.Box_Count__c = null;
                    record.Num_Pcs__c = 1;
                }
                
                record.Length__c = line.get('Length__c') != null ? Decimal.valueOf(String.valueOf(line.get('Length__c'))) : 0;
                record.Width__c = line.get('Width__c') != null ? Decimal.valueOf(String.valueOf(line.get('Width__c'))) : 0;
                record.Height__c = line.get('Height__c') != null ? Decimal.valueOf(String.valueOf(line.get('Height__c'))) : 0;
                record.Unit_Weight__c = line.get('Unit_Weight__c') != null ? Decimal.valueOf(String.valueOf(line.get('Unit_Weight__c'))) : 0;
                record.Length_Unit__c = 'in';
                record.Weight_Unit__c = 'kg';

                recordsToUpsert.add(record);
            }

            upsert recordsToUpsert;
            publishRefreshEvent();

            //Return updated records with all fields
            return [
                SELECT Id, Packing_Type__c, Length__c, Width__c, Height__c,
                    Unit_Weight__c, Box_Count__c, Num_Pcs__c, Total_Weight__c,
                    Length_Unit__c, Weight_Unit__c, Source_Line_Id__c,
                    HAWB__c, HAWB__r.CaseNumber
                FROM Manifest_Package_Line__c
                WHERE Id IN :recordsToUpsert
            ];

        } catch (Exception e) {
            throw new AuraHandledException('Error updating manifest package lines: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static Manifest_Package_Line__c addManifestPackageLine(Id manifestId, String packingType) {
        try {
            //Validate input parameters
            if (String.isBlank(manifestId)) {
                throw new AuraHandledException('Manifest ID is required.');
            }
            if (String.isBlank(packingType)) {
                throw new AuraHandledException('Packing type is required.');
            }
    
            //Create new package line
            Manifest_Package_Line__c newLine = new Manifest_Package_Line__c(
                Outbound_Manifest__c = manifestId,
                Packing_Type__c = packingType,
                Length_Unit__c = 'in',
                Weight_Unit__c = 'kg',
                Num_Pcs__c = 1 
            );
            
            try {
                insert newLine;
                publishRefreshEvent();
            } catch (DmlException e) {
                throw new AuraHandledException('Failed to create package line: ' + e.getMessage());
            }
    
            //Retrieve full record with all fields
            try {
                return [SELECT Id, Packing_Type__c, Length__c, Width__c, Height__c, 
                        Unit_Weight__c, Num_Pcs__c, Total_Weight__c, Length_Unit__c, Weight_Unit__c
                        FROM Manifest_Package_Line__c 
                        WHERE Id = :newLine.Id
                        LIMIT 1];
            } catch (QueryException e) {
                throw new AuraHandledException('Failed to retrieve created package line: ' + e.getMessage());
            }
    
        } catch (Exception e) {
            throw new AuraHandledException('An error occurred while adding package line: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static void deleteManifestPackageLine(Id lineId) {
        try {
            if (String.isBlank(lineId)) {
                throw new AuraHandledException('Line ID is required.');
            }
    
            //Get manifest package line with its source line reference
            Manifest_Package_Line__c lineToDelete = [
                SELECT Id, Source_Line_Id__c, Outbound_Manifest__c 
                FROM Manifest_Package_Line__c 
                WHERE Id = :lineId 
                LIMIT 1
            ];
    
            if (lineToDelete == null) {
                throw new AuraHandledException('Package line not found for deletion.');
            }
    
            delete lineToDelete;

            publishRefreshEvent();
            
        } catch (Exception e) {
            throw new AuraHandledException('Error deleting package line: ' + e.getMessage());
        }
    }
    
    @AuraEnabled
    public static void removeHawbFromPackageLine(Id packageLineId, Id hawbIdToRemove) {
        try {
            //Query the package line
            Manifest_Package_Line__c line = [
                SELECT Id, HAWB__c
                FROM Manifest_Package_Line__c 
                WHERE Id = :packageLineId
                LIMIT 1
            ];
            
            //Clear HAWB reference
            line.HAWB__c = null;
            
            update line;

            publishRefreshEvent();
        } catch (Exception e) {
            throw new AuraHandledException('Error removing HAWB from package line: ' + e.getMessage());
        }
    }
    
    @AuraEnabled
    public static void removeHawbsFromPackageLines(List<Map<String, String>> removalRequests) {
        try {
            //Validate input
            if (removalRequests == null || removalRequests.isEmpty()) {
                throw new AuraHandledException('No removal requests provided');
            }
    
            Set<Id> lineIds = new Set<Id>();
            Set<Id> hawbIds = new Set<Id>();
            
            for (Map<String, String> request : removalRequests) {
                String lineId = request.get('lineId');
                String hawbId = request.get('hawbId');
                
                if (String.isBlank(lineId) || String.isBlank(hawbId)) {
                    throw new AuraHandledException('Missing required fields in removal request');
                }
                
                lineIds.add(lineId);
                hawbIds.add(hawbId);
            }
            
            //Query and update
            List<Manifest_Package_Line__c> linesToUpdate = [
                SELECT Id, HAWB__c
                FROM Manifest_Package_Line__c 
                WHERE Id IN :lineIds
                AND HAWB__c IN :hawbIds
            ];
            
            for (Manifest_Package_Line__c line : linesToUpdate) {
                //Clear HAWB reference
                line.HAWB__c = null;
            }
            
            update linesToUpdate;

            publishRefreshEvent();
        } catch (Exception e) {
            throw new AuraHandledException('Error removing HAWBs: ' + e.getMessage());
        }
    }

    @AuraEnabled(cacheable=true)
    public static List<Case> getAvailableHawbs(Id manifestId) {
        try {
            return [
                SELECT Id, CaseNumber 
                FROM Case 
                WHERE Outbound_Manifest__c = :manifestId
                ORDER BY CaseNumber
            ];
        } catch (Exception e) {
            throw new AuraHandledException('Error retrieving available HAWBs: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static void linkHawbToPackageLine(Id packageLineId, Id hawbId) {
        try {
            if (String.isBlank(packageLineId)) {
                throw new AuraHandledException('Package Line ID is required.');
            }
            if (String.isBlank(hawbId)) {
                throw new AuraHandledException('HAWB ID is required.');
            }

            //Query the package line
            Manifest_Package_Line__c existingLine = [
                SELECT Id, HAWB__c 
                FROM Manifest_Package_Line__c 
                WHERE Id = :packageLineId 
                LIMIT 1
            ];

            if (existingLine.HAWB__c != null) {
                throw new AuraHandledException('This package line is already linked to HAWB: ' + existingLine.HAWB__c);
            }

            //Update the package line
            existingLine.HAWB__c = hawbId;
            update existingLine;

            publishRefreshEvent();

        } catch (Exception e) {
            throw new AuraHandledException('Error linking HAWB to package line: ' + e.getMessage());
        }
    }


    
    
    @AuraEnabled(cacheable=true)
    public static Outbound_Manifest__c getCarrierRoutingDetails(String recordId){
        return [SELECT Id, Carrier__c, Carrier__r.Name, Carrier__r.RecordType.Name, Service_Level__c, Service_Level__r.Service_Level_Name__c, 
                        Service_Level__r.Service_Level_Code__c, TRN_Override__c, Transportation_Reference_Number__c, Transportation_Reference_Number__r.Bill_Number__c,
                        Transportation_Reference_Number__r.MAWB_Number__c, Transportation_Reference_Number__r.Used_By__c, Transportation_Reference_Number__r.Used_By__r.Alias,
                        Transportation_Reference_Number__r.Used_Time__c, Leg_1_Arrival__c, Leg_1_Arrival__r.Name, Leg_1_Carrier__c, Leg_1_Carrier__r.Name, 
                        Leg_1_Carrier__r.IATA_Code__c, Leg_1_Departure__c, Leg_1_Departure__r.Name, Leg_1_Departure_Date__c, Leg_1_Flight_Number__c, Leg_2_Arrival__c, 
                        Leg_2_Arrival__r.Name, Leg_2_Carrier__c, Leg_2_Carrier__r.Name, Leg_2_Carrier__r.IATA_Code__c, Leg_2_Departure__c, Leg_2_Departure__r.Name, 
                        Leg_2_Departure_Date__c, Leg_2_Flight_Number__c, Leg_3_Arrival__c, Leg_3_Arrival__r.Name, Leg_3_Carrier__c, Leg_3_Carrier__r.Name, 
                        Leg_3_Carrier__r.IATA_Code__c, Leg_3_Departure__c, Leg_3_Departure__r.Name, Leg_3_Departure_Date__c, Leg_3_Flight_Number__c, 
                        Leg_4_Arrival__c, Leg_4_Arrival__r.Name, Leg_4_Carrier__c, Leg_4_Carrier__r.Name, Leg_4_Carrier__r.IATA_Code__c,
                        Leg_4_Departure__c, Leg_4_Departure__r.Name, Leg_4_Departure_Date__c, Leg_4_Flight_Number__c, Surcharge_1_Description__c, 
                        Surcharge_1_Minimum__c, Surcharge_1_Rate__c, Surcharge_2_Description__c, Surcharge_2_Minimum__c, Surcharge_2_Rate__c, 
                        Surcharge_3_Description__c, Surcharge_3_Minimum__c, Surcharge_3_Rate__c, Surcharge_4_Description__c, Surcharge_4_Minimum__c,
                        Surcharge_4_Rate__c, Tender_Date__c, Freight_Rate__c, Minimum_Rate__c, Total_Number_of_Pieces__c, Total_Gross_Weight__c, 
                        Chargeable_Weight_Output__c, Total_IATA_Volumetric_Weight__c
                    FROM Outbound_Manifest__c WHERE Id = :recordId];
    }

    @AuraEnabled
    public static Outbound_Manifest__c setOutboundCarrier(String recordId, String carrierId){
        Savepoint sp = Database.setSavepoint();
        try {
            // if carrier is changing, clear the service level and TRN since service levels differ depending on carriers.
            Outbound_Manifest__c manifest = [SELECT Id, Transportation_Reference_Number__c FROM Outbound_Manifest__c WHERE Id =: recordId];

            if (String.isNotBlank(manifest.Transportation_Reference_Number__c)){
                // clear TRN used by and used time since we are unassigning it
                Transportation_Reference_Number__c trn = new Transportation_Reference_Number__c(Id = manifest.Transportation_Reference_Number__c, 
                                                                                                    Used_By__c = null, 
                                                                                                    Used_Time__c = null);
                Database.SaveResult trnResult = Database.update(trn, false);
                if (!trnResult.isSuccess()){
                    for(Database.Error error : trnResult.getErrors()) {
                        String errorMessage = 'Error updating TRN: ' + error.getStatusCode() + ': ' + error.getMessage();
                        System.debug(errorMessage);
                        throw new DmlException(errorMessage);
                    }
                }
            }

            manifest.Carrier__c = carrierId;
            manifest.Service_Level__c = null;
            manifest.Transportation_Reference_Number__c = null;
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            Database.rollback(sp);
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static List<Service_Level__c> getServiceLevelOptions(String carrierId){
        return [SELECT Id, Service_Level_Name__c FROM Service_Level__c WHERE Carrier__c = :carrierId];
    }

    @AuraEnabled
    public static Outbound_Manifest__c setServiceLevel(String recordId, String serviceLevelId){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Service_Level__c = serviceLevelId);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c setManualMawb(String recordId, String mawb){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, TRN_Override__c = mawb);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c assignMawb(String recordId, String carrierId){
        Savepoint sp = Database.setSavepoint();
        try {
            Carrier__c carr = [SELECT Id, RecordTypeId FROM Carrier__c WHERE Id =: carrierId];
            // if not airline, do not do anything
            if (carr.RecordTypeId == Constants.CARRIER_AIRLINE_RT){
                List<Transportation_Reference_Number__c> trns = [SELECT Id FROM Transportation_Reference_Number__c 
                                                                    WHERE Used_By__c = null AND Used_Time__c = null AND Carrier__c =: carrierId AND
                                                                        (Expiration_Date__c = null OR Expiration_Date__c >: Date.today())
                                                                        ORDER BY CreatedDate ASC];
                if (!trns.isEmpty()){
                    Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Transportation_Reference_Number__c = trns[0].Id);
                    Database.SaveResult result = Database.update(manifest, false);
                    if (!result.isSuccess()){
                        for(Database.Error error : result.getErrors()) {
                            String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                            System.debug(errorMessage);
                            throw new DmlException(errorMessage);
                        }
                    }

                    Transportation_Reference_Number__c trn = new Transportation_Reference_Number__c(Id = trns[0].Id, Used_By__c = UserInfo.getUserId(), Used_Time__c = DateTime.now());
                    Database.SaveResult trnResult = Database.update(trn, false);
                    if (!trnResult.isSuccess()){
                        for(Database.Error error : trnResult.getErrors()) {
                            String errorMessage = 'Error updating TRN: ' + error.getStatusCode() + ': ' + error.getMessage();
                            System.debug(errorMessage);
                            throw new DmlException(errorMessage);
                        }
                    }
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            Database.rollback(sp);
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c unassignMawb(String recordId, String mawbId){
        Savepoint sp = Database.setSavepoint();
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Transportation_Reference_Number__c = null);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            Transportation_Reference_Number__c trn = new Transportation_Reference_Number__c(Id = mawbId, Used_By__c = null, Used_Time__c = null);
            Database.SaveResult trnResult = Database.update(trn, false);
            if (!trnResult.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating TRN: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            Database.rollback(sp);
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c setDepartureAirport(String recordId, String airportId){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Leg_1_Departure__c = airportId);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c setArrivalAirport(String recordId, String airportId){
        try {
            // find the last leg and set it's arrival airport. 
            // the last leg's departure airport is always set equal to the previous leg's arrival airport upon creation so it's the only required field
            Outbound_Manifest__c manifest = [SELECT Leg_1_Departure__c, Leg_2_Departure__c, Leg_3_Departure__c, Leg_4_Departure__c
                                                FROM Outbound_Manifest__c WHERE Id =: recordId];
            if (String.isNotBlank(manifest.Leg_4_Departure__c)){
                manifest.Leg_4_Arrival__c = airportId;
            } else if (String.isNotBlank(manifest.Leg_3_Departure__c)){
                manifest.Leg_3_Arrival__c = airportId;
            } else if (String.isNotBlank(manifest.Leg_2_Departure__c)){
                manifest.Leg_2_Arrival__c = airportId;
            } else if (String.isNotBlank(manifest.Leg_1_Departure__c)){
                manifest.Leg_1_Arrival__c = airportId;
            }

            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c setTenderDate(String recordId, String tenderDate){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Tender_Date__c = Date.valueOf(tenderDate));
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c addLeg(String recordId){
        try {
            // find the current last leg
            Outbound_Manifest__c manifest = [SELECT Leg_1_Departure__c, Leg_1_Arrival__c, Leg_2_Departure__c, Leg_2_Arrival__c, Leg_3_Departure__c, Leg_3_Arrival__c
                                                FROM Outbound_Manifest__c WHERE Id =: recordId];
            // since the final arrival airport is always set, set it as the new last leg's arrival and clear out the previous leg's arrival
            if (String.isNotBlank(manifest.Leg_3_Departure__c) || String.isNotBlank(manifest.Leg_3_Arrival__c)){
                manifest.Leg_4_Arrival__c = manifest.Leg_3_Arrival__c;
                manifest.Leg_3_Arrival__c = null;
            } else if (String.isNotBlank(manifest.Leg_2_Departure__c) || String.isNotBlank(manifest.Leg_2_Arrival__c)){
                manifest.Leg_3_Arrival__c = manifest.Leg_2_Arrival__c;
                manifest.Leg_2_Arrival__c = null;
            } else if (String.isNotBlank(manifest.Leg_1_Departure__c) || String.isNotBlank(manifest.Leg_1_Arrival__c)){
                manifest.Leg_2_Arrival__c = manifest.Leg_1_Arrival__c;
                manifest.Leg_1_Arrival__c = null;
            }

            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c deleteLeg(String recordId){
        try {
            // find the current last leg
            Outbound_Manifest__c manifest = [SELECT Leg_1_Departure__c, Leg_1_Arrival__c, Leg_2_Departure__c, Leg_2_Arrival__c, Leg_3_Departure__c, Leg_3_Arrival__c,
                                                    Leg_4_Departure__c, Leg_4_Arrival__c
                                                FROM Outbound_Manifest__c WHERE Id =: recordId];
            // set the previous leg's arrival airport equal to it AND then clear the leg to "delete" it
            if (String.isNotBlank(manifest.Leg_4_Departure__c) || String.isNotBlank(manifest.Leg_4_Arrival__c)){
                manifest.Leg_4_Departure__c = null;
                manifest.Leg_3_Arrival__c = manifest.Leg_4_Arrival__c;
                manifest.Leg_4_Arrival__c = null;
                manifest.Leg_4_Carrier__c = null;
                manifest.Leg_4_Departure_Date__c = null;
                manifest.Leg_4_Flight_Number__c = null;
            } else if (String.isNotBlank(manifest.Leg_3_Departure__c) || String.isNotBlank(manifest.Leg_3_Arrival__c)){
                manifest.Leg_3_Departure__c = null;
                manifest.Leg_2_Arrival__c = manifest.Leg_3_Arrival__c;
                manifest.Leg_3_Arrival__c = null;
                manifest.Leg_3_Carrier__c = null;
                manifest.Leg_3_Departure_Date__c = null;
                manifest.Leg_3_Flight_Number__c = null;
            } else if (String.isNotBlank(manifest.Leg_2_Departure__c) || String.isNotBlank(manifest.Leg_2_Arrival__c)){
                manifest.Leg_2_Departure__c = null;
                manifest.Leg_1_Arrival__c = manifest.Leg_2_Arrival__c;
                manifest.Leg_2_Arrival__c = null;
                manifest.Leg_2_Carrier__c = null;
                manifest.Leg_2_Departure_Date__c = null;
                manifest.Leg_2_Flight_Number__c = null;
            } else if (String.isNotBlank(manifest.Leg_1_Departure__c) || String.isNotBlank(manifest.Leg_1_Arrival__c)){
                // don't clear departure or arrival if only 1 leg
                manifest.Leg_1_Carrier__c = null;
                manifest.Leg_1_Departure_Date__c = null;
                manifest.Leg_1_Flight_Number__c = null;
            }

            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c updateManifestField(String recordId, String fieldName, String fieldValue){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId);
            if (fieldName.toLowerCase().contains('date')){
                if (String.isBlank(fieldValue)){
                    manifest.put(fieldName, null);
                }
                else {
                    Date dateValue = Date.valueOf(fieldValue);
                    manifest.put(fieldName, dateValue);
                }
            }
            else if (fieldName.toLowerCase().contains('rate') || fieldName.toLowerCase().contains('minimum')) {
                if (String.isBlank(fieldValue)){
                    manifest.put(fieldName, null);
                }
                else {
                    Decimal decimalValue = Decimal.valueOf(fieldValue);
                    manifest.put(fieldName, decimalValue);
                }
            }
            else {
                manifest.put(fieldName, fieldValue);
            }

            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static void setFreightRate(String recordId, String freightRate, Boolean minimum){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Freight_Rate__c = Decimal.valueOf(freightRate), Minimum_Rate__c = minimum);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c deleteSurcharge(String recordId, String surchargeNum){
        try {
            Outbound_Manifest__c manifest = [SELECT Id, Surcharge_1_Description__c, Surcharge_1_Minimum__c, Surcharge_1_Rate__c, Surcharge_2_Description__c, 
                                                    Surcharge_2_Minimum__c, Surcharge_2_Rate__c, Surcharge_3_Description__c, Surcharge_3_Minimum__c, Surcharge_3_Rate__c, 
                                                    Surcharge_4_Description__c, Surcharge_4_Minimum__c, Surcharge_4_Rate__c
                                                FROM Outbound_Manifest__c WHERE Id =: recordId];
            // simply clear out surcharge 4
            if (surchargeNum == '4'){
                manifest.Surcharge_4_Description__c = null;
                manifest.Surcharge_4_Minimum__c = null;
                manifest.Surcharge_4_Rate__c = null;
            } 
            // for every other one, move surcharges above it down one and clear out the last surcharge
            else if (surchargeNum == '3'){
                manifest.Surcharge_3_Description__c = manifest.Surcharge_4_Description__c;
                manifest.Surcharge_3_Minimum__c = manifest.Surcharge_4_Minimum__c;
                manifest.Surcharge_3_Rate__c = manifest.Surcharge_4_Rate__c;
                manifest.Surcharge_4_Description__c = null;
                manifest.Surcharge_4_Minimum__c = null;
                manifest.Surcharge_4_Rate__c = null;
            }
            else if (surchargeNum == '2'){
                manifest.Surcharge_2_Description__c = manifest.Surcharge_3_Description__c;
                manifest.Surcharge_2_Minimum__c = manifest.Surcharge_3_Minimum__c;
                manifest.Surcharge_2_Rate__c = manifest.Surcharge_3_Rate__c;
                manifest.Surcharge_3_Description__c = manifest.Surcharge_4_Description__c;
                manifest.Surcharge_3_Minimum__c = manifest.Surcharge_4_Minimum__c;
                manifest.Surcharge_3_Rate__c = manifest.Surcharge_4_Rate__c;
                manifest.Surcharge_4_Description__c = null;
                manifest.Surcharge_4_Minimum__c = null;
                manifest.Surcharge_4_Rate__c = null;
            }
            else if (surchargeNum == '1'){
                manifest.Surcharge_1_Description__c = manifest.Surcharge_2_Description__c;
                manifest.Surcharge_1_Minimum__c = manifest.Surcharge_2_Minimum__c;
                manifest.Surcharge_1_Rate__c = manifest.Surcharge_2_Rate__c;
                manifest.Surcharge_2_Description__c = manifest.Surcharge_3_Description__c;
                manifest.Surcharge_2_Minimum__c = manifest.Surcharge_3_Minimum__c;
                manifest.Surcharge_2_Rate__c = manifest.Surcharge_3_Rate__c;
                manifest.Surcharge_3_Description__c = manifest.Surcharge_4_Description__c;
                manifest.Surcharge_3_Minimum__c = manifest.Surcharge_4_Minimum__c;
                manifest.Surcharge_3_Rate__c = manifest.Surcharge_4_Rate__c;
                manifest.Surcharge_4_Description__c = null;
                manifest.Surcharge_4_Minimum__c = null;
                manifest.Surcharge_4_Rate__c = null;
            }

            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCarrierRoutingDetails(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled(cacheable = false)
    public static List<Commercial_Invoice_Line__c> getRelatedCils(List<String> caseIds) {
        return [SELECT Id, Manufacturer__c, Part_Number__c, Description__c, Product_Type__c, HS_Code__c, Total_Value__c 
                    FROM Commercial_Invoice_Line__c WHERE Shipment__c IN :caseIds AND Deleted__c = false ORDER BY Total_Value__c DESC];
    }

    @AuraEnabled(cacheable = true)
    public static Outbound_Manifest__c getCommoditiesInfo(String recordId) {
        return [SELECT Id, Contents_Description__c, HS_Code_Summary__c, HS_Code_Summary_Override__c
                    FROM Outbound_Manifest__c WHERE Id = :recordId];
    }

    @AuraEnabled
    public static Outbound_Manifest__c setContentsDescription(String recordId, String contentsDescription) {
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Contents_Description__c = contentsDescription);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCommoditiesInfo(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c generateHsCodeSummary(String recordId, List<String> caseIds){
        try{
            List<Commercial_Invoice_Line__c> cilList = [SELECT Id, HS_Code__c FROM Commercial_Invoice_Line__c 
                                                            WHERE Shipment__c IN :caseIds AND Deleted__c = false ORDER BY Total_Value__c DESC];

            // get top 3 elements and turn into one string separated by ,\n
            String resultString = '**DESTINATION HS CODE(S) NEEDED**';
            if (!cilList.isEmpty()){
                Integer i = 0;
                Set<String> firstThreeElements = new Set<String>();
                while(firstThreeElements.size() < 3 && i < cilList.size()) {
                    if (!String.isBlank(cilList[i].HS_Code__c)) {
                        firstThreeElements.add(cilList[i].HS_Code__c);
                    }
                    i++;
                }

                if (firstThreeElements.size() != 0) {
                    resultString = String.join(new List<String>(firstThreeElements), ',\n');
                }
            }
    
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, HS_Code_Summary__c = resultString, HS_Code_Summary_Override__c = false);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCommoditiesInfo(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Outbound_Manifest__c setHsCodeOverride(String recordId, String hsCodeSummary){
        try{
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, HS_Code_Summary__c = hsCodeSummary, HS_Code_Summary_Override__c = true);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return getCommoditiesInfo(recordId);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Boolean setBatteryLanguage(String recordId, String batteryLanguage){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Battery_Language__c = batteryLanguage);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return true;
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Boolean setBatteryType(String recordId, String batteryType){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, IATA_Battery_Type__c = batteryType);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return true;
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static String fetchBatteryLanguage(String recordId, String batteryType){
        try {
            Outbound_Manifest__c manifest = [SELECT Id, Carrier__r.Battery_Language_Override__c FROM Outbound_Manifest__c WHERE Id =: recordId];

            // use carrier override if it exists
            if (String.isNotBlank(manifest.Carrier__r.Battery_Language_Override__c)){
                manifest.Battery_Language__c = manifest.Carrier__r.Battery_Language_Override__c;
                Database.SaveResult result = Database.update(manifest, false);
                if (!result.isSuccess()){
                    for(Database.Error error : result.getErrors()) {
                        String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                        System.debug(errorMessage);
                        throw new DmlException(errorMessage);
                    }
                }

                publishRefreshEvent();

                return manifest.Carrier__r.Battery_Language_Override__c;
            }

            // find language in metadata
            List<Battery_Statements__mdt> statements = [SELECT Label, Battery_Language__c FROM Battery_Statements__mdt WHERE Label =: batteryType];
            if (statements.size() > 0){
                manifest.Battery_Language__c = statements[0].Battery_Language__c;
                Database.SaveResult result = Database.update(manifest, false);
                if (!result.isSuccess()){
                    for(Database.Error error : result.getErrors()) {
                        String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                        System.debug(errorMessage);
                        throw new DmlException(errorMessage);
                    }
                }

                publishRefreshEvent();

                return statements[0].Battery_Language__c;
            }

            return 'Not found';
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Boolean setHandlingInfo(String recordId, String handlingInfo){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Handling_Info__c = handlingInfo);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return true;
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static Boolean setAdditionalStatement(String recordId, String additionalStatement){
        try {
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Additional_Carrier_Statement__c = additionalStatement);
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();

            return true;
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled(cacheable = true)
    public static ManifestHandlingWrapper getSpecialHandling(String recordId) {
        Outbound_Manifest__c manifest = [SELECT Id, IATA_Battery_Type__c, Battery_Language__c, Carrier__c, Carrier__r.Battery_Language_Override__c, Carrier__r.Additional_Statement__c, 
            Handling_Info__c, Additional_Carrier_Statement__c FROM Outbound_Manifest__c WHERE Id = :recordId];
        List<Case> cases = [SELECT Id, IATA_Contains_Batteries__c, IATA_Battery_Type__c, CaseNumber FROM Case WHERE Outbound_Manifest__c =: recordId ];
        List<Asset_Unit__c> units = [SELECT Id, Manufacturer__c, Part_Number__c, Related_Shipment__c, Battery_Type__c FROM Asset_Unit__c WHERE Related_Shipment__c IN :cases];
        List<Warehouse_Pieces__c> pieces = [SELECT Id, Piece_Id__c, Battery_Type__c, Outbound_Shipment__c FROM Warehouse_Pieces__c WHERE Outbound_Shipment__c IN: cases];

        return new ManifestHandlingWrapper(manifest, cases, units, pieces);
    }

    public class ManifestHandlingWrapper {
        @AuraEnabled public Outbound_Manifest__c manifest;
        @AuraEnabled public List<Case> cases;
        @AuraEnabled public List<Asset_Unit__c> units;
        @AuraEnabled public List<Warehouse_Pieces__c> pieces;
        @AuraEnabled public Map<String, Map<String, String>> batteryTypePerShipment;

        public ManifestHandlingWrapper(Outbound_Manifest__c manifest, List<Case> cases, List<Asset_Unit__c> units, List<Warehouse_Pieces__c> pieces) {
            this.manifest = manifest;
            this.cases = cases;
            this.units = units;
            this.pieces = pieces;
            this.batteryTypePerShipment = new Map<String, Map<String, String>>();
            mapBatteryTypeToShipement();
        }

        private void mapBatteryTypeToShipement(){
            for (Case cse : cases){
                Set<String> unitBatteryTypes = new Set<String>();
                for (Asset_Unit__c unit : units) {
                    if (unit.Related_Shipment__c == cse.Id && unit.Battery_Type__c != null) {
                        unitBatteryTypes.add(unit.Battery_Type__c);
                    }
                }

                Set<String> pieceBatteryTypes = new Set<String>();
                for (Warehouse_Pieces__c piece : pieces) {
                    if (piece.Outbound_Shipment__c == cse.Id && piece.Battery_Type__c != null) {
                        pieceBatteryTypes.add(piece.Battery_Type__c);
                    }
                }

                if (unitBatteryTypes.size() > 0){
                    populateMap(cse, unitBatteryTypes, 'Warehouse Manifest');
                }
                else if (pieceBatteryTypes.size() > 0){
                    populateMap(cse, pieceBatteryTypes, 'Inbound');
                }
                else if (cse.IATA_Contains_Batteries__c){
                    if (!batteryTypePerShipment.containsKey(cse.CaseNumber)) {
                        batteryTypePerShipment.put(cse.CaseNumber, new Map<String, String>());
                    }
                    batteryTypePerShipment.get(cse.CaseNumber).put(cse.IATA_Battery_Type__c, 'Shipment');
                }
            }
        }

        private void populateMap(Case cse, Set<String> batteryTypes, String location){
            for (String type : Constants.batteryPriorityList) {
                String batteryType = type;
                if (batteryTypes.contains(batteryType)) {
                    // if battery type is 3481 or 3091, check if there is the other. if there are both, use the combined type instead if we did not add it already
                    if (batteryType == Constants.BATTERY_UN3481 && batteryTypes.contains(Constants.BATTERY_UN3091)){
                        // if we did not already add the combined type, add it
                        if (!batteryTypePerShipment.containsKey(cse.CaseNumber) || !batteryTypePerShipment.get(cse.CaseNumber).containsKey(Constants.BATTERY_UN3481_UN3091)){
                            batteryType = Constants.BATTERY_UN3481_UN3091;
                        }
                        // otherwise don't add anything
                        else {
                            batteryType = null;
                        }
                    }
                    else if (batteryType == Constants.BATTERY_UN3091 && batteryTypes.contains(Constants.BATTERY_UN3481)){
                        // if we did not already add the combined type, add it
                        if (!batteryTypePerShipment.containsKey(cse.CaseNumber) || !batteryTypePerShipment.get(cse.CaseNumber).containsKey(Constants.BATTERY_UN3481_UN3091)){
                            batteryType = Constants.BATTERY_UN3481_UN3091;
                        }
                        // otherwise don't add anything
                        else {
                            batteryType = null;
                        }
                    }

                    if (batteryType != null){
                        if (batteryTypePerShipment.containsKey(cse.CaseNumber)){
                            batteryTypePerShipment.get(cse.CaseNumber).put(batteryType, location);
                        }
                        else{
                            Map<String, String> innerMap = new Map<String, String>();
                            innerMap.put(batteryType, location);
                            batteryTypePerShipment.put(cse.CaseNumber, innerMap);
                        }
                    }
                }
            }
        }
    }

    @AuraEnabled
    public static Map<String, String> generateDocuments(Id recordId) {
        try {
            Outbound_Manifest__c manifest = [SELECT Id, Carrier__c, RecordTypeId, IATA_Battery_Type__c, Destination_Freight_Agent__c 
                                                FROM Outbound_Manifest__c WHERE Id =: recordId];
            List<Case> cses = [SELECT Id, CaseNumber FROM Case WHERE Outbound_Manifest__c =: recordId];
            List<String> cseIds = new List<String>();
            for (Case c : cses){
                cseIds.add(c.Id);
            }

            Map<String, String> templateMap = new Map<String, String>(); // map of templates and record id that lwc uses to keep track of what has been generated
            List<String> manifestTemplates = new List<String>(); // stores manifest related docs
            manifestTemplates.add(Constants.DOC_ACTION_PRINT_IAC);
            templateMap.put(Constants.DOC_ACTION_PRINT_IAC + ':' + recordId, 'generating');
            manifestTemplates.add(Constants.DOC_ACTION_PRINT_MAWB);
            templateMap.put(Constants.DOC_ACTION_PRINT_MAWB + ':' + recordId, 'generating');
            List<String> caseTemplates = new List<String>(); // stores case related docs

            List<Document_Template_Link__c> dtls = [SELECT Document_Name__c FROM Document_Template_Link__c WHERE Account__c =: manifest.Destination_Freight_Agent__c];
            for (Document_Template_Link__c dtl : dtls){
                caseTemplates.add(dtl.Document_Name__c);
                for (Case c : cses){
                    templateMap.put(dtl.Document_Name__c + ':' + c.Id, 'generating');
                }
            }

            // for direct
            if (manifest.RecordTypeId == Constants.OUTBOUND_MANIFEST_DIRECT_RT){
                // if batteries exist, print appropriate doc
                if (manifest.IATA_Battery_Type__c != null && manifest.IATA_Battery_Type__c != Constants.BATTERY_NO_BATTERIES){
                    // check if carrier has own docs first. if not use default ones
                    List<Document_Template_Link__c> batteryDtls = [SELECT Document_Name__c FROM Document_Template_Link__c 
                                                                        WHERE Carrier__c =: manifest.Carrier__c AND IATA_Battery_Type__c =: manifest.IATA_Battery_Type__c ];
                    if (batteryDtls.size() == 0){
                        for (Case c : cses){
                            if (manifest.IATA_Battery_Type__c == Constants.BATTERY_UN3091){
                                caseTemplates.add(Constants.DOC_ACTION_PRINT_BATTERY_UN3091);
                                templateMap.put(Constants.DOC_ACTION_PRINT_BATTERY_UN3091 + ':' + c.Id, 'generating');
                            } else if (manifest.IATA_Battery_Type__c == Constants.BATTERY_UN3481){
                                caseTemplates.add(Constants.DOC_ACTION_PRINT_BATTERY_UN3481);
                                templateMap.put(Constants.DOC_ACTION_PRINT_BATTERY_UN3481 + ':' + c.Id, 'generating');
                            } else if (manifest.IATA_Battery_Type__c == Constants.BATTERY_UN3481_UN3091){
                                caseTemplates.add(Constants.DOC_ACTION_PRINT_BATTERY_UN3481_UN3091);
                                templateMap.put(Constants.DOC_ACTION_PRINT_BATTERY_UN3481_UN3091 + ':' + c.Id, 'generating');
                            }
                        }
                    }
                    else {
                        for (Document_Template_Link__c dtl : batteryDtls){
                            for (Case c : cses){
                                caseTemplates.add(dtl.Document_Name__c);
                                templateMap.put(dtl.Document_Name__c + ':' + c.Id, 'generating');
                            }
                        }
                    }
                }
            }
            // for consolidated
            else if (manifest.RecordTypeId == Constants.OUTBOUND_MANIFEST_CONSOLIDATED_RT) {
                manifestTemplates.add(Constants.DOC_ACTION_PRINT_AIRLINE_MANIFEST);
                templateMap.put(Constants.DOC_ACTION_PRINT_AIRLINE_MANIFEST + ':' + recordId, 'generating');

                // generate certain docs for each case
                for (Case c : cses) {
                    caseTemplates.add(Constants.DOC_ACTION_PRINT_COMM_INV);
                    templateMap.put(Constants.DOC_ACTION_PRINT_COMM_INV + ':' + c.Id, 'generating');
                    caseTemplates.add(Constants.DOC_ACTION_PRINT_EXP_HAWB);
                    templateMap.put(Constants.DOC_ACTION_PRINT_EXP_HAWB + ':' + c.Id, 'generating');
                }

                // check if carrier has own docs first. if not use default ones
                List<Document_Template_Link__c> batteryDtls = [SELECT IATA_Battery_Type__c, Document_Name__c FROM Document_Template_Link__c 
                                                                WHERE Carrier__c =: manifest.Carrier__c];

                ManifestHandlingWrapper manifestWrapper = getSpecialHandling(recordId);
                for (String cseNum : manifestWrapper.batteryTypePerShipment.keySet()) {
                    Map<String, String> batteryMap = manifestWrapper.batteryTypePerShipment.get(cseNum);
                    
                    if (batteryMap != null && !batteryMap.isEmpty()) {
                        // check for battery type doc based on highestPrioBattery
                        String highestPrioBattery = new List<String>(batteryMap.keySet())[0];
                        for (Case c : cses) {
                            if (c.CaseNumber == cseNum) {
                                // print unique battery doc for each case depending on their battery type
                                List<String> cseId = new List<String>{c.Id};
                                List<String> batteryTemplate = new List<String>();

                                // check for carrier docs first
                                Boolean carrierTemplateFound = false;
                                for (Document_Template_Link__c batteryDtl : batteryDtls){
                                    if (batteryDtl.IATA_Battery_Type__c == highestPrioBattery){
                                        batteryTemplate.add(batteryDtl.Document_Name__c);
                                        templateMap.put(batteryDtl.Document_Name__c + ':' + c.Id, 'generating');
                                        carrierTemplateFound = true;
                                        break;
                                    }
                                }

                                // if it doesn't exist, use generic battery docs
                                if (!carrierTemplateFound){
                                    if (highestPrioBattery == Constants.BATTERY_UN3091){
                                        batteryTemplate.add(Constants.DOC_ACTION_PRINT_BATTERY_UN3091);
                                        templateMap.put(Constants.DOC_ACTION_PRINT_BATTERY_UN3091 + ':' + c.Id, 'generating');
                                    } else if (highestPrioBattery == Constants.BATTERY_UN3481){
                                        batteryTemplate.add(Constants.DOC_ACTION_PRINT_BATTERY_UN3481);
                                        templateMap.put(Constants.DOC_ACTION_PRINT_BATTERY_UN3481 + ':' + c.Id, 'generating');
                                    } else if (highestPrioBattery == Constants.BATTERY_UN3481_UN3091){
                                        batteryTemplate.add(Constants.DOC_ACTION_PRINT_BATTERY_UN3481_UN3091);
                                        templateMap.put(Constants.DOC_ACTION_PRINT_BATTERY_UN3481_UN3091 + ':' + c.Id, 'generating');
                                    }
                                }

                                BulkifyDocPrintingQueueController batteryJob = new BulkifyDocPrintingQueueController(cseId, batteryTemplate);
                                System.enqueueJob(batteryJob);
                                break;
                            }
                        }
                    }
                }
            }

            // print manifest docs
            List<String> manifestId = new List<String>{recordId};
            BulkifyDocPrintingQueueController manifestJob = new BulkifyDocPrintingQueueController(manifestId, manifestTemplates);
            System.enqueueJob(manifestJob);

            // print case docs
            BulkifyDocPrintingQueueController caseJobs = new BulkifyDocPrintingQueueController(cseIds, caseTemplates);
            System.enqueueJob(caseJobs);

            return templateMap;
        } catch (Exception e) {
            throw new AuraHandledException('Error generating documents: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static void closeManifest(String recordId){
        try{
            Outbound_Manifest__c manifest = new Outbound_Manifest__c(Id = recordId, Status__c = 'Closed');
            Database.SaveResult result = Database.update(manifest, false);
            if (!result.isSuccess()){
                for(Database.Error error : result.getErrors()) {
                    String errorMessage = 'Error updating manifest: ' + error.getStatusCode() + ': ' + error.getMessage();
                    System.debug(errorMessage);
                    throw new DmlException(errorMessage);
                }
            }

            publishRefreshEvent();
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled(cacheable = true)
    public static ManifestErrorWrapper getClosePanelRelatedFields(String recordId) {
        try{
            List<Case> cases = [SELECT Id, Origin_Country__c, Origin_Country__r.Code__c, Origin_Country__r.Name, Origin_Site__r.TSA_Status__c, Destination_Country__c, 
                                        DIMWeightEntryType__c, Manifest_Status__c, AES_ITN__c, Related_Quote_Version__c, Account.QT_Include_Insurance__c, Account.Name,
                                        Insurance_Certificate__c, Related_Quote_Version__r.Include_Insurance__c, Departure_Airport__c, Arrival_Airport__c, Export_Entity__c, 
                                        Import_Entity__c, CaseNumber, To__c, Owner.Alias, Agent_Export_Entity__c, Agent_Export_Entity__r.Client_Account__c, 
                                        Agent_Import_Entity__c, Agent_Import_Entity__r.Client_Account__c
                                    FROM Case WHERE Outbound_Manifest__c =: recordId ];
            List<Shipment_Package_Line__c> spls = [SELECT Id, Box_Count__c, Height__c, Length__c, Num_Pcs__c, Packing_Type__c, Unit_Weight__c, Width__c, Shipment__c
                                                        FROM Shipment_Package_Line__c WHERE Shipment__c IN: cases];
            List<Manifest_Package_Line__c> mpls = [SELECT Id, Height__c, Length__c, Num_Pcs__c, Packing_Type__c, Unit_Weight__c, Width__c, HAWB__c, Box_Count__c
                                                        FROM Manifest_Package_Line__c WHERE Outbound_Manifest__c =: recordId];
            List<Commercial_Invoice_Line__c> cils = [SELECT Id, Part_Number__c, Description__c, Value__c, HS_Code__c, Shipment__c
                                                        FROM Commercial_Invoice_Line__c WHERE Shipment__c IN: cases AND Deleted__c = false];
            Outbound_Manifest__c manifest = [SELECT Id, RecordTypeId, RecordType.Name, 
                                                    Leg_1_Arrival__c, Leg_2_Arrival__c, Leg_3_Arrival__c, Leg_4_Arrival__c, 
                                                    Leg_1_Arrival__r.IATA_Code__c, Leg_2_Arrival__r.IATA_Code__c, Leg_3_Arrival__r.IATA_Code__c, Leg_4_Arrival__r.IATA_Code__c, 
                                                    Leg_1_Departure__c, Leg_2_Departure__c, Leg_3_Departure__c, Leg_4_Departure__c, 
                                                    Leg_1_Departure__r.IATA_Code__c, Leg_2_Departure__r.IATA_Code__c, Leg_3_Departure__r.IATA_Code__c, Leg_4_Departure__r.IATA_Code__c,
                                                    Leg_1_Departure_Date__c, Leg_2_Departure_Date__c, Leg_3_Departure_Date__c, Leg_4_Departure_Date__c,
                                                    Leg_1_Carrier__c, Leg_2_Carrier__c, Leg_3_Carrier__c, Leg_4_Carrier__c, 
                                                    Leg_1_Flight_Number__c, Leg_2_Flight_Number__c, Leg_3_Flight_Number__c, Leg_4_Flight_Number__c,
                                                    Tender_Date__c, Minimum_Rate__c, Freight_Rate__c, Transportation_Reference_Number__c, Transportation_Reference_Number__r.Expiration_Date__c,
                                                    TRN_Override__c, Contents_Description__c, HS_Code_Summary__c, Status__c, 
                                                    Origin_Freight_Agent__c, Origin_Freight_Agent__r.Name, Destination_Freight_Agent__c, Destination_Freight_Agent__r.Name,
                                                    IATA_Battery_Type__c
                                                FROM Outbound_Manifest__c WHERE Id =: recordId];
            
            return new ManifestErrorWrapper(manifest, cases, spls, mpls, cils);
        } catch (Exception e) {
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }
    
    public class ManifestErrorWrapper {
        @AuraEnabled public Outbound_Manifest__c manifest;
        @AuraEnabled public List<Case> cases;
        @AuraEnabled public List<Shipment_Package_Line__c> spls;
        @AuraEnabled public List<Manifest_Package_Line__c> mpls;
        @AuraEnabled public List<Commercial_Invoice_Line__c> cils;
        @AuraEnabled public List<String> errors;
        @AuraEnabled public Map<String, List<String>> caseErrors;

        public ManifestErrorWrapper(Outbound_Manifest__c manifest, List<Case> cases, List<Shipment_Package_Line__c> spls, List<Manifest_Package_Line__c> mpls, List<Commercial_Invoice_Line__c> cils) {
            this.manifest = manifest;
            this.cases = cases;
            this.spls = spls;
            this.mpls = mpls;
            this.cils = cils;
            this.errors = new List<String>();
            this.caseErrors = new Map<String, List<String>>();
            findErrors();
        }

        private void findErrors() {
            for (Case cse : cases){
                // check missing origin country
                if (String.isBlank(cse?.Origin_Country__c)){
                    errors.add(cse.CaseNumber + ': Missing Origin Country');
                    addToCaseErrors(cse.Id, 'Missing Origin Country');
                }

                // check origin site tsa status
                if (String.isNotBlank(cse?.Origin_Site__c) && cse?.Origin_Country__r?.Code__c == 'US' 
                        && (cse?.Origin_Site__r?.TSA_Status__c != 'Known Shipper' || String.isBlank(cse?.Origin_Site__r?.TSA_Status__c))){
                    errors.add(cse.CaseNumber + ': Origin Site Not TSA Known');
                    addToCaseErrors(cse.Id, 'Origin Site Not TSA Known');
                }

                // check missing destination country
                if (String.isBlank(cse?.Destination_Country__c)){
                    errors.add(cse.CaseNumber + ': Missing Destination Country');
                    addToCaseErrors(cse.Id, 'Missing Destination Country');
                }

                // check dimensions and weight
                List<Shipment_Package_Line__c> relatedSpls = new List<Shipment_Package_Line__c>();
                for (Shipment_Package_Line__c spl : spls){
                    if (spl.Shipment__c == cse.Id){
                        relatedSpls.add(spl);
                    }
                }

                Boolean invalidSpl = false;
                for (Shipment_Package_Line__c spl : relatedSpls){
                    if (spl?.Length__c == null || spl?.Length__c == 0 || spl?.Width__c == null || spl?.Width__c == 0 || spl?.Height__c == null || spl?.Height__c == 0 
                            || spl?.Unit_Weight__c == null || spl?.Unit_Weight__c == 0
                            || String.isBlank(spl.Packing_Type__c) || (spl.Packing_Type__c == 'Pallet' && (spl.Box_Count__c == null || spl.Box_Count__c == 0))){
                        invalidSpl = true;
                        break;
                    }
                }

                if (invalidSpl){
                    errors.add(cse.CaseNumber + ': Missing Dimensions/Weight');
                    addToCaseErrors(cse.Id, 'Missing Dimensions/Weight');
                }
                // if departing from jfk or ewr, check dim weight finalized by warehouse
                else if ((manifest?.Leg_1_Departure__r?.IATA_Code__c == 'EWR' || manifest?.Leg_1_Departure__r?.IATA_Code__c == 'JFK') 
                            && cse?.DIMWeightEntryType__c != 'Finalized by Warehouse'){
                    errors.add(cse.CaseNumber + ': Missing Dimensions/Weight');
                    addToCaseErrors(cse.Id, 'Missing Dimensions/Weight');
                }

                // check manifest completion
                if (String.isBlank(cse?.Manifest_Status__c) || cse?.Manifest_Status__c != 'Completed'){
                    errors.add(cse.CaseNumber + ': Warehouse Manifest Incomplete');
                    addToCaseErrors(cse.Id, 'Warehouse Manifest Incomplete');
                }

                // check cils
                List<Commercial_Invoice_Line__c> relatedCils = new List<Commercial_Invoice_Line__c>();
                for (Commercial_Invoice_Line__c cil : cils){
                    if (cil.Shipment__c == cse.Id){
                        relatedCils.add(cil);
                    }
                }

                if (relatedCils.size() == 0){
                    errors.add(cse.CaseNumber + ': Missing Bill of Materials');
                    addToCaseErrors(cse.Id, 'Missing Bill of Materials');
                }
                else {
                    Boolean invalidCil = false;
                    Boolean missingHsCode = false;
                    for (Commercial_Invoice_Line__c cil : relatedCils){
                        if (!(String.isNotBlank(cil.Part_Number__c) || String.isNotBlank(cil.Description__c)) || cil.Value__c == null){
                            invalidCil = true;
                        }
                        if (String.isBlank(cil.HS_Code__c)){
                            missingHsCode = true;
                        }
                        if (invalidCil && missingHsCode){
                            break;
                        }
                    }
                    if (invalidCil){
                        errors.add(cse.CaseNumber + ': Missing Bill of Materials');
                        addToCaseErrors(cse.Id, 'Missing Bill of Materials');
                    }
                    if (missingHsCode){
                        errors.add(cse.CaseNumber + ': Missing HS Code(s)');
                        addToCaseErrors(cse.Id, 'Missing HS Code(s)');
                    }
                }

                // check missing aes filing
                if (String.isBlank(cse?.AES_ITN__c)){
                    errors.add(cse.CaseNumber + ': Missing AES Filing');
                    addToCaseErrors(cse.Id, 'Missing AES Filing');
                }

                // check insurance
                if (cse?.Related_Quote_Version__c != null){
                    if (cse?.Related_Quote_Version__r?.Include_Insurance__c != null && String.isBlank(cse?.Insurance_Certificate__c)){
                        errors.add(cse.CaseNumber + ': Missing Insurance Certificate');
                        addToCaseErrors(cse.Id, 'Missing Insurance Certificate');
                    }
                }
                else if (cse?.Account?.QT_Include_Insurance__c != null && String.isBlank(cse?.Insurance_Certificate__c)){
                    errors.add(cse.CaseNumber + ': Missing Insurance Certificate');
                    addToCaseErrors(cse.Id, 'Missing Insurance Certificate');
                }

                // check airports
                String finalArrivalAirport = '';
                if (manifest?.Leg_4_Arrival__c != null){
                    finalArrivalAirport = manifest?.Leg_4_Arrival__c;
                } else if (manifest?.Leg_3_Arrival__c != null){
                    finalArrivalAirport = manifest?.Leg_3_Arrival__c;
                } else if (manifest?.Leg_2_Arrival__c != null){
                    finalArrivalAirport= manifest?.Leg_2_Arrival__c;
                } else if (manifest?.Leg_1_Arrival__c != null){
                    finalArrivalAirport = manifest?.Leg_1_Arrival__c;
                }

                if ((cse?.Departure_Airport__c != null && manifest?.Leg_1_Departure__c != null && cse?.Departure_Airport__c != manifest?.Leg_1_Departure__c) || 
                        (cse?.Arrival_Airport__c != null && String.isNotBlank(finalArrivalAirport) && cse?.Arrival_Airport__c != finalArrivalAirport)){
                    errors.add(cse.CaseNumber + ': Airport Mismatch');
                    addToCaseErrors(cse.Id, 'Airport Mismatch');
                }

                // check entities
                if (cse?.Export_Entity__c == null){
                    errors.add(cse.CaseNumber + ': Missing Export Entity');
                    addToCaseErrors(cse.Id, 'Missing Export Entity');
                }
                if (cse?.Import_Entity__c == null){
                    errors.add(cse.CaseNumber + ': Missing Import Entity');
                    addToCaseErrors(cse.Id, 'Missing Import Entity');
                }

                // check agent export/import entity
                if (cse?.Agent_Export_Entity__c != null && manifest?.Origin_Freight_Agent__c != null && 
                        cse.Agent_Export_Entity__r.Client_Account__c != manifest?.Origin_Freight_Agent__c){
                    errors.add(cse.CaseNumber + ': Agent Export Entity Mismatch');
                    addToCaseErrors(cse.Id, 'Agent Export Entity Mismatch');
                }
                if (cse?.Agent_Import_Entity__c != null && manifest?.Destination_Freight_Agent__c != null && 
                        cse.Agent_Import_Entity__r.Client_Account__c != manifest?.Destination_Freight_Agent__c){
                    errors.add(cse.CaseNumber + ': Agent Import Entity Mismatch');
                    addToCaseErrors(cse.Id, 'Agent Import Entity Mismatch');
                }
            }

            // if a leg exists, check all of it's fields are filled in. also check each leg's flight date >= prev leg flight date and >= tender date
            Boolean incompleteFlightRouting = false;
            Boolean incorrectFlightDates = false;
            Boolean incorrectTenderDate = false;

            if (manifest?.Leg_4_Departure__c != null){
                if (manifest?.Leg_4_Arrival__c == null || manifest?.Leg_4_Departure_Date__c == null || manifest?.Leg_4_Carrier__c == null
                        || String.isBlank(manifest?.Leg_4_Flight_Number__c)){
                    incompleteFlightRouting = true;
                }
                if (manifest?.Leg_4_Departure_Date__c < manifest?.Leg_3_Departure_Date__c){
                    incorrectFlightDates = true;
                }
                if (manifest?.Leg_4_Departure_Date__c < manifest?.Tender_Date__c){
                    incorrectTenderDate = true;
                }
            }

            if (manifest?.Leg_3_Departure__c != null){
                if (manifest?.Leg_3_Arrival__c == null || manifest?.Leg_3_Departure_Date__c == null || manifest?.Leg_3_Carrier__c == null 
                        || String.isBlank(manifest?.Leg_3_Flight_Number__c)){
                    incompleteFlightRouting = true;
                }
                if (manifest?.Leg_3_Departure_Date__c < manifest?.Leg_2_Departure_Date__c){
                    incorrectFlightDates = true;
                }
                if (manifest?.Leg_3_Departure_Date__c < manifest?.Tender_Date__c){
                    incorrectTenderDate = true;
                }
            }

            if (manifest?.Leg_2_Departure__c != null){
                if (manifest?.Leg_2_Arrival__c == null || manifest?.Leg_2_Departure_Date__c == null || manifest?.Leg_2_Carrier__c == null
                        || String.isBlank(manifest?.Leg_2_Flight_Number__c)){
                    incompleteFlightRouting = true;
                }
                if (manifest?.Leg_2_Departure_Date__c < manifest?.Leg_1_Departure_Date__c){
                    incorrectFlightDates = true;
                }
                if (manifest?.Leg_2_Departure_Date__c < manifest?.Tender_Date__c){
                    incorrectTenderDate = true;
                }
            }

            if (manifest?.Leg_1_Departure__c != null){
                if (manifest?.Leg_1_Arrival__c == null || manifest?.Leg_1_Departure_Date__c == null || manifest?.Leg_1_Carrier__c == null
                        || String.isBlank(manifest?.Leg_1_Flight_Number__c)){
                    incompleteFlightRouting = true;
                }
                if (manifest?.Leg_1_Departure_Date__c < manifest?.Tender_Date__c){
                    incorrectTenderDate = true;
                }
            }

            if (incorrectFlightDates){
                errors.add('Incorrect Flight Date(s)');
            }
            if (incorrectTenderDate){
                errors.add('Incorrect Tender Date');
            }
            if (incompleteFlightRouting){
                errors.add('Incomplete Flight Routing');
            }

            // check freight cost
            if (manifest?.Freight_Rate__c == null || manifest?.Freight_Rate__c == 0){
                errors.add('Incomplete Freight Cost');
            }

            // check mawb exists
            List<Case> outsideUSCases = new List<Case>();
            for (Case cse: cases){
                if (cse?.Origin_Country__r?.Code__c != 'US'){
                    outsideUSCases.add(cse);
                }
            }
            if (outsideUSCases.size() > 0 && String.isBlank(manifest?.TRN_Override__c)){
                errors.add('MAWB# Not Assigned');
            }
            else if (manifest?.Transportation_Reference_Number__c == null){
                errors.add('MAWB# Not Assigned');
            }

            // check mawb expiration
            if (manifest?.Transportation_Reference_Number__r?.Expiration_Date__c != null && manifest?.Transportation_Reference_Number__r?.Expiration_Date__c < Date.today()){
                errors.add('MAWB# Expired');
            }

            // check contents description
            if (String.isBlank(manifest?.Contents_Description__c)){
                errors.add('MAWB Missing Contents Description');
            }

            // check hs codes
            if (String.isBlank(manifest?.HS_Code_Summary__c)){
                errors.add('MAWB Missing HS Codes');
            }

            // check manifest package lines
            if (mpls?.size() > 0){
                Boolean invalidMpl = false;
                Set<String> linkedHawbs = new Set<String>();
                for (Manifest_Package_Line__c mpl : mpls){
                    // check for valid lines
                    if (mpl?.Length__c == null || mpl?.Length__c == 0 || mpl?.Width__c == null || mpl?.Width__c == 0 || mpl?.Height__c == null || mpl?.Height__c == 0 
                            || mpl?.Unit_Weight__c == null || mpl?.Unit_Weight__c == 0 || String.isBlank(mpl?.Packing_Type__c) 
                            || (mpl?.Packing_Type__c == 'Pallet' && (mpl?.Box_Count__c == null || mpl?.Box_Count__c == 0))){
                        invalidMpl = true;
                    }
                    // get list of all linked cases
                    if (mpl?.HAWB__c != null){
                        linkedHawbs.add(mpl.HAWB__c);
                    }
                }

                if (invalidMpl){
                    errors.add('Incomplete Manifest Weight/Dim Data');
                }
                
                // check that all cases are linked to a line
                Boolean allCasesLinked = true;
                for (Case c : cases) {
                    if (!linkedHawbs.contains(c.Id)) {
                        allCasesLinked = false;
                        break;
                    }
                }
                if (!allCasesLinked){
                    errors.add('Unlinked HAWBs');
                }
            }

            // check if battery type doesn't match priority list
            ManifestHandlingWrapper manifestHandling = getSpecialHandling(manifest.Id);
            String batteryPriority = getBatteryTypePriorityCheck(manifestHandling.batteryTypePerShipment);
            if (String.isBlank(manifest.IATA_Battery_Type__c) && String.isNotBlank(batteryPriority)){
                errors.add('Batteries Exist On Linked HAWBs but Not on Manifest');
            }
            else if (manifest.IATA_Battery_Type__c != batteryPriority){
                errors.add('Battery Type Mismatch');
            }
        }

        private void addToCaseErrors(String id, String message){
            if (caseErrors.containsKey(id)){
                caseErrors.get(id).add(message);
            }
            else{
                caseErrors.put(id, new List<String>{message});
            }
        }

        private String getBatteryTypePriorityCheck(Map<String, Map<String, String>> batteryTypePerShipment) {
            if (batteryTypePerShipment == null) {
                return null;
            }

            for (String type : Constants.batteryPriorityList) {
                for (Map<String, String> batteries : batteryTypePerShipment.values()) {
                    if (batteries.containsKey(type)) {
                        if (type == Constants.BATTERY_UN3481 && batteries.containsKey(Constants.BATTERY_UN3091)) {
                            return Constants.BATTERY_UN3481_UN3091;
                        }
                        return type;
                    }
                }
            }

            return null;
        }
    }

    //Consignment Details Tab
    //Retrieves Consignment Info from associated Outbound Manifest cases
    @AuraEnabled
    public static List<Case> getRelatedCases(Id manifestId) {
        return [
            SELECT 
                Id, 
                CaseNumber,
                Origin_Freight_Agent__c, 
                Origin_Freight_Agent__r.Tax_ID__c,
                Origin_Freight_Agent__r.Name,
                Origin_Freight_Agent__r.Consignment_Info__c,
                Destination_Freight_Agent__c,
                Destination_Freight_Agent__r.Tax_ID__c,
                Destination_Freight_Agent__r.Name,
                Destination_Freight_Agent__r.Consignment_Info__c,
                Agent_Export_Entity__c, 
                Agent_Export_Entity__r.Legal_Name__c,
                Agent_Import_Entity__c,
                Agent_Import_Entity__r.Legal_Name__c,
                RS_Output_Exporter_Consignment__c,
                RS_Output_Importer_Consignment__c,
                RS_Output_Exporter_TaxID__c,
                RS_Output_Importer_TaxID__c
            FROM Case
            WHERE Outbound_Manifest__c = :manifestId
        ];
    }

    @AuraEnabled
    public static void publishRefreshEvent() {
        RefreshEvent__e event = new RefreshEvent__e();
        EventBus.publish(event);
    }
}