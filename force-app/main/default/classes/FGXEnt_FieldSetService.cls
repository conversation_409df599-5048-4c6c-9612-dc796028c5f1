/**
 * Created by re<PERSON><PERSON><PERSON> on 7/29/23.
 */

public without sharing class FGXEnt_FieldSetService {
    @TestVisible
    private static FGXEnt_FieldSetService mock;

    public static FGXEnt_FieldSetService createService() {
        if (mock != null) {
            return mock;
        } else {
            return new FGXEnt_FieldSetService();
        }
    }

    public static String getQueryFields(String fieldSetName, String objectName) {
        return getQueryFields(new Set<String>(), getFieldSetMembers(fieldSetName, objectName), false);
    }

    public static List<Schema.FieldSetMember> getFieldSetMembers(String fieldSetName, String objectName) {
        Schema.DescribeSObjectResult describeSObjectResultObj  = ((SObject) Type.forName(objectName).newInstance()).getSObjectType().getDescribe();
        Schema.FieldSet fieldSetObj  = describeSObjectResultObj.FieldSets.getMap().get(fieldSetName);
        return fieldSetObj.getFields();
    }

    public static String getQueryFields(Set<String> baseFields, List<Schema.FieldSetMember> fieldSetMembers, Boolean addTrailingComma) {
        String queryString = '';
        Set<String> allFields = new Set<String>();
        allFields.addAll(baseFields);

        if(fieldSetMembers != null) {
            for(Schema.FieldSetMember fsm : fieldSetMembers) {
                allFields.add(fsm.getFieldPath());

                // add lookup object Name field
                if(!Test.isRunningTest()) {
                    if(fsm.getSObjectField().getDescribe().getReferenceTo().size() > 0) {
                        allFields.add(fsm.getFieldPath().replace('__c', '__r').replace('Id', '') +
                            (fsm.getSObjectField().getDescribe().getReferenceTo()[0] === Case.SObjectType ? '.CaseNumber' :'.Name'));
                    }
                }
            }
        }

        if(!allFields.isEmpty()) {
            queryString = String.join(new List<String>(allFields), ', ');
            queryString += addTrailingComma ? ', ' : ' ';
        }
        return queryString;
    }
}