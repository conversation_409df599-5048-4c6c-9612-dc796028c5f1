/**
 * PlatformEventProcessorDomain
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 10/4/24
 */

public without sharing class PlatformEventProcessorDomain extends Domain {
    public final List<PlatformEventQueue> events;
    private final PlatformEventQueueSettings settings;

    public PlatformEventProcessorDomain(List<FGXPlatformEventProcessor__e> records) {
        super(records);
        this.events = new List<PlatformEventQueue>();
        this.settings = PlatformEventQueueSettings.getSettings();
    }

    /**
     * @description
     */
    public override void doAfterInsert() {
        new PlatformEventProcessorDomain(this.triggerRecords).handle();
    }

    /**
     * @description
     */
    public void handle() {
        Map<PlatformEventQueue.PayloadEventCode, List<PlatformEventQueue>> groupedPayloads;
        Logger logger = Logger.getInstance(true);

        try {
            groupedPayloads = loadQueues();

        } catch (Exception e) {
            logger.log(PlatformEventProcessorDomain.class.toString(), e);
            logger.finalize();
            return;
        }

        try {
            for (PlatformEventQueue queueItem : events) {
                try {
                    queueItem.setPayload();
                } catch (Exception e) {
                    queueItem.addRetryable(e);
                }
            }

            for (PlatformEventQueue.PayloadEventCode eventCode : PROCESSING_ORDER) {
                if (groupedPayloads.containsKey(eventCode)) {
                    List<PlatformEventQueue> queueItems = groupedPayloads.get(eventCode);
                    queueItems.sort();

                    try {
                        PlatformEventQueueHandler payloadHandler = getHandlerForEvent(eventCode);
                        payloadHandler.processPayloads(queueItems);
                    } catch (HandlerDisabledOrNotDefinedException e) {
                        for (PlatformEventQueue tpe : queueItems) {
                            tpe.setSkipped('Handler Disabled');
                        }
                    }
                }
            }
        } catch (Exception ex) {
            for (PlatformEventQueue pev : this.events) {
                pev.addRetryable(ex);
            }
        } finally {
            List<FGXPlatformEventQueue__c> queues = getQueues();

            Database.DMLOptions dmlOptions = new Database.DMLOptions();
            dmlOptions.optAllOrNone = false;
            dmlOptions.allowFieldTruncation = true;

            logger.log(PlatformEventProcessorDomain.class.toString(), 'FGXPlatformEventQueue__c',  Database.update(queues, dmlOptions));
            logger.finalize();
        }
    }

    @TestVisible
    private Map<PlatformEventQueue.PayloadEventCode, List<PlatformEventQueue>> loadQueues() {
        Map<Id, FGXPlatformEventQueue__c> queues = new Map<Id, FGXPlatformEventQueue__c>();
        for (FGXPlatformEventProcessor__e pev : (List<FGXPlatformEventProcessor__e>) this.triggerRecords) {
            if (!String.isBlank(pev.EventQueueId__c)) {
                queues.put(pev.EventQueueId__c, null);
            }
        }
        queues = new Map<Id, FGXPlatformEventQueue__c>([
                SELECT Id,
                        Payload__c,
                        CreatedById,
                        Status__c,
                        OccurredAt__c,
                        PayloadEventCode__c,
                        RetryCount__c,
                        LastTried__c,
                        EventBusMessageUUIDPublished__c,
                        EventBusMessageUUIDProcessed__c,
                        Account__r.Id,
                        Account__r.Name,
                        Account__r.Platform_Visibility_Date__c,
                        Requestor__r.Id,
                        Requestor__r.Name
                FROM FGXPlatformEventQueue__c
                WHERE Id IN :queues.keySet()
        ]);

        Map<PlatformEventQueue.PayloadEventCode, List<PlatformEventQueue>> eventsByCodes = new Map<PlatformEventQueue.PayloadEventCode, List<PlatformEventQueue>>();
        Set<Id> queuesToProcess = new Set<Id>();
        Map<Id, FGXPlatformEventQueue__c> executingQueues = new Map<Id, FGXPlatformEventQueue__c>();
        String lastReplayId;

        for (FGXPlatformEventProcessor__e pev : (List<FGXPlatformEventProcessor__e>) this.triggerRecords) {
            if (this.events.size() >= settings.maxBatchSize) {
                break;
            }

            Id queueId = (Id) pev.EventQueueId__c;
            if (queueId != null && queues.containsKey(queueId) && !queuesToProcess.contains(queueId)) {
                FGXPlatformEventQueue__c queueRecord = queues.get(queueId);

                if(!STATUSES_TO_PROCESS.contains(queueRecord.Status__c)) {
                    continue; // don't send processed events back to the handler
                }
                queuesToProcess.add(queueId);

                PlatformEventQueue event = new PlatformEventQueue(pev, queueRecord);
                this.events.add(event);

                executingQueues.put(queueId,
                    new FGXPlatformEventQueue__c(
                        Id = queueId,
                        Status__c = PlatformEventQueue.EventStatus.Executing.name(),
                        LastTried__c = Datetime.now()
                    )
                );
                lastReplayId = pev.ReplayId;
                if (event.status.ordinal() > 2) {
                    // Anything other than Unprocessed or Processing should not be processed
                    continue;
                }

                if (!eventsByCodes.containsKey(event.eventCode)) {
                    eventsByCodes.put(event.eventCode, new List<PlatformEventQueue>());
                }
                eventsByCodes.get(event.eventCode).add(event);
            }
        }

        PlatformEventQueueDomain.allowExecution_beforeUpdate = false;
        PlatformEventQueueDomain.allowExecution_afterUpdate = false;
        update executingQueues.values();
        PlatformEventQueueDomain.allowExecution_beforeUpdate = true;
        PlatformEventQueueDomain.allowExecution_afterUpdate = true;

        if (!String.isBlank(lastReplayId)) {
            try {
                EventBus.TriggerContext.currentContext().setResumeCheckpoint(lastReplayId);
            } catch (Exception e) {
                System.debug(e);
            }
        }

        return eventsByCodes;
    }


    @TestVisible
    private List<FGXPlatformEventQueue__c> getQueues() {
        List<FGXPlatformEventQueue__c> queues = new List<FGXPlatformEventQueue__c>();
        for (PlatformEventQueue peq : this.events) {
            queues.add(peq.getQueueRecord());
        }
        return queues;
    }

    @TestVisible
    private PlatformEventQueueHandler getHandlerForEvent(PlatformEventQueue.PayloadEventCode eventCodeType) {
        String eventCode = eventCodeType.name();

        if (String.isBlank(eventCode)) {
            throw new PlatformEventQueueServiceException('Empty Event Code');
        }

        PlatformEventQueueSettings.HandlerWrapper theHandlerWrapper = settings.handlerMappings.get(eventCode);
        if (theHandlerWrapper == null) {
            throw new HandlerDisabledOrNotDefinedException('Handler Mapping Not Defined');
        }

        if (theHandlerWrapper?.isEnabled == false) {
            throw new HandlerDisabledOrNotDefinedException('Handler Disabled');
        }

        String className = theHandlerWrapper?.handlerClassName;

        if (String.isBlank(className)) {
            throw new PlatformEventQueueServiceException(String.format('No class name provided for event name "{0}", is the setting complete?', new List<String>{
                eventCode
            }));
        }

        Type theClassType = Type.forName(className);

        if (theClassType == null) {
            throw new PlatformEventQueueServiceException(String.format('No apex class with name "{0}" could be found for event "{1}".', new List<String>{
                className,
                eventCode
            }));
        }

        return (PlatformEventQueueHandler) theClassType.newInstance();
    }

    private static final List<String> STATUSES_TO_PROCESS = new List<String> {
        PlatformEventQueue.EventStatus.Unprocessed.name(),
        PlatformEventQueue.EventStatus.Processing.name(),
        PlatformEventQueue.EventStatus.Executing.name()
    };

    public static final List<PlatformEventQueue.PayloadEventCode> PROCESSING_ORDER = new List<PlatformEventQueue.PayloadEventCode>{
            PlatformEventQueue.PayloadEventCode.execute_report,
            PlatformEventQueue.PayloadEventCode.retrieve_reports
    };

    @TestVisible
    private class PlatformEventQueueServiceException extends Exception {}

    @TestVisible
    private class HandlerDisabledOrNotDefinedException extends Exception {}
}