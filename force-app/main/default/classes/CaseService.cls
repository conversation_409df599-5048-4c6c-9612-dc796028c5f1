/**
 * Created by tdr<PERSON> on 6/25/25.
 */

public with sharing class CaseService {
    public static void setInsertDeliveryDate(List<Case> newCases) {

        Case[] caseList = new List<Case>();

        for(Case c : newCases){
            if(c.Status == Constants.CASE_STATUS_BOOKED){
                caseList.add(c);
            }
        }

        if(!caseList.isEmpty()){
            runDateProjection(caseList, 3);
        }
    }

    public static void setUpdateDeliveryDate(Map<Id, Case> newCases, Map<Id, Case> oldCases) {

        Case[] caseList = new List<Case>();

        for(Id i : newCases.keySet()){
            Case newCase = newCases.get(i);
            Case oldCase = oldCases != null ? oldCases.get(i) : null;

            if (newCase.Status != oldCase.Status) {
                caseList.add(newCase);
            }
        }

        if(!caseList.isEmpty()){
            runDateProjection(caseList, 0);
        }
    }

    public static void runDateProjection(List<Case> cases, Integer defaultDays){

        Set<String> countries = new Set<String>();

        for(Case c: cases){
            countries.add(c.Destination_Country__c);
        }

        for(AggregateResult a : [SELECT Country__c, MAX(Days_Max__c)maxVal FROM Timeline__c WHERE Country__c IN :countries GROUP BY Country__c]){
            for(Case c : cases){
                Date resultDate = Date.today();
                Integer addedDays = 0;
                while (addedDays < Integer.valueOf(a.get('maxVal'))) {
                    resultDate = resultDate.addDays(1);
                    Integer dayOfWeek = resultDate.toStartOfWeek().daysBetween(resultDate) + 1;
                    if (dayOfWeek >= 2 && dayOfWeek <= 6) {
                        addedDays++;
                    }
                }
                c.Projected_Delivery_Date__c = Date.today().addDays(addedDays + defaultDays);
            }
        }
    }
}