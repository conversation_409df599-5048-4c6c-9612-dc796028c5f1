 /**
 * FGXEnt_GeneralConfig
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 4/12/24
 */

public with sharing class FGXEnt_GeneralConfig {
    private static FGXEnt_General_Config__mdt config;

    public static String EOR_ENTITY_NAME {
        get {
            return getConfig().FGX_EOR_Entity_Name__c;
        }
    }

    public static String IOR_ENTITY_NAME {
        get {
            return getConfig().FGX_IOR_Entity_Name__c;
        }
    }

    public static String IDK_ENTITY_NAME {
        get {
            return getConfig().FGX_IDK_Entity_Name__c;
        }
    }

    public static String MAGIC_LINK_CLIENT_ID {
        get {
            return getConfig().Magic_Link_Client_Id__c;
        }
    }

    public static String MAGIC_LINK_CERT_API_NAME {
        get {
            return getConfig().Magic_Link_Cert_Api_Name__c;
        }
    }

    public static Boolean REPORT_RETRIEVAL_CACHE_ENABLED {
        get {
            return getConfig().Report_Retrieval_Cache_Enabled__c;
        }
    }

    public static Decimal REPORT_RETRIEVAL_CACHE_THRESHOLD {
        get {
            return getConfig().Report_Retrieval_Cache_Threshold__c;
        }
    }

    public static Date PLATFORM_WIDE_VISIBILITY_DATE {
        get {
            Date configDate = getConfig().Platform_Wide_Visibility_Date__c;
            // Default to July 31, 2024 if not set in config
            return configDate != null ? configDate : Date.newInstance(2024, 7, 31);
        }
    }

    /**
     * @description Singleton helper function to
     * get the config record needed for the transaction
     * record
     */
    private static FGXEnt_General_Config__mdt getConfig() {
        if (config == null) {
            List<FGXEnt_General_Config__mdt> existingConfig = [
                SELECT Id,
                    FGX_EOR_Entity_Name__c,
                    FGX_IOR_Entity_Name__c,
                    FGX_IDK_Entity_Name__c,
                    Magic_Link_Client_Id__c,
                    Magic_Link_Cert_Api_Name__c,
                    Report_Retrieval_Cache_Enabled__c,
                    Report_Retrieval_Cache_Threshold__c,
                    Platform_Wide_Visibility_Date__c
                FROM FGXEnt_General_Config__mdt
            ];

            if(!existingConfig.isEmpty()) {
                config = existingConfig.get(0);
            } else {
                config = new FGXEnt_General_Config__mdt(
                    FGX_EOR_Entity_Name__c = 'FGX EOR Service',
                    FGX_IOR_Entity_Name__c = 'FGX IOR Service',
                    FGX_IDK_Entity_Name__c = 'Customer Doesn\'t Know',
                    Magic_Link_Client_Id__c = 'TEST_CLIENT_ID',
                    Magic_Link_Cert_Api_Name__c = 'FGX_Enterprise_Magic_Link',
                    Report_Retrieval_Cache_Enabled__c = true,
                    Report_Retrieval_Cache_Threshold__c = 1,
                    Platform_Wide_Visibility_Date__c = Date.newInstance(2024, 7, 31)
                );
            }
        }
        return config;
    }
}