/**
 * Created by re<PERSON><PERSON><PERSON> on 8/31/22.
 */

@IsTest
public class AddAssetsControllerTest {

    @IsTest
    private static void testGetAssetTypes() {
        Account acct = (Account) TestDataFactory.createSObject('Account',new Map<String,Object>(),true);
        Asset_Type__c assetType = (Asset_Type__c) TestDataFactory.createSObject(
            'Asset_Type__c',
            new Map<String,Object> {
                'Product_Type__c' => 'Switch',
                'Location__c' => 'FGX USA',
                'Project_Reference__c' => 'India Phase 3',
                'Manufacturer__c' => 'NapaTech',
                'Part_Number__c' => '825-0007-01-10',
                'Description__c' => 'Napatech NT100A01 SmartNIC Network Access Interface Cards with Dongles',
                'Account__c' => acct.Id,
                'RecordTypeId' => Constants.ASSET_TYPE_SERIALIZED_RT
            },
            true
        );

        InventoryManagerMsg.AssetTableSearch assetTableSearch = new InventoryManagerMsg.AssetTableSearch();
        assetTableSearch.searchField = 'Part_Number__c';
        assetTableSearch.searchValue = '825-0';
        assetTableSearch.account = acct.Id;

        Test.startTest();
        InventoryManagerMsg.AssetTableData results = AddAssetsController.getAssetTypes(JSON.serialize(assetTableSearch));
        Test.stopTest();

        System.assertEquals(1, results.assetTypes.size());
    }

}