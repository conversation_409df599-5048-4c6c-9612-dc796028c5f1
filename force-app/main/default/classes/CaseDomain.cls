/**
 * Created by tdr<PERSON> on 6/25/25.
 */

public with sharing class CaseD<PERSON><PERSON> extends Domain {

    public CaseDomain(List<Case> records) {
        super(records);
    }

    public override void doBeforeInsert(){
        CaseService.setInsertDeliveryDate((List<Case>)this.triggerRecords);
    }

    public override void doBeforeUpdate(Map<Id,SObject> oldRecordsMap){
        Map<Id, Case> newRecords =  new Map<Id, Case>((List<Case>)this.triggerRecords);
        Map<Id, Case> oldRecords = (Map<Id, Case>) oldRecordsMap;
        CaseService.setUpdateDeliveryDate(newRecords, oldRecords);
    }
}