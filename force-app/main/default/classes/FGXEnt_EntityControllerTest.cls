/**
 * FGXEnt_EntityControllerTest
 * @description: Test class for both the
 * FGXEnt_EntityService and FGXEnt_EntityController
 * @author: <PERSON><PERSON>
 * @date: 10/1/23
 */

@IsTest
public with sharing class FGXEnt_EntityControllerTest {

    @TestSetup static void setup() {
        User testUser = TestFactory.createCommunityUser();
        insert testUser;

        Countries__c unitedStates = (Countries__c) TestDataFactory.createSObject('Countries__c', new Map<String, Object>{
                'Name' => 'United States',
                'Code__c' => 'US',
                'Flag_Icon__c' => 'united_states.svg'
        }, true);

        Countries__c unitedKingdom = (Countries__c) TestDataFactory.createSObject('Countries__c', new Map<String, Object>{
                'Name' => 'United Kingdom',
                'Code__c' => 'UK',
                'VAT_Reclaimable__c' => true,
                'Import_Tax__c' => 20
        }, true);
    }

    @IsTest
    private static void testGetEntitiesWithFilter() {

        FGXEnt_Msgs.EntityContainers result;
        FGXEnt_Msgs.EntityContainers externalTypeResult;

        User testUser = [SELECT Id, AccountId FROM User WHERE Contact.Email LIKE '%@sfdc%' LIMIT 1];
        Countries__c unitedStates = [SELECT Id FROM Countries__c WHERE Code__c = 'US'];

        Entity__c approvedEntity = (Entity__c) TestDataFactory.createSObject(
            'Entity__c',
            new Map<String, Object> {
                'Client_Account__c' => testUser.AccountId,
                'Country__c' => unitedStates.Id,
                'Flow_Status__c' => 'Approved',
                'RecordTypeId' => Constants.ENTITIY_EXTERNAL_RT,
                'CSL_Review_Sign_Off__c' => 'Test',
                'TSA_ID__c' => '1234567',
                'TSA_Status__c' => 'Known Shipper'
            },
            true
        );

        Entity__c inDraftEntity = (Entity__c) TestDataFactory.createSObject(
            'Entity__c',
            new Map<String, Object> {
                'Client_Account__c' => testUser.AccountId,
                'Country__c' => unitedStates.Id,
                'Flow_Status__c' => 'In Draft'
            },
            true
        );

        System.runAs(testUser) {
            Test.startTest();
            result = FGXEnt_EntityController.getEntities('United States');
            externalTypeResult = FGXEnt_EntityController.getEntitiesByType('external', 'United');
            Test.stopTest();
        }

        System.assertEquals(true, result.containerMap.containsKey('United States'));
        System.assertEquals(1, result.containers.size());
        System.assertEquals(1, result.containers.get(0).items.size());

        System.assertEquals(1, externalTypeResult.containers.size());
        System.assertEquals('United States (1)', result.containers.get(0).label);
    }

    @IsTest
    private static void testGetEntitiesWithoutFilter() {
        FGXEnt_Msgs.EntityContainers result;
        FGXEnt_Msgs.EntityContainers internalTypeResult;
        FGXEnt_Msgs.Entity individualEntity;

        User testUser = [SELECT Id, AccountId FROM User WHERE Contact.Email LIKE '%@sfdc%' LIMIT 1];
        Countries__c unitedStates = [SELECT Id FROM Countries__c WHERE Code__c = 'US'];
        Countries__c unitedKingdom = [SELECT Id FROM Countries__c WHERE Code__c = 'UK'];

        Entity__c approvedEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'Approved',
                        'RecordTypeId' => Constants.ENTITIY_INTERNAL_RT,
                        'CSL_Review_Sign_Off__c' => 'Test',
                        'TSA_ID__c' => '1234567',
                        'TSA_Status__c' => 'Known Shipper'
                },
                true
        );

        Entity__c pendingReviewEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'Pending Review',
                        'RecordTypeId' => Constants.ENTITIY_INTERNAL_RT
                },
                true
        );

        Entity__c exceptionEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedKingdom.Id,
                        'Flow_Status__c' => 'Exception',
                        'RecordTypeId' => Constants.ENTITIY_EXTERNAL_RT,
                        'Import_Status__c' => 'Exception',
                        'Export_Status__c' => 'Approved',
                        'Legal_Name__c' => 'FGX UK',
                        'Customs_Code__c' => '1234',
                        'Has_Primary_Point_Of_Contact__c' => true,
                        'Entity_Contact_Name__c' => 'John Doe',
                        'Entity_Contact_Email__c' => '<EMAIL>',
                        'CSL_Review_Sign_Off__c' => 'Test'
                },
                true
        );


        Entity__c inDraftEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'In Draft',
                        'RecordTypeId' => Constants.ENTITIY_EXTERNAL_RT
                },
                true
        );

        System.runAs(testUser) {
            Test.startTest();
            result = FGXEnt_EntityController.getEntities(null);
            internalTypeResult = FGXEnt_EntityController.getEntitiesByType('internal', '');
            individualEntity = FGXEnt_EntityController.getEntity(exceptionEntity.Id);
            Test.stopTest();
        }

        System.assertEquals(true, result.containerMap.containsKey('United States'));
        System.assertEquals(true, result.containerMap.containsKey('United Kingdom'));
        System.assertEquals(2, result.containers.size());
        System.assertEquals(2, result.containerMap.get('United States').items.size());
        System.assertEquals(1, result.containerMap.get('United Kingdom').items.size());

        System.assertEquals(2, internalTypeResult.containerMap.get('United States').items.size());
        System.assertEquals('Exception', individualEntity.entityStatus);
        System.assertEquals('Exception', individualEntity.entityImportStatus);
        System.assertEquals('Approved', individualEntity.entityExportStatus);
        System.assertEquals('FGX UK', individualEntity.entityLegalName);
        System.assertEquals('1234', individualEntity.entityCustomsCode);
        System.assertEquals(true, individualEntity.entityHasPoc);
        System.assertEquals('John Doe', individualEntity.entityContactName);
        System.assertEquals('<EMAIL>', individualEntity.entityContactEmail);
    }

    @IsTest
    private static void testGetDraftEntities() {
        List<FGXEnt_Msgs.Entity> results;

        User testUser = [SELECT Id, AccountId FROM User WHERE Contact.Email LIKE '%@sfdc%' LIMIT 1];
        Countries__c unitedStates = [SELECT Id, Name, Code__c, Flag_Icon__c FROM Countries__c WHERE Code__c = 'US'];
        Countries__c unitedKingdom = [SELECT Id FROM Countries__c WHERE Code__c = 'UK'];

        Entity__c approvedEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'Approved',
                        'CSL_Review_Sign_Off__c' => 'SR TEST',
                        'State_US__c' => 'New York',
                        'TSA_ID__c' => '1234567',
                        'TSA_Status__c' => 'Known Shipper'
                },
                true
        );

        Entity__c pendingReviewEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'Pending Review',
                        'State_US__c' => 'New York'
                },
                true
        );

        Entity__c exceptionEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedKingdom.Id,
                        'Flow_Status__c' => 'Exception'
                },
                true
        );

        Entity__c inDraftEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Entity_Code__c' => 'TEST',
                        'Entity_Name__c' => 'Entity Name',
                        'Entity_Type__c' => 'Entity Type',
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Tax_ID__c' => 'Tax Id',
                        'Flow_Status__c' => 'In Draft',
                        'Address_Line_1__c' => '123 Test Street',
                        'Address_Line_2__c' => 'APT 100',
                        'Address_Line_3__c' => 'Test',
                        'City__c' => 'Phoenix',
                        'State_US__c' => 'Arizona',
                        'Post_Code__c' => '85923',
                        'Entity_Notes__c' => 'Notes',
                        'State_US__c' => 'New York'
                },
                true
        );

        System.runAs(testUser) {
            Test.startTest();
            results = FGXEnt_EntityController.getDraftEntities();
            Test.stopTest();
        }

        inDraftEntity = [SELECT Id, Name, Entity_Name__c, Entity_Code__c, Entity_Type__c, Flow_Status__c, Tax_ID__c, Entity_Notes__c FROM Entity__c WHERE Id =: inDraftEntity.Id LIMIT 1];

        System.assertEquals(false, results.isEmpty());
        System.assertEquals(1, results.size());
        System.assertEquals(inDraftEntity.Id, results.get(0).entityId);
        System.assertEquals(inDraftEntity.Name, results.get(0).entityNameAutonum);
        System.assertEquals(inDraftEntity.Entity_Name__c, results.get(0).entityName);
        System.assertEquals(unitedStates.Name, results.get(0).entityCountry);
        System.assertEquals(unitedStates.Code__c, results.get(0).entityCountryCode);
        System.assertEquals(inDraftEntity.Entity_Code__c, results.get(0).entityCode);
        System.assertEquals(inDraftEntity.Entity_Type__c, results.get(0).entityType);
        System.assertEquals(inDraftEntity.Flow_Status__c, results.get(0).entityStatus);
        System.assertEquals(unitedStates.Flag_Icon__c, results.get(0).entityCountryImg);
        System.assertEquals(inDraftEntity.Tax_ID__c, results.get(0).entityTaxId);
        System.assertEquals('123 Test Street, APT 100, Test, Phoenix', results.get(0).entityAddressCombined);
        System.assertEquals(inDraftEntity.Entity_Notes__c, results.get(0).entityNotes);
        System.assertEquals(null, results.get(0).history);
    }


    @IsTest
    private static void testArchiveEntity() {

        FGXEnt_Msgs.EntityArchiveResult result;
        User testUser = [SELECT Id, AccountId FROM User WHERE Contact.Email LIKE '%@sfdc%' LIMIT 1];
        Countries__c unitedStates = [SELECT Id FROM Countries__c WHERE Code__c = 'US'];

        Entity__c inDraftEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'In Draft',
                        'State_US__c' => 'New York'
                },
                true
        );

        System.runAs(testUser) {
            Test.startTest();
            result = FGXEnt_EntityController.archiveEntity(inDraftEntity.Id);
            Test.stopTest();
        }

        System.assertEquals(true, result.success);

        inDraftEntity = [SELECT Id, Archived__c FROM Entity__c];
        System.assertEquals(true, inDraftEntity.Archived__c);
    }

    @IsTest
    private static void testImportExportStatusMethods() {
        User testUser = [SELECT Id, AccountId FROM User WHERE Contact.Email LIKE '%@sfdc%' LIMIT 1];
        Countries__c unitedStates = [SELECT Id FROM Countries__c WHERE Code__c = 'US'];
        Countries__c unitedKingdom = [SELECT Id FROM Countries__c WHERE Code__c = 'UK'];
        Account acc = (Account)TestDataFactory.createSObject('Account', new Map<String, Object>(), true);
        Contact testContact = TestFactory.createContact('John', 'Smith', '<EMAIL>', acc.Id);
        insert testContact;

        // two sets of cases. one to test last 2 months check and another to test last 2 years check
        Case cse = TestFactory.getCase(acc, testContact, unitedStates, Constants.CASE_STATUS_BOOKED, Constants.CASE_IT_SHIPMENT_RT, true);
        Case cseOneMonthOld = TestFactory.getCase(acc, testContact, unitedStates, Constants.CASE_STATUS_BOOKED, Constants.CASE_IT_SHIPMENT_RT, true);

        Case cseOneYearOld = TestFactory.getCase(acc, testContact, unitedStates, Constants.CASE_STATUS_BOOKED, Constants.CASE_IT_SHIPMENT_RT, true);
        Case cseTwoYearOld = TestFactory.getCase(acc, testContact, unitedStates, Constants.CASE_STATUS_BOOKED, Constants.CASE_IT_SHIPMENT_RT, true);

        // two sets of entities. one to test last 2 months check and another to test last 2 years check
        Entity__c exportEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'Approved',
                        'RecordTypeId' => Constants.ENTITIY_INTERNAL_RT,
                        'CSL_Review_Sign_Off__c' => 'Test',
                        'State_US__c' => 'New York',
                        'TSA_ID__c' => '1234567',
                        'TSA_Status__c' => 'Known Shipper'
                },
                true
        );

        Entity__c importEntity = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedKingdom.Id,
                        'Flow_Status__c' => 'Approved',
                        'RecordTypeId' => Constants.ENTITIY_INTERNAL_RT,
                        'CSL_Review_Sign_Off__c' => 'Test'
                },
                true
        );

        Entity__c exportEntity2 = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedStates.Id,
                        'Flow_Status__c' => 'Approved',
                        'RecordTypeId' => Constants.ENTITIY_INTERNAL_RT,
                        'State_US__c' => 'New York',
                        'CSL_Review_Sign_Off__c' => 'Test',
                        'TSA_ID__c' => '1234567',
                        'TSA_Status__c' => 'Known Shipper'
                },
                true
        );

        Entity__c importEntity2 = (Entity__c) TestDataFactory.createSObject(
                'Entity__c',
                new Map<String, Object> {
                        'Client_Account__c' => testUser.AccountId,
                        'Country__c' => unitedKingdom.Id,
                        'Flow_Status__c' => 'Approved',
                        'RecordTypeId' => Constants.ENTITIY_INTERNAL_RT,
                        'CSL_Review_Sign_Off__c' => 'Test'
                },
                true
        );

        Date currentDate = Date.today();
        Date oneMonthOld = currentDate.addDays(-31);
        Date oneYearOld = currentDate.addDays(-364);
        Date twoYearOld = currentDate.addDays(-729);

        cse.Import_Entity__c = importEntity.Id;
        cse.Export_Entity__c = exportEntity.Id;
        cse.Shipment_Value__c = 100;
        Test.setCreatedDate(cse.Id, currentDate);
        update cse;

        cseOneMonthOld.Import_Entity__c = importEntity.Id;
        cseOneMonthOld.Export_Entity__c = exportEntity.Id;
        cseOneMonthOld.Shipment_Value__c = 20;
        Test.setCreatedDate(cseOneMonthOld.Id, oneMonthOld);
        update cseOneMonthOld;

        cseOneYearOld.Import_Entity__c = importEntity2.Id;
        cseOneYearOld.Export_Entity__c = exportEntity2.Id;
        cseOneYearOld.Shipment_Value__c = 100;
        Test.setCreatedDate(cseOneYearOld.Id, oneYearOld);
        update cseOneYearOld;

        cseTwoYearOld.Import_Entity__c = importEntity2.Id;
        cseTwoYearOld.Export_Entity__c = exportEntity2.Id;
        cseTwoYearOld.Shipment_Value__c = 20;
        Test.setCreatedDate(cseTwoYearOld.Id, twoYearOld);
        update cseTwoYearOld;

        Boolean importUpdateResult;
        Boolean exportUpdateResult;
        Map<String, Integer> importResults;
        Map<String, Integer> exportResults;
        Map<String, Decimal> importSavings;
        Map<String, Decimal> exportSavings;
        Map<String, Decimal> reclaimableVAT;
        Map<String, Integer> importResultsYearOld;
        Map<String, Integer> exportResultsYearOld;
        Map<String, Decimal> importSavingsYearOld;
        Map<String, Decimal> exportSavingsYearOld;
        Map<String, Decimal> reclaimableVATYearOld;
        Entity__c internalEOREnt;
        Entity__c internalIOREnt;
        Entity__c internalIDKEnt;
        System.runAs(testUser) {
            Test.startTest();
            importUpdateResult = FGXEnt_EntityController.updateImportExportStatus(importEntity.Id, 'importer', 'Approved');
            exportUpdateResult = FGXEnt_EntityController.updateImportExportStatus(exportEntity.Id, 'exporter', 'Approved');
            importResults = FGXEnt_EntityController.getCasesAsImporter(importEntity.Id);
            exportResults = FGXEnt_EntityController.getCasesAsExporter(exportEntity.Id);
            importSavings = FGXEnt_EntityController.savingsFromCasesAsImporter(importEntity.Id);
            exportSavings = FGXEnt_EntityController.savingsFromCasesAsExporter(exportEntity.Id);
            reclaimableVAT = FGXEnt_EntityController.reclaimableVAT(importEntity.Id);
            importResultsYearOld = FGXEnt_EntityController.getCasesAsImporter(importEntity2.Id);
            exportResultsYearOld = FGXEnt_EntityController.getCasesAsExporter(exportEntity2.Id);
            importSavingsYearOld = FGXEnt_EntityController.savingsFromCasesAsImporter(importEntity2.Id);
            exportSavingsYearOld = FGXEnt_EntityController.savingsFromCasesAsExporter(exportEntity2.Id);
            reclaimableVATYearOld = FGXEnt_EntityController.reclaimableVAT(importEntity2.Id);
            internalEOREnt = FGXEnt_EntityService.getInternalEOREntity();
            internalIOREnt = FGXEnt_EntityService.getInternalIOREntity();
            internalIDKEnt = FGXEnt_EntityService.getInternalIDKEntity();
            Test.stopTest();
        }

        System.assertEquals(true, importUpdateResult);
        System.assertEquals(true, exportUpdateResult);
        System.assertEquals(1, importResults.get('1 month'));
        System.assertEquals(1, exportResults.get('1 month'));
        System.assertEquals(4, importSavings.get('1 month'));
        System.assertEquals(4, exportSavings.get('1 month'));
        System.assertEquals(20, reclaimableVAT.get('1 month'));
        System.assertEquals(1, importResultsYearOld.get('1 year'));
        System.assertEquals(1, exportResultsYearOld.get('1 year'));
        System.assertEquals(4, importSavingsYearOld.get('1 year'));
        System.assertEquals(4, exportSavingsYearOld.get('1 year'));
        System.assertEquals(20, reclaimableVATYearOld.get('1 year'));
        System.assertEquals(null, internalEOREnt);
        System.assertEquals(null, internalIOREnt);
        System.assertEquals(null, internalIDKEnt);
    }
}