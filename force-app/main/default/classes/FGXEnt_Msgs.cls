/**
 * FGXEnt_Msgs
 * @description: Message class to centralize
 * all FE/BE communication with LWC components
 * @author: <PERSON><PERSON>
 * @date: 06/17/23
 */

public with sharing class FGXEnt_Msgs {

    public class UserTableList {
        @AuraEnabled
        public List<UserTableItem> contacts;

        public UserTableList(List<Contact> contacts) {
            this.contacts = new List<UserTableItem>();

            for (Contact c : contacts) {
                this.contacts.add(
                        new UserTableItem(c)
                );
            }
            this.contacts.sort();
        }
    }

    public class UserTableItem implements Comparable {
        @AuraEnabled
        public Boolean isSelected;

        @AuraEnabled
        public String contactId {
            get {
                String value;
                if (this.contact != null) {
                    value = this.contact.Id;
                }
                return value;
            }
        }

        @AuraEnabled
        public Boolean hasUser {
            get {
                return this.user != null;
            }
        }

        @AuraEnabled
        public Boolean hasLoggedIn {
            get {
                return hasUser ? this.user.LastLoginDate != null : false;
            }
        }

        @AuraEnabled
        public Boolean isActive {
            get {
                return hasUser ? this.user.IsActive : false;
            }
        }

        @AuraEnabled
        public Boolean userColumnShowBlankAvatar {
            get {
                return hasUser ? String.isBlank(userColumnAvatarURL) : true;
            }
        }

        @AuraEnabled
        public String nameColumn {
            get {
                return userColumnFirstName + userColumnLastName;
            }
        }

        @AuraEnabled
        public String userColumnFirstName {
            get {
                return hasUser ? this.user.FirstName : this.contact.FirstName;
            }
        }

        @AuraEnabled
        public String userColumnLastName {
            get {
                return hasUser ? this.user.LastName : this.contact.LastName;
            }
        }

        @AuraEnabled
        public String userColumnEmail {
            get {
                return hasUser ? this.user.Email : this.contact.Email;
            }
        }

        @AuraEnabled
        public String userColumnAvatarURL {
            get {
                return hasUser ? (!this.user.FullPhotoUrl.endsWith(FGXEnt_Constants.DEFAULT_PROFILE_URL_SUFFIX) ? this.user.FullPhotoUrl : null) : null;
            }
        }

        @AuraEnabled
        public String accessLevelColumn {
            get {
                String value = '--';

                if (hasUser) {
                    value = String.isNotBlank(user.FGXEnt_Product_Access_Level__c) ? user.FGXEnt_Product_Access_Level__c : '--';
                }
                return value;
            }
        }

        @AuraEnabled
        public String approvalSettingColumn {
            get {
                String value = '--';

                if (hasUser) {
                    value = String.isNotBlank(user.FGXEnt_Approval_Settings__c) ? user.FGXEnt_Approval_Settings__c : '--';
                }
                return value;
            }
        }

        @AuraEnabled
        public Boolean showInviteButton {
            get {
                return !hasUser && String.isNotBlank(this.userColumnEmail);
            }
        }

        @AuraEnabled
        public String statusColumn {
            get {
                String value;

                if (!hasUser) {
                    if (String.isBlank(this.userColumnEmail)) {
                        value = 'Specify Email to Invite';
                    } else {
                        value = FGXEnt_Constants.USER_STATUS_INVITE;
                    }
                } else {
                    if (this.user.IsActive && this.user.LastLoginDate == null) {
                        value = FGXEnt_Constants.USER_STATUS_INVITE_PENDING;
                    } else if (this.user.IsActive && this.user.LastLoginDate != null) {
                        value = FGXEnt_Constants.USER_STATUS_ACTIVE;
                    } else if (!this.user.IsActive) {
                        value = FGXEnt_Constants.USER_STATUS_DEACTIVATED;
                    }
                }
                return value;
            }
        }

        public String alias {
            get {
                Integer maxLength = userColumnLastName.length() >= 7 ? 7 : userColumnLastName.length();
                return userColumnFirstName.substring(0, 1).toUpperCase() + userColumnLastName.substring(0, maxLength);
            }
        }

        @AuraEnabled
        public Contact contact;

        @AuraEnabled
        public User user {
            get {
                if (contact != null) {
                    return !contact.Users.isEmpty() ? contact.Users.get(0) : null;
                }
                return null;
            }
        }

        public UserTableItem(Contact c) {
            this.contact = c;
            this.isSelected = false;
        }

        public UserTableItem() {
        }

        public UserTableItem parse(String json) {
            return (UserTableItem) System.JSON.deserialize(json, UserTableItem.class);
        }

        public User getUser() {
            return new User(
                    FirstName = userColumnFirstName,
                    LastName = userColumnLastName,
                    Alias = alias,
                    Email = userColumnEmail,
                    CommunityNickname = FGXEnt_Utilities.generateRandomString(10),
                    Username = userColumnEmail,
                    ProfileId = FGXEnt_Constants.FGX_ENTERPRISE_STARTER_PROFILE,
                    CountryCode = 'US',
                    FGXEnt_Product_Access_Level__c = 'User',
                    TimeZoneSidKey = 'America/New_York',
                    LocaleSidKey = 'en_US',
                    LanguageLocaleKey = 'en_US',
                    DefaultCurrencyIsoCode = 'USD',
                    EmailEncodingKey = 'ISO-8859-1'
            );
        }

        public Integer compareTo(Object objToCompare) {
            UserTableItem tableItem = (UserTableItem) objToCompare;

            Integer currentWeight = FGXEnt_Constants.USER_TABLE_SORTING_WEIGHTS.get(statusColumn);
            Integer compareWeight = FGXEnt_Constants.USER_TABLE_SORTING_WEIGHTS.get(tableItem.statusColumn);

            if (currentWeight == compareWeight) {
                return 0;
            } else if (currentWeight < compareWeight) {
                return 1;
            } else {
                return -1;
            }
        }
    }

    public class UserCreateRequest {
        public User user;

        public Contact contact;

        public String alias {
            get {
                Integer maxLength = user.LastName.length() >= 7 ? 7 : user.LastName.length();
                return user.FirstName.substring(0, 1).toUpperCase() + user.LastName.substring(0, maxLength);
            }
        }

        public UserCreateRequest() {
        }

        public UserCreateRequest(User user) {
            this.user = user;
        }

        public UserCreateRequest parse(String json) {
            User u = (User) System.JSON.deserialize(json, User.class);
            return new UserCreateRequest(u);
        }

        public User getUser() {
            return new User(
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Alias = alias,
                    Email = user.Email,
                    CommunityNickname = FGXEnt_Utilities.generateRandomString(10),
                    Username = user.Email,
                    ProfileId = FGXEnt_Constants.FGX_ENTERPRISE_STARTER_PROFILE,
                    CountryCode = 'US',
                    FGXEnt_Product_Access_Level__c = user.FGXEnt_Product_Access_Level__c,
                    TimeZoneSidKey = 'America/New_York',
                    LocaleSidKey = 'en_US',
                    LanguageLocaleKey = 'en_US',
                    DefaultCurrencyIsoCode = 'USD',
                    EmailEncodingKey = 'ISO-8859-1'
            );
        }

        public Contact getContact(Id contactId) {
            return new Contact(
                    Id = contactId,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    Title = user.Title,
                    Phone = user.Phone,
                    AccountId = FGXEnt_Constants.CURRENT_USER_ACCOUNT,
                    RecordTypeId = FGXEnt_Constants.CONTACT_CUSTOMER_EMPLOYEE_RT
            );
        }

        public Contact getContact() {
            return getContact(null);
        }
    }

    public class UserCreateResult extends ResultMsg {

        public UserCreateResult(Exception e) {
            this.e = e;
            this.success = false;
        }

        public UserCreateResult() {
            this.success = true;
        }

        public UserCreateResult(Database.SaveResult saveResults) {
            List<String> errorList = getErrors(new List<Database.SaveResult>{
                    saveResults
            });
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }
    }

    public class UserResetResult extends ResultMsg {

        public UserResetResult(Exception e) {
            this.e = e;
            this.success = false;
        }

        public UserResetResult(Boolean allSuccess, Integer successCount) {
            this.success = allSuccess;
            this.successCount = successCount;
        }
    }

    public class UserDeactivateResult extends ResultMsg {

        public UserDeactivateResult(List<Database.SaveResult> saveResults) {
            List<String> errorList = getErrors(saveResults);
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }
    }

    public class EntityContainers {
        @AuraEnabled
        public Map<String, EntityContainer> containerMap;
        @AuraEnabled
        public List<EntityContainer> containers {
            get {
                return containerMap.values();
            }
        }

        public EntityContainers(List<Entity__c> entities, String filters) {
            Boolean defaultOpen = String.isNotBlank(filters);
            containerMap = new Map<String, EntityContainer>();

            for (Entity__c entity : entities) {
                add(entity, defaultOpen);
            }

            if (!defaultOpen) {
                if (containers.size() > 2) {
                    containers.get(0).isOpen = true;
                } else {
                    for (EntityContainer container : containers) {
                        container.isOpen = true;
                    }
                }
            }

        }

        public void add(Entity__c entity, Boolean defaultOpen) {
            String countryName = entity.Country__r.Name;

            if (this.containerMap.containsKey(countryName)) {
                this.containerMap.get(countryName).items.add(entity);
            } else {
                this.containerMap.put(countryName, new EntityContainer(entity, defaultOpen));
            }
        }
    }

    public class EntityContainer {

        @AuraEnabled
        public Boolean isOpen;

        @AuraEnabled
        public String label {
            get {
                return country + ' (' + items.size() + ')';
            }
        }

        @AuraEnabled
        public String country;

        @AuraEnabled
        public List<Entity__c> items;

        public EntityContainer(Entity__c entity, Boolean defaultOpen) {
            this.isOpen = defaultOpen;
            this.items = new List<Entity__c>{
                    entity
            };
            this.country = entity.Country__r.Name;
        }
    }

    public class Entity extends ContainerMsg {
        private Entity__c entity;

        @AuraEnabled
        public String sobjectType {
            get {
                return Entity__c.SObjectType.toString();
            }
        }

        @AuraEnabled
        public Boolean isSelected = false;

        @AuraEnabled
        public String entityId {
            get {
                return entity.Id;
            }
        }

        @AuraEnabled
        public String entityNameAutonum {
            get {
                return entity.Name;
            }
        }

        @AuraEnabled
        public String entityName {
            get {
                return entity.Entity_Name__c;
            }
        }

        @AuraEnabled
        public String entityCountry {
            get {
                return entity.Country__r.Name;
            }
        }

        @AuraEnabled
        public String entityCountryCode {
            get {
                return entity.Country__r.Code__c;
            }
        }

        @AuraEnabled
        public String entityCode {
            get {
                return entity.Entity_Code__c;
            }
        }

        @AuraEnabled
        public String entityType {
            get {
                return entity.Entity_Type__c;
            }
        }

        @AuraEnabled
        public String entityStatus {
            get {
                return entity.Flow_Status__c;
            }
        }

        @AuraEnabled
        public String entityImportStatus {
            get {
                return entity.Import_Status__c;
            }
        }

        @AuraEnabled
        public String entityExportStatus {
            get {
                return entity.Export_Status__c;
            }
        }

        @AuraEnabled
        public String entityCountryImg {
            get {
                return entity.Country__r.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String entityTaxId {
            get {
                return entity.Tax_ID__c;
            }
        }

        @AuraEnabled
        public String entityLegalName {
            get {
                return entity.Legal_Name__c;
            }
        }

        @AuraEnabled
        public String entityAddressCombined {
            get {
                List<String> address = new List<String>();

                if (String.isNotBlank(entity.Address_Line_1__c)) {
                    address.add(entity.Address_Line_1__c);
                }
                if (String.isNotBlank(entity.Address_Line_2__c)) {
                    address.add(entity.Address_Line_2__c);
                }
                if (String.isNotBlank(entity.Address_Line_3__c)) {
                    address.add(entity.Address_Line_3__c);
                }
                if (String.isNotBlank(entity.City__c)) {
                    address.add(entity.City__c);
                }

                String state = 'US'.equalsIgnoreCase(entity.Address_Country__r?.Code__c) ? entity.State_US__c : entity.State__c;
                if (String.isNotBlank(state)) {

                    if (String.isNotBlank(entity.Post_Code__c)) {
                        state += ' ' + entity.Post_Code__c;
                    }

                    address.add(state);
                }

                if (String.isNotBlank(entity.Address_Country__r.Code__c)) {
                    address.add(entity.Address_Country__r.Code__c);
                }

                return String.join(address, ', ');
            }
        }

        @AuraEnabled
        public String entityCustomsCode {
            get {
                return entity.Customs_Code__c;
            }
        }

        @AuraEnabled
        public String entityCustomsCodeLabel {
            get {
                if (entity.Country__r.CustomsCode_Label__c != null) {
                    return entity.Country__r.CustomsCode_Label__c;
                }
                return 'Customs Code';
            }
        }

        @AuraEnabled
        public String entityTaxIdLabel {
            get {
                if (entity.Country__r.TaxID_Label__c != null) {
                    return entity.Country__r.TaxID_Label__c;
                }
                return 'Tax ID';
            }
        }

        @AuraEnabled
        public String entityNotes {
            get {
                return entity.Entity_Notes__c;
            }
        }

        @AuraEnabled
        public String recordType {
            get {
                return entity.RecordType.DeveloperName;
            }
        }

        @AuraEnabled
        public Boolean entityHasPoc {
            get {
                return entity.Has_Primary_Point_Of_Contact__c;
            }
        }

        @AuraEnabled
        public String entityContactName {
            get {
                return entity.Entity_Contact_Name__c;
            }
        }

        @AuraEnabled
        public String entityContactEmail {
            get {
                return entity.Entity_Contact_Email__c;
            }
        }

        @AuraEnabled
        public List<HistoryItem> history;

        public Entity(Entity__c entity) {
            this(entity, null);
        }

        public Entity(Entity__c entity, List<HistoryItem> history) {
            this.entity = entity;
            this.history = history;
            if (this.history != null && this.history.size() > 0) {
                this.history.sort();
            }
        }
    }

    public class HistoryItem implements Comparable {
        @AuraEnabled
        public Datetime createdDate;

        @AuraEnabled
        public String createdByName;

        @AuraEnabled
        public String body;

        public HistoryItem(FeedItem feedItem) {
            this.createdByName = feedItem.CreatedBy.Name;
            this.createdDate = feedItem.CreatedDate;
            this.body = feedItem.Body;
        }

        public HistoryItem(SObject historyItem, Map<String, Schema.SObjectField> sobjectFields) {
            this.createdByName = (String) historyItem.getSObject('CreatedBy').get('Name');
            this.createdDate = (Datetime) historyItem.get('CreatedDate');
            System.debug(historyItem.get('Field'));
            Schema.DescribeFieldResult fieldResult = sobjectFields.get((String) historyItem.get('Field')).getDescribe();
            this.body = fieldResult.getLabel() + ' was updated';
        }

        public Integer compareTo(Object objToCompare) {
            HistoryItem historyItem = (HistoryItem) objToCompare;

            if (createdDate == historyItem.createdDate) {
                return 0;
            } else if (createdDate > historyItem.createdDate) {
                return -1;
            } else {
                return 1;
            }
        }
    }

    public class EntityArchiveResult extends ResultMsg {

        public EntityArchiveResult(Database.SaveResult saveResults) {
            List<String> errorList = getErrors(new List<Database.SaveResult>{
                    saveResults
            });
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }
    }

    public class ActionItem implements Comparable {
        private Action_Item__c actionItem;

        @AuraEnabled
        public String Id {
            get {
                return actionItem.Id;
            }
        }

        @AuraEnabled
        public String caseId {
            get {
                return actionItem.Case__r.Id;
            }
        }

        @AuraEnabled
        public String salesforceName {
            get {
                return actionItem.Name;
            }
        }

        @AuraEnabled
        public String caseNumber {
            get {
                return actionItem.Case__r.CaseNumber;
            }
        }

        @AuraEnabled
        public DateTime caseCreatedDate {
            get {
                return actionItem.Case__r.CreatedDate;
            }
        }

        @AuraEnabled
        public String clientRef {
            get {
                return actionItem.Case__r.Client_Ref__c;
            }
        }

        @AuraEnabled
        public Decimal caseAge {
            get {
                return actionItem.Case__r.Status_Age_Days__c;
            }
        }

        @AuraEnabled
        public String destinationCountry {
            get {
                return actionItem.Case__r.Destination_Country__r.Name;
            }
        }

        @AuraEnabled
        public String destinationCountryFlag {
            get {
                return actionItem.Case__r.Destination_Country__r.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String destinationSite {
            get {
                return actionItem.Case__r.Destination_Site__c;
            }
        }

        @AuraEnabled
        public String originCountry {
            get {
                return actionItem.Case__r.Origin_Country__r.Name;
            }
        }

        @AuraEnabled
        public String originCountryFlag {
            get {
                return actionItem.Case__r.Origin_Country__r.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String originSite {
            get {
                return actionItem.Case__r.Origin_Site__c;
            }
        }

        @AuraEnabled
        public String name {
            get {
                return actionItem.RecordType.Name;
            }
        }

        @AuraEnabled
        public String status {
            get {
                return actionItem.Status__c;
            }
        }

        @AuraEnabled
        public Boolean resolved {
            get {
                if (actionItem.Status__c != 'Pending Client' && actionItem.Status__c != 'Client Draft' && actionItem.Status__c != 'New')
                    return true;
                return false;
            }
        }

        @AuraEnabled
        public String priority {
            get {
                return actionItem.Priority__c;
            }
        }

        @AuraEnabled
        public Boolean urgent {
            get {
                return actionItem.Priority__c == 'Urgent' ? true : false;
            }
        }

        @AuraEnabled
        public String templateFiles {
            get {
                return actionItem.Template_Files__c;
            }
        }

        @AuraEnabled
        public String templateFilesDescription {
            get {
                return actionItem.Template_File_Description__c;
            }
        }

        @AuraEnabled
        public String fileUploadInstructions {
            get {
                return actionItem.File_Upload_Instructions__c;
            }
        }

        @AuraEnabled
        public String fileUploadRestrictions {
            get {
                return actionItem.File_Upload_Restrictions__c;
            }
        }

        @AuraEnabled
        public String fileUploadComments {
            get {
                return actionItem.File_Upload_Comments__c;
            }
        }

        @AuraEnabled
        public String clientReply {
            get {
                return actionItem.Client_Reply__c;
            }
        }

        @AuraEnabled
        public String requestTitle {
            get {
                return actionItem.Request_Title__c;
            }
        }


        @AuraEnabled
        public String requestDescription {
            get {
                return actionItem.Request_Description__c;
            }
        }

        @AuraEnabled
        public String createdDate {
            get {
                return actionItem.CreatedDate.format('M/d/yyyy');
            }
        }

        @AuraEnabled
        public Boolean isCollection {
            get {
                return actionItem.RecordTypeId == Constants.ACTION_ITEM_COLLECTION_RT ? true : false;
            }
        }

        @AuraEnabled
        public Boolean isDelivery {
            get {
                return actionItem.RecordTypeId == Constants.ACTION_ITEM_DELIVERY_RT ? true : false;
            }
        }

        @AuraEnabled
        public Boolean isFileUpload {
            get {
                return actionItem.RecordTypeId == Constants.ACTION_ITEM_FILE_UPLOAD_RT ? true : false;
            }
        }

        @AuraEnabled
        public Boolean isGeneralInfo {
            get {
                return actionItem.RecordTypeId == Constants.ACTION_ITEM_GENERAL_INFO_RT ? true : false;
            }
        }

        @AuraEnabled
        public Boolean isBOM {
            get {
                return actionItem.RecordTypeId == Constants.ACTION_ITEM_BOM_RT ? true : false;
            }
        }

        @AuraEnabled
        public String assignedTo {
            get {
                return actionItem.Assign_To_Client__c;
            }
        }

        @AuraEnabled
        public String caseContact {
            get {
                return actionItem.Case__r.ContactId;
            }
        }

        public Integer compareTo(Object objToCompare) {
            ActionItem wrapper = (ActionItem) objToCompare;
            if (urgent && !wrapper.urgent) {
                return -1;
            } else if (!urgent && wrapper.urgent) {
                return 1;
            } else {
                if (resolved && !wrapper.resolved) {
                    return 1;
                } else if (!resolved && wrapper.resolved) {
                    return -1;
                } else {
                    return 0;
                }
            }
        }

        public ActionItem(Action_Item__c actionItem) {
            this.actionItem = actionItem;
        }
    }

    public class Shipment extends ContainerMsg {
        private Case shipment;
        private List<ActionItem> actionItemList;
        private Boolean returnAllFields = false;
        private List<Timeline__c> timelineData;

        @AuraEnabled
        public Boolean noShipmentFound {
            get {
                return shipment == null || shipment?.Id == null;
            }
        }

        @AuraEnabled
        public String sobjectType {
            get {
                return Case.SObjectType.toString();
            }
        }

        @AuraEnabled
        public String Id {
            get {
                return shipment.Id;
            }
        }

        @AuraEnabled
        public String caseNumber {
            get {
                return shipment.CaseNumber;
            }
        }

        @AuraEnabled
        public String currencyISOCode {
            get {
                if (String.isNotBlank(shipment.BOM_Currency_Code__c)) {
                    return shipment.BOM_Currency_Code__c;
                } else if (String.isNotBlank(shipment.CurrencyIsoCode)) {
                    return shipment.CurrencyIsoCode;
                } else {
                    return Constants.CURRENCY_USD;
                }
            }
        }

        @AuraEnabled
        public String subject {
            get {
                return shipment.Subject;
            }
        }

        @AuraEnabled
        public String clientRef {
            get {
                return shipment.Client_Ref__c;
            }
        }

        @AuraEnabled
        public String clientRef2 {
            get {
                if (returnAllFields)
                    return shipment.Client_Ref2__c;
                return null;
            }
        }

        @AuraEnabled
        public String contactName {
            get {
                return shipment.Contact.Name;
            }
        }

        @AuraEnabled
        public String contactId {
            get {
                if (returnAllFields)
                    return shipment.Contact.Id;
                return null;
            }
        }

        @AuraEnabled
        public String destinationCity {
            get {
                if (returnAllFields)
                    return shipment.Destination_City__c;
                return null;
            }
        }

        @AuraEnabled
        public String projectedDeliveryDate {
            get {
                if(shipment.Projected_Delivery_Date__c == null){
                    return 'Pending';
                }else if(shipment.Projected_Delivery_Date__c < Date.today()){
                    return 'Contact FGX';
                }else{
                    DateTime dt = shipment.Projected_Delivery_Date__c;
                    return dt.format('MM/dd/yyyy');
                }
            }
        }

        @AuraEnabled
        public String destinationCountryCode {
            get {
                return shipment.Destination_Country__r.Code__c;
            }
        }

        @AuraEnabled
        public String destinationFlagIcon {
            get {
                return shipment.Destination_Country__r.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String destinationId {
            get {
                return shipment.Destination_Country__r.Id;
            }
        }

        @AuraEnabled
        public String destinationCountryName {
            get {
                return shipment.Destination_Country__r.Name;
            }
        }

        @AuraEnabled
        public String destinationSite {
            get {
                if (returnAllFields)
                    return shipment.Destination_Site__c;
                return null;
            }
        }

        @AuraEnabled
        public String originCity {
            get {
                if (returnAllFields)
                    return shipment.Origin_City__c;
                return null;
            }
        }

        @AuraEnabled
        public String originCountryCode {
            get {
                return shipment.Origin_Country__r.Code__c;
            }
        }

        @AuraEnabled
        public String originFlagIcon {
            get {
                return shipment.Origin_Country__r.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String originId {
            get {
                return shipment.Origin_Country__r.Id;
            }
        }

        @AuraEnabled
        public String originCountryName {
            get {
                return shipment.Origin_Country__r.Name;
            }
        }

        @AuraEnabled
        public String originSite {
            get {
                if (returnAllFields)
                    return shipment.Origin_Site__c;
                return null;
            }
        }

        @AuraEnabled
        public String originSiteCode {
            get {
                return shipment.Origin_Site__r.Site_Code__c;
            }
        }

        @AuraEnabled
        public Boolean manifestCompleted {
            get {
                return shipment.Manifest_Status__c == Constants.CASE_MANIFEST_STATUS_COMPLETED;
            }
        }

        @AuraEnabled
        public String manifestStatus {
            get {
                return shipment.Manifest_Status__c;
            }
        }

        @AuraEnabled
        public String destinationSiteCode {
            get {
                return shipment.Destination_Site__r.Site_Code__c;
            }
        }

        @AuraEnabled
        public String originSiteName {
            get {
                return shipment.Origin_Site__r.Site_Name__c;
            }
        }

        @AuraEnabled
        public String destinationSiteName {
            get {
                return shipment.Destination_Site__r.Site_Name__c;
            }
        }

        @AuraEnabled
        public String originDestinationSite {
            get {
                String originSiteName = shipment.Origin_Site__c != null ? originSiteName : 'Pending';
                String destinationSiteName = shipment.Destination_Site__c != null ? destinationSiteName : 'Pending';
                return originSiteName + ' / ' + destinationSiteName;
            }
        }

        @AuraEnabled
        public String exportImportEntity {
            get {
                String exportEntityName = shipment.Export_Entity__c != null ? shipment.Export_Entity__r.Entity_Name__c : 'TBD';
                String importEntityName = shipment.Import_Entity__c != null ? shipment.Import_Entity__r.Entity_Name__c : 'TBD';
                exportEntityName = String.isNotBlank(exportEntityName) ? exportEntityName : 'TBD';
                importEntityName = String.isNotBlank(importEntityName) ? importEntityName : 'TBD';

                return String.join(new List<String>{
                        exportEntityName, importEntityName
                }, ' / ');
            }
        }

        @AuraEnabled
        public String exportEntity {
            get {
                return shipment.Export_Entity__c;
            }
        }

        @AuraEnabled
        public String importEntity {
            get {
                return shipment.Import_Entity__c;
            }
        }

        @AuraEnabled
        public String exportEntityCode {
            get {
                return shipment.Export_Entity__c != null ? String.isNotBlank(shipment.Export_Entity__r.Entity_Code__c) ? shipment.Export_Entity__r.Entity_Code__c : 'Pending' : 'Pending';
            }
        }

        @AuraEnabled
        public String importEntityCode {
            get {
                return shipment.Import_Entity__c != null ? String.isNotBlank(shipment.Import_Entity__r.Entity_Code__c) ? shipment.Import_Entity__r.Entity_Code__c : 'Pending' : 'Pending';
            }
        }

        @AuraEnabled
        public String lastTrackingUpdate {
            get {
                return shipment?.Last_Tracking_Update__c?.format('MMMM d, yyyy');
            }
        }

        @AuraEnabled
        public String createdDate {
            get {
                return shipment.CreatedDate.format('M/d/yyyy');
            }
        }

        @AuraEnabled
        public String serviceLevel {
            get {
                return shipment.Service_Level__c;
            }
        }

        @AuraEnabled
        public Decimal shipmentValue {
            get {
                Decimal baseValue = shipment.Shipment_Value__c != null ? shipment.Shipment_Value__c : 0;
                String bomCurrency = this.currencyISOCode;
                if (bomCurrency != Constants.CURRENCY_USD && shipment.BOM_Conversion_Rate__c != null && shipment.BOM_Conversion_Rate__c > 0) {
                    return baseValue * shipment.BOM_Conversion_Rate__c;
                }
                return baseValue;
            }
        }

        @AuraEnabled
        public String status {
            get {
                return shipment.Status;
            }
        }

        @AuraEnabled
        public String reason {
            get {
                return shipment.Reason;
            }
        }

        @AuraEnabled
        public String statusDescription {
            get {
                if (returnAllFields) {
                    switch on shipment.Status {
                        when 'Booked' {
                            return 'Shipment confirmed with tracking #: ' + shipment.CaseNumber;
                        }
                        when 'Enroute to FGX' {
                            return 'Moving to the nearest FGX facility for processing';
                        }
                        when 'Scheduling Pickup' {
                            if (!String.isEmpty(shipment.Origin_City__c) && shipment.Origin_Country__c != null)
                                return 'Our team is coordinating collection from ' + shipment.Origin_City__c + ', ' + shipment.Origin_Country__r.Name;
                            else
                                    return 'Our team is coordinating collection';
                        }
                        when 'Received @ FGX' {
                            return 'Shipment arrived at FGX facility for further processing';
                        }
                        when 'Crating/Packing' {
                            return 'Packing is underway to ensure safe transport';
                        }
                        when 'Pending Manifest' {
                            return 'Bill of materials verification is underway';
                        }
                        when 'Pending Doc Approval' {
                            return 'Shipping documents are under review by local customs specialist';
                        }
                        when 'Pending Permit' {
                            return 'Required permits are in process and/or under agency review';
                        }
                        when 'Pending Payment' {
                            return 'Advance payment is required for the shipment to proceed';
                        }
                        when 'In Transit' {
                            return 'Shipment is moving towards the destination';
                        }
                        when 'Clearance Processing' {
                            return 'Customs processing is underway';
                        }
                        when 'Permit Processing' {
                            return 'Required permits are in process and/or under agency review';
                        }
                        when 'Out for Delivery' {
                            return 'Arrangements for final delivery are underway';
                        }
                        when 'Delivered' {
                            if (!String.isEmpty(shipment.POD_Name__c) && shipment.POD_Datetime__c != null) {
                                // convert POD time to user's timezone
                                String userTimeZoneId = UserInfo.getTimeZone().getID();
                                TimeZone userTimeZone = TimeZone.getTimeZone(userTimeZoneId);
                                DateTime userLocalTime = shipment.POD_Datetime__c.addSeconds(userTimeZone.getOffset(shipment.POD_Datetime__c) / 1000);
                                String formattedDate = userLocalTime.format('MMMM d, yyyy', userTimeZoneId);
                                String formattedTime = userLocalTime.format('h:mm a', userTimeZoneId);
                                String formattedDateTime = formattedDate + ' at ' + formattedTime;

                                return 'Received by: ' + shipment.POD_Name__c + ' on ' + formattedDateTime;
                            } else
                                    return 'POD details pending';
                        }
                        when 'On Hold' {
                            return 'Contact FGX for more information';
                        }
                        when 'Pending Client Action' {
                            return 'Please check action items or contact FGX';
                        }
                        when 'Quote Request' {
                            return 'Quote is being prepared by our team';
                        }
                        when 'Quoted' {
                            return 'Shipment can proceed once quote is accepted';
                        }
                        when else {
                            return '';
                        }
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String nextStep {
            get {
                switch on shipment.Status {
                    when 'Booked' {
                        if (shipment.Drop_Ship_to_FGX__c)
                            return 'Enroute to FGX';
                        else
                                return 'Scheduling Pickup';
                    }
                    when 'Enroute to FGX', 'Scheduling Pickup' {
                        return 'Received @ FGX';
                    }
                    when 'Received @ FGX' {
                        return 'Pending Manifest';
                    }
                    when 'Crating/Packing', 'Pending Manifest' {
                        return 'Pending Doc Approval';
                    }
                    when 'Pending Doc Approval', 'Pending Permit' {
                        return 'In Transit';
                    }
                    when 'Pending Payment', 'On Hold', 'Pending Client Action' {
                        return 'To be determined';
                    }
                    when 'In Transit' {
                        if (shipment.Domestic_Shipment__c)
                            return 'Out for Delivery';
                        else
                                return 'Clearance Processing';
                    }
                    when 'Clearance Processing', 'Permit Processing' {
                        return 'Out for Delivery';
                    }
                    when 'Out for Delivery' {
                        return 'Delivered';
                    }
                    when 'Quote Request' {
                        return 'Quoted';
                    }
                    when 'Quoted' {
                        return 'Booked';
                    }
                    when else {
                        return '';
                    }
                }
            }
        }

        @AuraEnabled
        public String nextStepDescription {
            get {
                if (returnAllFields) {
                    switch on shipment.Status {
                        when 'Booked' {
                            if (shipment.Drop_Ship_to_FGX__c)
                                return 'Drop-ship instructions will be provided by our team';
                            else
                                    return 'Shipment will move to the nearest FGX facility for processing';
                        }
                        when 'Enroute to FGX', 'Scheduling Pickup' {
                            return 'Shipment will be processed at the nearest FGX facility';
                        }
                        when 'Received @ FGX' {
                            return 'Bill of materials will be verified and reviewed';
                        }
                        when 'Crating/Packing', 'Pending Manifest' {
                            return 'Shipping documents will be reviewed by a local customs specialist';
                        }
                        when 'Pending Doc Approval', 'Pending Permit' {
                            return 'Shipment will be moving towards the destination';
                        }
                        when 'Pending Payment', 'On Hold', 'Pending Client Action' {
                            return 'Please check back later for more information';
                        }
                        when 'In Transit' {
                            if (shipment.Domestic_Shipment__c)
                                return 'Arrangments will be made for final delivery';
                            else
                                    return 'Customs processing will begin';
                        }
                        when 'Clearance Processing' {
                            return 'Arrangements will be made for final delivery';
                        }
                        when 'Permit Processing' {
                            return 'After clearance is completed, delivery will be scheduled';
                        }
                        when 'Out for Delivery' {
                            return 'Please check back for delivery confirmation';
                        }
                        when 'Quote Request' {
                            return 'Shipment can proceed once quote is accepted';
                        }
                        when 'Quoted' {
                            return 'Shipment will be confirmed with tracking #: ' + shipment.CaseNumber;
                        }
                        when else {
                            return '';
                        }
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public Boolean domesticShip {
            get {
                if (returnAllFields)
                    return shipment.Domestic_Shipment__c;
                return null;
            }
        }

        @AuraEnabled
        public Decimal totalWeight {
            get {
                if (returnAllFields)
                    return shipment.Total_Gross_Wt__c;
                return null;
            }
        }

        @AuraEnabled
        public Boolean dropship {
            get {
                if (returnAllFields)
                    return shipment.Drop_Ship_to_FGX__c;
                return null;
            }
        }

        @AuraEnabled
        public String owner {
            get {
                if (String.isEmpty(shipment.Owner.FirstName) || String.isEmpty(shipment.Owner.LastName)) {
                    return null;
                }
                return shipment.Owner.FirstName + ' ' + shipment.Owner.LastName;
            }
        }

        @AuraEnabled
        public String ownerId {
            get {
                return shipment.OwnerId;
            }
        }

        @AuraEnabled
        public String ownerEmail {
            get {
                return shipment.Owner.Email;
            }
        }

        @AuraEnabled
        public String podName {
            get {
                return shipment.POD_Name__c;
            }
        }

        @AuraEnabled
        public String podDate {
            get {
                if (shipment.POD_Datetime__c != null) {
                    // convert POD time to user's timezone
                    String userTimeZoneId = UserInfo.getTimeZone().getID();
                    TimeZone userTimeZone = TimeZone.getTimeZone(userTimeZoneId);
                    DateTime userLocalTime = shipment.POD_Datetime__c.addSeconds(userTimeZone.getOffset(shipment.POD_Datetime__c) / 1000);
                    String formattedDate = userLocalTime.format('MMMM d, yyyy', userTimeZoneId);
                    String formattedTime = userLocalTime.format('h:mm a', userTimeZoneId);
                    String formattedDateTime = formattedDate + ' at ' + formattedTime;
                    return formattedDateTime;
                }
                return null;
            }
        }

        @AuraEnabled
        public String podDocument {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.POD_Document__c) && shipment.POD_Document__c.contains(';')) {
                        List<String> fileList = shipment.POD_Document__c.split(';');
                        if (!fileList.isEmpty()) {
                            return fileList[0]; // return the attachment
                        }
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String podDocumentAttachment {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.POD_Document__c) && shipment.POD_Document__c.contains(';')) {
                        List<String> fileList = shipment.POD_Document__c.split(';');
                        if (fileList.size() > 1) {
                            return fileList[1]; // return the attachment
                        }
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String podImage {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.POD_Signature_Image__c) && shipment.POD_Signature_Image__c.contains(';')) {
                        return shipment.POD_Signature_Image__c.split(';')[0];
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String exportDocumentAttachment {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.Export_Documents__c) && shipment.Export_Documents__c.contains(';')) {
                        List<String> fileList = shipment.Export_Documents__c.split(';');
                        if (fileList.size() > 1) {
                            return fileList[1]; // return the attachment
                        }
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String importDocumentAttachment {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.Import_Documents__c) && shipment.Import_Documents__c.contains(';')) {
                        List<String> fileList = shipment.Import_Documents__c.split(';');
                        if (fileList.size() > 1) {
                            return fileList[1]; // return the attachment
                        }
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String exportDocument {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.Export_Documents__c) && shipment.Export_Documents__c.contains(';')) {

                        return shipment.Export_Documents__c.split(';')[0];
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String importDocument {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.Import_Documents__c) && shipment.Import_Documents__c.contains(';')) {
                        return shipment.Import_Documents__c.split(';')[0];
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String finalCIDocumentAttachment {
            get {
                if (returnAllFields) {
                    if (!String.isBlank(shipment.Final_CI_Document__c) && shipment.Final_CI_Document__c.contains(';')) {
                        List<String> fileList = shipment.Final_CI_Document__c.split(';');
                        if (fileList.size() > 1) {
                            return fileList[1]; // return the attachment
                        }
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String aesItn {
            get {
                if (returnAllFields) {
                    return shipment.AES_ITN__c;
                }
                return null;
            }
        }

        @AuraEnabled
        public String caseClosureProcedure {
            get {
                if (returnAllFields) {
                    return shipment.Account.Case_Closure_Procedure__c;
                }
                return null;
            }
        }

        @AuraEnabled
        public Boolean iorNeeded {
            get {
                if (returnAllFields) {
                    return shipment.IOR_Needed__c;
                }
                return null;
            }
        }

        @AuraEnabled
        public Boolean eorNeeded {
            get {
                if (returnAllFields) {
                    return shipment.EOR_Needed__c;
                }
                return null;
            }
        }

        @AuraEnabled
        public String mawb {
            get {
                if (returnAllFields) {
                    return shipment.MAWB__c;
                }
                return null;
            }
        }

        @AuraEnabled
        public List<ActionItem> actionItems {
            get {
                return actionItemList;
            }
        }

        @AuraEnabled
        public Integer actionItemCount {
            get {
                if (actionItemList != null)
                    return actionItemList.size();
                return null;
            }
        }

        @AuraEnabled
        public String deliveryETA {
            get {
                if (timelineData == null)
                    return null;
                if (timelineData.size() == 0)
                    return 'Pending';
                switch on shipment.Status {
                    when 'Received @ FGX', 'Pending Manifest', 'Pending Doc Approval', 'Pending Permit' {
                        Decimal minDays = 0;
                        Decimal maxDays = 0;
                        for (Timeline__c data : timelineData) {
                            minDays += data.Days_Min__c;
                            maxDays += data.Days_Max__c;
                        }
                        return Integer.valueOf(minDays) + ' to ' + Integer.valueOf(maxDays) + ' Days';
                    }
                    when 'In Transit' {
                        Decimal minDays = 0;
                        Decimal maxDays = 0;
                        for (Timeline__c data : timelineData) {
                            if (data.Category__c != 'Manifest & Document Preparation') {
                                minDays += data.Days_Min__c;
                                maxDays += data.Days_Max__c;
                            }
                        }
                        return Integer.valueOf(minDays) + ' to ' + Integer.valueOf(maxDays) + ' Days';
                    }
                    when 'Clearance Processing', 'Permit Processing' {
                        Decimal minDays = 0;
                        Decimal maxDays = 0;
                        for (Timeline__c data : timelineData) {
                            if (data.Category__c != 'Manifest & Document Preparation' && data.Category__c != 'Direct Air Freight') {
                                minDays += data.Days_Min__c;
                                maxDays += data.Days_Max__c;
                            }
                        }
                        return Integer.valueOf(minDays) + ' to ' + Integer.valueOf(maxDays) + ' Days';
                    }
                    when 'Out for Delivery' {
                        Decimal minDays = 0;
                        Decimal maxDays = 0;
                        for (Timeline__c data : timelineData) {
                            if (data.Category__c != 'Manifest & Document Preparation' && data.Category__c != 'Direct Air Freight'
                                    && data.Category__c != 'Customs Clearance' && data.Category__c != 'Permit Processing') {
                                minDays += data.Days_Min__c;
                                maxDays += data.Days_Max__c;
                            }
                        }
                        return Integer.valueOf(minDays) + ' to ' + Integer.valueOf(maxDays) + ' Days';
                    }
                    when 'Booked', 'Enroute to FGX', 'Scheduling Pickup' {
                        Decimal minDays = 3; //fixed 3 working day buffer for pickup estimate
                        Decimal maxDays = 3; //fixed 3 working day buffer for pickup estimate
                        for (Timeline__c data : timelineData) {

                            minDays += data.Days_Min__c;

                            maxDays += data.Days_Max__c;

                        }
                        return Integer.valueOf(minDays) + ' to ' + Integer.valueOf(maxDays) + ' Days';

                    }
                    when 'Crating/Packing', 'Pending Payment', 'On Hold', 'Pending Client Action', 'Quote Request', 'Quoted' {
                        return 'Pending';
                    }
                    when 'Closed', 'Delivered' {
                        return '';
                    }
                    when else {
                        return 'Pending';
                    }
                }
            }
        }

        @AuraEnabled
        public String destinationAddress1 {
            get {
                if (returnAllFields)
                    return shipment.Destination_Address_Line_1__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationAddress2 {
            get {
                if (returnAllFields)
                    return shipment.Destination_Address_Line_2__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationAddress3 {
            get {
                if (returnAllFields)
                    return shipment.Destination_Address_Line_3__c;
                return null;
            }
        }

        @AuraEnabled
        public String originAddress1 {
            get {
                if (returnAllFields)
                    return shipment.Origin_Address_Line_1__c;
                return null;
            }
        }

        @AuraEnabled
        public String originAddress2 {
            get {
                if (returnAllFields)
                    return shipment.Origin_Address_Line_2__c;
                return null;
            }
        }

        @AuraEnabled
        public String originAddress3 {
            get {
                if (returnAllFields)
                    return shipment.Origin_Address_Line_3__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationCompany {
            get {
                if (returnAllFields)
                    return shipment.Destination_Company_Name__c;
                return null;
            }
        }

        @AuraEnabled
        public String originCompany {
            get {
                if (returnAllFields)
                    return shipment.Origin_Company_Name__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationPostCode {
            get {
                if (returnAllFields)
                    return shipment.Destination_Post_Code__c;
                return null;
            }
        }

        @AuraEnabled
        public String originPostCode {
            get {
                if (returnAllFields)
                    return shipment.Origin_Post_Code__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationState {
            get {
                if (returnAllFields)
                    return shipment.Destination_State__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationUSState {
            get {
                if (returnAllFields)
                    return shipment.Destination_US_State__c;
                return null;
            }
        }

        @AuraEnabled
        public String originState {
            get {
                if (returnAllFields)
                    return shipment.Origin_State__c;
                return null;
            }
        }

        @AuraEnabled
        public String originUSState {
            get {
                if (returnAllFields)
                    return shipment.Origin_US_State__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationCounty {
            get {
                if (returnAllFields)
                    return shipment.Destination_County_Province_Other__c;
                return null;
            }
        }

        @AuraEnabled
        public String originCounty {
            get {
                if (returnAllFields)
                    return shipment.Origin_County_Province_Other__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationContactName {
            get {
                if (returnAllFields) {
                    if (String.isEmpty(shipment.Destination_First_Name__c) || String.isEmpty(shipment.Destination_Last_Name__c))
                        return null;
                    return shipment.Destination_First_Name__c + ' ' + shipment.Destination_Last_Name__c;
                }
                return null;
            }
        }

        @AuraEnabled
        public String originContactName {
            get {
                if (returnAllFields) {
                    if (String.isEmpty(shipment.Origin_First_Name__c) || String.isEmpty(shipment.Origin_Last_Name__c))
                        return null;
                    return shipment.Origin_First_Name__c + ' ' + shipment.Origin_Last_Name__c;
                }
                return null;
            }
        }

        @AuraEnabled
        public String destinationEmail {
            get {
                if (returnAllFields)
                    return shipment.Destination_Email__c;
                return null;
            }
        }

        @AuraEnabled
        public String originEmail {
            get {
                if (returnAllFields)
                    return shipment.Origin_Email__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationPhone {
            get {
                if (returnAllFields)
                    return shipment.Destination_Mobile_Phone__c;
                return null;
            }
        }

        @AuraEnabled
        public String originPhone {
            get {
                if (returnAllFields)
                    return shipment.Origin_Mobile_Phone__c;
                return null;
            }
        }

        @AuraEnabled
        public String destinationAddressCombined {
            get {
                if (returnAllFields) {
                    List<String> addrOutputList = new List<String>();

                    if (String.isNotBlank(shipment.Destination_Company_Name__c)) {
                        addrOutputList.add(shipment.Destination_Company_Name__c);
                    }

                    if (String.isNotBlank(shipment.Destination_Address_Line_1__c)) {
                        addrOutputList.add(shipment.Destination_Address_Line_1__c);
                    }

                    if (String.isNotBlank(shipment.Destination_Address_Line_2__c) || String.isNotBlank(shipment.Destination_Address_Line_3__c)) {
                        List<String> arr = new List<String>();

                        if (String.isNotBlank(shipment.Destination_Address_Line_2__c)) {
                            arr.add(shipment.Destination_Address_Line_2__c);
                        }

                        if (String.isNotBlank(shipment.Destination_Address_Line_3__c)) {
                            arr.add(shipment.Destination_Address_Line_3__c);
                        }

                        addrOutputList.add(String.join(arr, ', '));
                    }

                    if (String.isNotBlank(shipment.Destination_City__c) || String.isNotBlank(shipment.Destination_Country__r.Name) || String.isNotBlank(shipment.Destination_Post_Code__c)) {
                        List<String> arr = new List<String>();

                        if (String.isNotBlank(shipment.Destination_City__c)) {
                            arr.add(shipment.Destination_City__c);
                        }

                        String state = FGXEnt_Constants.UNITED_STATES.equalsIgnoreCase(shipment.Destination_Country__r.Name) ? shipment.Destination_US_State__c : shipment.Destination_State__c;

                        if (String.isNotBlank(state)) {
                            arr.add(', ' + state);
                        }

                        if (String.isNotBlank(shipment.Destination_Post_Code__c)) {
                            arr.add(' ' + shipment.Destination_Post_Code__c);
                        }

                        addrOutputList.add(String.join(arr, ''));
                    }

                    if (String.isNotBlank(shipment.Destination_County_Province_Other__c) || String.isNotBlank(shipment.Destination_Country__r.Name)) {
                        List<String> arr = new List<String>();

                        if (String.isNotBlank(shipment.Destination_County_Province_Other__c)) {
                            arr.add(shipment.Destination_County_Province_Other__c);
                        }

                        if (String.isNotBlank(shipment.Destination_Country__r.Name)) {
                            arr.add(shipment.Destination_Country__r.Name);
                        }

                        addrOutputList.add(String.join(arr, ', '));
                    }

                    return String.join(addrOutputList, ', ');
                }
                return null;
            }
        }

        @AuraEnabled
        public String originAddressCombined {
            get {
                if (returnAllFields) {
                    List<String> addrOutputList = new List<String>();

                    if (String.isNotBlank(shipment.Origin_Company_Name__c)) {
                        addrOutputList.add(shipment.Origin_Company_Name__c);
                    }

                    if (String.isNotBlank(shipment.Origin_Address_Line_1__c)) {
                        addrOutputList.add(shipment.Origin_Address_Line_1__c);
                    }

                    if (String.isNotBlank(shipment.Origin_Address_Line_2__c) || String.isNotBlank(shipment.Origin_Address_Line_3__c)) {
                        List<String> arr = new List<String>();

                        if (String.isNotBlank(shipment.Origin_Address_Line_2__c)) {
                            arr.add(shipment.Origin_Address_Line_2__c);
                        }

                        if (String.isNotBlank(shipment.Origin_Address_Line_3__c)) {
                            arr.add(shipment.Origin_Address_Line_3__c);
                        }

                        addrOutputList.add(String.join(arr, ', '));
                    }

                    if (String.isNotBlank(shipment.Origin_City__c) || String.isNotBlank(shipment.Origin_Country__r.Name) || String.isNotBlank(shipment.Origin_Post_Code__c)) {
                        List<String> arr = new List<String>();

                        if (String.isNotBlank(shipment.Origin_City__c)) {
                            arr.add(shipment.Origin_City__c);
                        }

                        String state = FGXEnt_Constants.UNITED_STATES.equalsIgnoreCase(shipment.Origin_Country__r.Name) ? shipment.Origin_US_State__c : shipment.Origin_State__c;

                        if (String.isNotBlank(state)) {
                            arr.add(', ' + state);
                        }

                        if (String.isNotBlank(shipment.Origin_Post_Code__c)) {
                            arr.add(' ' + shipment.Origin_Post_Code__c);
                        }

                        addrOutputList.add(String.join(arr, ''));
                    }

                    if (String.isNotBlank(shipment.Origin_County_Province_Other__c) || String.isNotBlank(shipment.Origin_Country__r.Name)) {
                        List<String> arr = new List<String>();

                        if (String.isNotBlank(shipment.Origin_County_Province_Other__c)) {
                            arr.add(shipment.Origin_County_Province_Other__c);
                        }

                        if (String.isNotBlank(shipment.Origin_Country__r.Name)) {
                            arr.add(shipment.Origin_Country__r.Name);
                        }

                        addrOutputList.add(String.join(arr, ', '));
                    }

                    return String.join(addrOutputList, ', ');
                }
                return null;
            }
        }

        @AuraEnabled
        public String originCityCountry {
            get {
                return this.shipment.Origin_City__c + ', ' + this.shipment.Origin_Country__r.Code__c;
            }
        }

        @AuraEnabled
        public String destinationCityCountry {
            get {
                return this.shipment.Destination_City__c + ', ' + this.shipment.Destination_Country__r.Code__c;
            }
        }

        @AuraEnabled
        public Decimal actionCount {
            get {
                return shipment.Action_Item_Count__c;
            }
        }

        @AuraEnabled
        public String clientApprovalStatus {
            get {
                return shipment?.Client_Approval_Status__c;
            }
        }

        // used for shipment detail page
        public Shipment(Case shipment, List<ActionItem> actionItems, Boolean returnAll) {
            this.actionItemList = actionItems;
            this.shipment = shipment;
            this.returnAllFields = returnAll;
        }

        // used for shipment table
        public Shipment(Case shipment, List<Timeline__c> timelineData) {
            this.shipment = shipment;
            this.timelineData = timelineData;
        }

        public Shipment(Case shipment) {
            this.shipment = shipment;
        }
    }

    public class PullRequest extends ContainerMsg {
        private Pull_Request__c pullRequest;

        @AuraEnabled
        public List<AssetUnit> assetUnits;

        @AuraEnabled
        public List<String> matchingSerials;

        @AuraEnabled
        public String sobjectType {
            get {
                return Pull_Request__c.SObjectType.toString();
            }
        }

        @AuraEnabled
        public String matchingSerialsString {
            get {
                return String.join(matchingSerials, ', ');
            }
        }

        @AuraEnabled
        public String id {
            get {
                return pullRequest.Id;
            }
        }

        // TODO: refactor all classes using pullRequestName to use
        // the following getter instead
        @AuraEnabled
        public String name {
            get {
                return pullRequest.Name;
            }
        }

        @AuraEnabled
        public String pullRequestName {
            get {
                return pullRequest.Name;
            }
        }

        @AuraEnabled
        public String createdByName {
            get {
                return pullRequest.Requestor__r.Name;
            }
        }

        @AuraEnabled
        public String recordType {
            get {
                return pullRequest.RecordType.Name;
            }
        }

        @AuraEnabled
        public String referenceNumber {
            get {
                return pullRequest.Client_Ref__c;
            }
        }

        @AuraEnabled
        public String referenceNumber2 {
            get {
                return pullRequest.Client_Ref2__c;
            }
        }

        @AuraEnabled
        public String status {
            get {
                return pullRequest.Status__c;
            }
        }

        @AuraEnabled
        public String destinationCity {
            get {
                return pullRequest.Destination_City__c;
            }
        }

        @AuraEnabled
        public String destinationCityCountry {
            get {
                return pullRequest.Destination_City__c + ', ' + pullRequest.Destination_Country__r.Code__c;
            }
        }

        @AuraEnabled
        public String destinationCountryCode {
            get {
                return pullRequest.Destination_Country__r.Code__c;
            }
        }

        @AuraEnabled
        public String destinationFlagIcon {
            get {
                return pullRequest.Destination_Country__r.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String destinationId {
            get {
                return pullRequest.Destination_Country__c;
            }
        }

        @AuraEnabled
        public String destinationCountryName {
            get {
                return pullRequest.Destination_Country__r.Name;
            }
        }

        @AuraEnabled
        public String destinationSite {
            get {
                return pullRequest.Destination_Site__c;
            }
        }

        @AuraEnabled
        public String destinationSiteCode {
            get {
                return pullRequest.Destination_Site__r.Site_Code__c;
            }
        }

        @AuraEnabled
        public String destinationSiteName {
            get {
                return pullRequest.Destination_Site__r.Site_Name__c;
            }
        }

        @AuraEnabled
        public String createdDate {
            get {
                return pullRequest.CreatedDate.format('M/d/yyyy');
            }
        }

        public PullRequest(Pull_Request__c pullRequest) {
            this.pullRequest = pullRequest;
            this.assetUnits = new List<AssetUnit>();
            this.matchingSerials = new List<String>();

            if (pullRequest.Asset_Units__r != null) {
                for (Asset_Unit__c unit : pullRequest.Asset_Units__r) {
                    this.assetUnits.add(new AssetUnit(unit));
                }
            }
        }
    }

    public class CommercialInvoiceLine {

        private Commercial_Invoice_Line__c cil;

        @AuraEnabled
        public List<AssetUnit> assetUnits;

        @AuraEnabled
        public List<AssetUnit> serializedAssetUnits {
            get {
                List<AssetUnit> units = new List<AssetUnit>();
                for(AssetUnit assetUnit : this.assetUnits) {
                    if(assetUnit.recordTypeId == Constants.ASSET_UNIT_SERIALIZED_RT) {
                        units.add(assetUnit);
                    }
                }
                return units;
            }
        }

        @AuraEnabled
        public List<AssetUnit> nonSerializedAssetUnits {
            get {
                List<AssetUnit> units = new List<AssetUnit>();
                for(AssetUnit assetUnit : this.assetUnits) {
                    if(assetUnit.recordTypeId == Constants.ASSET_UNIT_NON_SERIALIZED_RT) {
                        units.add(assetUnit);
                    }
                }
                return units;
            }
        }

        @AuraEnabled
        public String id {
            get {
                return cil.Id;
            }
        }

        @AuraEnabled
        public String currencyISOCode {
            get {
                return cil.CurrencyIsoCode;
            }
        }

        @AuraEnabled
        public String manufacturer {
            get {
                return cil.Manufacturer__c;
            }
        }

        @AuraEnabled
        public String partNumber {
            get {
                return cil.Part_Number__c;
            }
        }

        @AuraEnabled
        public String description {
            get {
                return cil.Description__c;
            }
        }

        @AuraEnabled
        public Decimal unitValue {
            get {
                return cil.Value__c != null ? cil.Value__c : 0;
            }
        }

        @AuraEnabled
        public Decimal totalValue {
            get {
                return cil.Value__c != null && cil.Quantity__c != null ? cil.Value__c * cil.Quantity__c : 0;
            }
        }

        @AuraEnabled
        public String productType {
            get {
                return cil.Product_Type__c;
            }
        }

        @AuraEnabled
        public String quantity {
            get {
                return String.valueOf(cil.Quantity__c);
            }
        }

        @AuraEnabled
        public String condition {
            get {
                return cil.Condition__c;
            }
        }

        @AuraEnabled
        public String hsCode {
            get {
                return cil.HS_Code__c;
            }
        }

        @AuraEnabled
        public String eccn {
            get {
                return cil.ECCN__c;
            }
        }

        @AuraEnabled
        public String cooName {
            get {
                return cil.Country_of_Origin__r.Name;
            }
        }

        public CommercialInvoiceLine(Commercial_Invoice_Line__c cil) {
            this.cil = cil;
            this.assetUnits = new List<AssetUnit>();

            for (Asset_Unit__c unit : this.cil.Asset_Units__r) {
                this.assetUnits.add(new AssetUnit(unit));
            }
        }
    }

    public class AssetUnit extends ContainerMsg {
        private Asset_Unit__c assetUnit;

        @AuraEnabled
        public String id {
            get {
                return assetUnit.Id;
            }
        }

        @AuraEnabled
        public String location {
            get {
                return assetUnit.Location__c;
            }
        }

        @AuraEnabled
        public String pieceID {
            get {
                return assetUnit.Piece_ID__c;
            }
        }

        @AuraEnabled
        public String name {
            get {
                return assetUnit.Name;
            }
        }

        @AuraEnabled
        public String recordTypeName {
            get {
                return assetUnit.RecordType.Name;
            }
        }

        @AuraEnabled
        public String recordTypeId {
            get {
                return assetUnit.RecordTypeId;
            }
        }

        @AuraEnabled
        public String status {
            get {
                return assetUnit.Status__c;
            }
        }

        @AuraEnabled
        public String manufacturer {
            get {
                return assetUnit.Unit_Manufacturer__c;
            }
        }

        @AuraEnabled
        public String partNumber {
            get {
                return assetUnit.Unit_Part_Number__c;
            }
        }

        @AuraEnabled
        public String quantity {
            get {
                return assetUnit.RecordTypeId == Constants.ASSET_UNIT_SERIALIZED_RT && assetUnit.Quantity__c == null ? '1' : String.valueOf(Math.abs(assetUnit.Quantity__c));
            }
        }

        @AuraEnabled
        public String serialNumber {
            get {
                return assetUnit.Serial_Number__c;
            }
        }

        @AuraEnabled
        public String condition {
            get {
                return assetUnit.Condition__c;
            }
        }

        @AuraEnabled
        public String createdDate {
            get {
                return assetUnit.CreatedDate.format('M/d/yyyy');
            }
        }

        @AuraEnabled
        public String relatedShipment {
            get {
                return assetUnit.Related_Shipment__c;
            }
        }

        @AuraEnabled
        public String relatedShipmentNumber {
            get {
                return assetUnit.Related_Shipment__r.CaseNumber;
            }
        }

        @AuraEnabled
        public String relatedQuote {
            get {
                return assetUnit.Related_Quote__c;
            }
        }

        @AuraEnabled
        public String relatedQuoteName {
            get {
                return assetUnit.Related_Quote__r.Name;
            }
        }


        public AssetUnit(Asset_Unit__c assetUnit) {
            this.assetUnit = assetUnit;
        }
    }

    public class PullRequestDeleteResult extends ResultMsg {

        public PullRequestDeleteResult(Boolean result) {
            this.success = result;
        }

        public PullRequestDeleteResult(Database.DeleteResult deleteResult) {
            List<String> errorList = getErrors(new List<Database.DeleteResult>{
                    deleteResult
            });
            this.success = errorList.isEmpty() ? true : false ;
            this.errorString = String.join(errorList, ', ');
        }
    }

    public class CaseActionItemCount {
        public String caseId { get; set; }
        public Integer itemCount { get; set; }
    }

    public class ShipmentSite extends ContainerMsg {
        private Site__c site;
        private Boolean returnAllFields = true;

        @AuraEnabled
        public String sobjectType {
            get {
                return Site__c.SObjectType.toString();
            }
        }

        @AuraEnabled
        public String Id {
            get {
                return site.Id;
            }
        }

        @AuraEnabled
        public String siteName {
            get {
                return site.Site_Name__c;
            }
        }

        @AuraEnabled
        public String siteCode {
            get {
                return site.Site_Code__c;
            }
        }

        @AuraEnabled
        public String siteNameAutonum {
            get {
                return site.Name;
            }
        }

        @AuraEnabled
        public String siteType {
            get {
                return site.Site_Type__c;
            }
        }

        @AuraEnabled
        public String siteCountry {
            get {
                return site.Country__r.Name;
            }
        }

        @AuraEnabled
        public String siteCountryCode {
            get {
                return site.Country__r.Code__c;
            }
        }

        @AuraEnabled
        public String address {
            get {
                return site.Address_Line_1__c;
            }
        }

        @AuraEnabled
        public String city {
            get {
                return site.City__c;
            }
        }

        @AuraEnabled
        public String contactName {
            get {
                if (site.Site_Contact_First_Name__c != null) {
                    if (site.Site_Contact_Last_Name__c != null) {
                        return site.Site_Contact_First_Name__c + ' ' + site.Site_Contact_Last_Name__c;
                    } else {
                        return site.Site_Contact_First_Name__c;
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String contactFirstName {
            get {
                return site.Site_Contact_First_Name__c;
            }
        }

        @AuraEnabled
        public String contactLastName {
            get {
                return site.Site_Contact_Last_Name__c;
            }
        }

        @AuraEnabled
        public String contactEmail {
            get {
                return site.Site_Contact_Email__c;
            }
        }

        @AuraEnabled
        public String contactPhone {
            get {
                return site.Site_Contact_Phone__c;
            }
        }

        @AuraEnabled
        public String country {
            get {
                return site.Country__r.Name;
            }
        }

        @AuraEnabled
        public String siteCountryImg {
            get {
                return site.Country__r.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String companyName {
            get {
                return site.Company_Name__c;
            }
        }

        @AuraEnabled
        public String complianceStatus {
            get {
                if (site.Site_Compliance_Status__c == null)
                    return 'In Draft';
                return site.Site_Compliance_Status__c;
            }
        }

        @AuraEnabled
        public String surveyStatus {
            get {
                if (!returnAllFields)
                    return null;
                return site.Site_Survey_Status__c;
            }
        }

        @AuraEnabled
        public String daysOpen {
            get {
                if (!returnAllFields)
                    return null;
                return site.Days_Open__c;
            }
        }

        @AuraEnabled
        public String weekdayClose {
            get {
                if (!returnAllFields)
                    return null;
                if (site.Weekday_Close_Time__c != null) {
                    Datetime closeDateTime = Datetime.newInstance(Date.today(), site.Weekday_Close_Time__c);
                    return closeDateTime.format('h:mm a');
                }
                return null;
            }
        }

        @AuraEnabled
        public String weekendClose {
            get {
                if (!returnAllFields)
                    return null;
                if (site.Weekend_Close_Time__c != null) {
                    Datetime closeDateTime = Datetime.newInstance(Date.today(), site.Weekend_Close_Time__c);
                    return closeDateTime.format('h:mm a');
                }
                return null;
            }
        }

        @AuraEnabled
        public String insideDelivery {
            get {
                if (!returnAllFields)
                    return null;
                return site.Inside_Delivery_Required__c;
            }
        }

        @AuraEnabled
        public String truckType {
            get {
                if (!returnAllFields)
                    return null;
                return site.Truck_Type__c;
            }
        }

        @AuraEnabled
        public String ticketRequired {
            get {
                if (!returnAllFields)
                    return null;
                return site.Ticket_Required__c;
            }
        }

        @AuraEnabled
        public String trashRequired {
            get {
                if (!returnAllFields)
                    return null;
                return site.Waste_Removal_Required__c;
            }
        }

        @AuraEnabled
        public String createdTimezone {
            get {
                if (!returnAllFields)
                    return null;
                return site.CreatedBy.TimeZoneSidKey;
            }
        }

        @AuraEnabled
        public String securityContactExists {
            get {
                if (!returnAllFields)
                    return null;
                return site.Security_Site_Contact__c;
            }
        }


        @AuraEnabled
        public String securityName {
            get {
                if (!returnAllFields)
                    return null;
                return site.Security_Contact_Name__c;
            }
        }

        @AuraEnabled
        public String securityEmail {
            get {
                if (!returnAllFields)
                    return null;
                return site.Security_Contact_Email__c;
            }
        }

        @AuraEnabled
        public String securityPhone {
            get {
                if (!returnAllFields)
                    return null;
                return site.Security_Contact_Phone__c;
            }
        }

        @AuraEnabled
        public String primaryContactExists {
            get {
                if (!returnAllFields)
                    return null;
                return site.Primary_Site_Contact__c;
            }
        }

        @AuraEnabled
        public String primaryContactName {
            get {
                if (site.Site_Contact_First_Name__c != null) {
                    if (site.Site_Contact_Last_Name__c != null) {
                        return site.Site_Contact_First_Name__c + ' ' + site.Site_Contact_Last_Name__c;
                    } else {
                        return site.Site_Contact_First_Name__c;
                    }
                }
                return null;
            }
        }

        @AuraEnabled
        public String primaryContactFirstName {
            get {
                return site.Site_Contact_First_Name__c;
            }
        }

        @AuraEnabled
        public String primaryContactLastName {
            get {
                return site.Site_Contact_Last_Name__c;
            }
        }

        @AuraEnabled
        public String primaryContactEmail {
            get {
                return site.Site_Contact_Email__c;
            }
        }

        @AuraEnabled
        public String primaryContactPhone {
            get {
                return site.Site_Contact_Phone__c;
            }
        }

        @AuraEnabled
        public String siteNotes {
            get {
                if (!returnAllFields)
                    return null;
                return site.Site_Notes__c;
            }
        }

        @AuraEnabled
        public String loadingDock {
            get {
                if (!returnAllFields)
                    return null;
                return site.Loading_Dock__c;
            }
        }

        @AuraEnabled
        public String coiStatus {
            get {
                if (!returnAllFields)
                    return null;
                return site.COI_Required__c;
            }
        }

        @AuraEnabled
        public String groundFloor {
            get {
                if (!returnAllFields)
                    return null;
                return site.Ground_Floor_Only__c;
            }
        }

        @AuraEnabled
        public String defaultFloor {
            get {
                if (!returnAllFields)
                    return null;
                return site.Default_Floor__c;
            }
        }

        @AuraEnabled
        public String elevator {
            get {
                if (!returnAllFields)
                    return null;
                return site.Elevator_Type__c;
            }
        }

        @AuraEnabled
        public String gatePass {
            get {
                if (!returnAllFields)
                    return null;
                return site.Gate_Pass_Required__c;
            }
        }

        @AuraEnabled
        public String gatePassData {
            get {
                if (!returnAllFields)
                    return null;
                return site.Gate_Pass_Data__c;
            }
        }

        @AuraEnabled
        public String forklift {
            get {
                if (!returnAllFields)
                    return null;
                return site.Fork_Lift__c;
            }
        }

        @AuraEnabled
        public String palletJack {
            get {
                if (!returnAllFields)
                    return null;
                return site.Pallet_Jack__c;
            }
        }

        @AuraEnabled
        public String logisticsNotes {
            get {
                if (!returnAllFields)
                    return null;
                return site.Logistics_Notes__c;
            }
        }

        @AuraEnabled
        public String operationsNotes {
            get {
                if (!returnAllFields)
                    return null;
                return site.Operations_Notes__c;
            }
        }

        @AuraEnabled
        public String securityNotes {
            get {
                if (!returnAllFields)
                    return null;
                return site.Security_Notes__c;
            }
        }

        @AuraEnabled
        public String siteAddressCombined {
            get {
                List<String> addrOutputList = new List<String>();

                if (String.isNotBlank(site.Company_Name__c)) {
                    addrOutputList.add(site.Company_Name__c);
                }

                if (String.isNotBlank(site.Address_Line_1__c)) {
                    addrOutputList.add(site.Address_Line_1__c);
                }

                if (String.isNotBlank(site.Address_Line_2__c) || String.isNotBlank(site.Address_Line_3__c)) {
                    List<String> arr = new List<String>();

                    if (String.isNotBlank(site.Address_Line_2__c)) {
                        arr.add(site.Address_Line_2__c);
                    }

                    if (String.isNotBlank(site.Address_Line_3__c)) {
                        arr.add(site.Address_Line_3__c);
                    }

                    addrOutputList.add(String.join(arr, ', '));
                }

                if (String.isNotBlank(site.City__c) || String.isNotBlank(site.Country__r.Name) || String.isNotBlank(site.Post_Code__c)) {
                    List<String> arr = new List<String>();

                    if (String.isNotBlank(site.City__c)) {
                        arr.add(site.City__c);
                    }

                    String state = FGXEnt_Constants.UNITED_STATES.equalsIgnoreCase(site.Country__r.Name) ? site.State_US__c : site.State__c;

                    if (String.isNotBlank(state)) {
                        arr.add(', ' + state);
                    }

                    if (String.isNotBlank(site.Post_Code__c)) {
                        arr.add(' ' + site.Post_Code__c);
                    }

                    addrOutputList.add(String.join(arr, ''));
                }

                if (String.isNotBlank(site.County_Province_Other__c) || String.isNotBlank(site.Country__r.Name)) {
                    List<String> arr = new List<String>();

                    if (String.isNotBlank(site.County_Province_Other__c)) {
                        arr.add(site.County_Province_Other__c);
                    }

                    if (String.isNotBlank(site.Country__r.Name)) {
                        arr.add(site.Country__r.Name);
                    }

                    addrOutputList.add(String.join(arr, ', '));
                }

                return String.join(addrOutputList, ', ');
            }
        }

        @AuraEnabled
        public List<HistoryItem> history;

        public ShipmentSite(Site__c site, List<HistoryItem> history) {
            this.site = site;
            this.history = history;
        }

        public ShipmentSite(Site__c site) {
            this.site = site;
        }

        public ShipmentSite(Site__c site, Boolean returnAllFields) {
            this.site = site;
            this.returnAllFields = returnAllFields;
        }
    }

    public class CurrencyData {
        @AuraEnabled
        public Map<String, Decimal> currencyRateMap { get; set; }

        @AuraEnabled
        public Set<String> currencyIsoValues { get; set; }

        public CurrencyData() {
        }
    }

    public class Quote extends ContainerMsg {
        private Quote__c quote;
        private Case shipment;
        private List<Quote_Line__c> qtLines;
        private List<BOM_Line__c> bLines;
        private List<Timeline__c> timeline;

        @AuraEnabled
        public String pendingCaseId;

        // Default constructor for creating empty quotes
        public Quote() {
            this.quote = new Quote__c();
            this.qtLines = new List<Quote_Line__c>();
            this.bLines = new List<BOM_Line__c>();
            this.timeline = new List<Timeline__c>();
        }

        // Constructor for pending case mode
        public Quote(Case shipment) {
            this.shipment = shipment;
            this.pendingCaseId = shipment.Id;
            this.quote = new Quote__c();
            this.qtLines = new List<Quote_Line__c>();
            this.bLines = new List<BOM_Line__c>();
            this.timeline = new List<Timeline__c>();
        }

        private Boolean isPendingMode {
            get {
                return String.isNotBlank(this.pendingCaseId);
            }
        }

        @AuraEnabled
        public User runningUser;

        @AuraEnabled
        public String sobjectType {
            get {
                return isPendingMode ? Case.SObjectType.toString() : Quote__c.SObjectType.toString();
            }
        }

        @AuraEnabled
        public String Id {
            get {
                return isPendingMode ? null : quote?.Id;
            }
        }

        @AuraEnabled
        public String quoteNumber {
            get {
                return isPendingMode ? 'Pending' : quote?.Name;
            }
        }

        @AuraEnabled
        public String draftNumber {
            get {
                if (isPendingMode) return 'Pending';
                return quote?.Name != null ? quote.Name.replace('QT', 'DRAFT') : null;
            }
        }

        @AuraEnabled
        public String activeVersionId {
            get {
                return isPendingMode ? null : this.activeVersion?.Id;
            }
        }

        @AuraEnabled
        public String activeVersionStatus {
            get {
                return isPendingMode ? 'Pending' : this.activeVersion?.Status__c;
            }
        }

        @AuraEnabled
        public String activeVersionActual {
            get {
                return isPendingMode ? null : this.quote?.Active_Version__c;
            }
        }


        @AuraEnabled
        public List<Quote_Version__c> quoteVersions {
            get {
                if (isPendingMode) return new List<Quote_Version__c>();

                if (this.quote?.Quote_Versions__r != null && !this.quote.Quote_Versions__r.isEmpty()) {
                    return this.quote.Quote_Versions__r;
                }
                return new List<Quote_Version__c>();
            }
        }

        public Quote_Version__c activeVersion {
            get {
                if (isPendingMode) return new Quote_Version__c();

                if (quote != null) {
                    List<Quote_Version__c> versions = quoteVersions;
                    if (!versions.isEmpty()) {
                        Map<Id, Quote_Version__c> quoteVersionMap = new Map<Id, Quote_Version__c>(versions);

                        if (quote.Active_Version__c != null && quoteVersionMap.containsKey(quote.Active_Version__c)) {
                            return quoteVersionMap.get(quote.Active_Version__c);
                        } else {
                            return versions[versions.size() - 1];
                        }
                    }
                }
                return new Quote_Version__c();
            }
            set;
        }

        @AuraEnabled
        public String status {
            get {
                if (isPendingMode) {
                    return 'Pending';
                }
                return quote?.Status__c;
            }
        }

        @AuraEnabled
        public String quoteStatus {
            get {
                if (isPendingMode) {
                    return 'In Progress';
                }
                return quote?.Status__c != null ? displayStatusMap.get(quote.Status__c) : null;
            }
        }

        @AuraEnabled
        public String quoteStatusIndicator {
            get {
                if (isPendingMode) {
                    return 'yellow-circle';
                }

                String status = this.quoteStatus;
                if (status == null) return '';

                if (new Set<String>{
                        'In Draft', 'Closed'
                }.contains(status)) {
                    return 'neutral-circle';
                }

                if (new Set<String>{
                        'In Progress', 'On Hold'
                }.contains(status)) {
                    return 'yellow-circle';
                }

                if (new Set<String>{
                        'Issued', 'Booked', 'Invoiced'
                }.contains(status)) {
                    return 'green-circle';
                }
                return '';
            }
        }

        @AuraEnabled
        public String clientRef {
            get {
                return isPendingMode ? shipment?.Client_Ref__c : quote?.Client_Ref__c;
            }
        }

        @AuraEnabled
        public String clientRef2 {
            get {
                return isPendingMode ? shipment?.Client_Ref2__c : quote?.Client_Ref2__c;
            }
        }

        @AuraEnabled
        public String contactName {
            get {
                return isPendingMode ? shipment?.Contact?.Name : quote?.Client_Contact__r?.Name;
            }
        }

        @AuraEnabled
        public String contactId {
            get {
                return isPendingMode ? shipment?.ContactId : quote?.Client_Contact__c;
            }
        }

        @AuraEnabled
        public String createdBy {
            get {
                return isPendingMode ? shipment?.CreatedBy?.Name : quote?.CreatedBy?.Name;
            }
        }

        @AuraEnabled
        public String createdDate {

            get {
                if (isPendingMode) {
                    return shipment?.CreatedDate != null ? shipment.CreatedDate.format('M/d/yyyy') : null;
                }
                return quote?.CreatedDate != null ? quote.CreatedDate.format('M/d/yyyy') : null;
            }
        }

        @AuraEnabled
        public String createdDateIso {
            get {
                return isPendingMode ? String.valueOf(shipment?.CreatedDate) : String.valueOf(quote?.CreatedDate);
            }
        }

        @AuraEnabled
        public String destinationCity {
            get {
                return isPendingMode ? shipment?.Destination_City__c : quote?.Destination_City__c;
            }
        }

        @AuraEnabled
        public String destinationCountryCode {
            get {
                return isPendingMode ? shipment?.Destination_Country__r?.Code__c : quote?.Destination_Country__r?.Code__c;
            }
        }

        @AuraEnabled
        public String destinationFlagIcon {
            get {
                return isPendingMode ? shipment?.Destination_Country__r?.Flag_Icon__c : quote?.Destination_Country__r?.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String destinationId {
            get {
                return isPendingMode ? shipment?.Destination_Country__c : quote?.Destination_Country__c;
            }
        }

        @AuraEnabled
        public String destinationCountryName {
            get {
                return isPendingMode ? shipment?.Destination_Country__r?.Name : quote?.Destination_Country__r?.Name;
            }
        }

        @AuraEnabled
        public String destinationSite {
            get {
                return isPendingMode ? shipment?.Destination_Site__c : quote?.Destination_Site__c;
            }
        }

        @AuraEnabled
        public String expiryDate {
            get {
                if (quote.Active_Version__c != null) {
                    if (quote.Active_Version__r.RS_Expiration__c != null) {
                        Integer year = quote.Active_Version__r.RS_Expiration__c.year();
                        Integer month = quote.Active_Version__r.RS_Expiration__c.month();
                        Integer day = quote.Active_Version__r.RS_Expiration__c.day();
                        return Datetime.newInstance(year, month, day).format('M/d/yyyy');
                    }
                }
                return '';
            }
        }

        @AuraEnabled
        public String expiryDateIso {
            get {
                if (quote.Active_Version__c != null) {
                    return String.valueOf(quote.Active_Version__r.RS_Expiration__c);
                }
                return '';
            }
        }

        @AuraEnabled
        public Decimal flowStep {
            get {
                return quote?.Flow_Step__c;
            }
        }

        @AuraEnabled
        public String flowStepRatio {
            get {
                if (quote?.Flow_Step__c != null) {
                    return String.valueOf(quote.Flow_Step__c + 1) + '/10 steps';
                }
                return '';
            }
        }

        @AuraEnabled
        public String originCity {
            get {
                return isPendingMode ? shipment?.Origin_City__c : quote?.Origin_City__c;
            }
        }

        @AuraEnabled
        public String originCountryCode {
            get {
                return isPendingMode ? shipment?.Origin_Country__r?.Code__c : quote?.Origin_Country__r?.Code__c;
            }
        }

        @AuraEnabled
        public String originFlagIcon {
            get {
                return isPendingMode ? shipment?.Origin_Country__r?.Flag_Icon__c : quote?.Origin_Country__r?.Flag_Icon__c;
            }
        }

        @AuraEnabled
        public String originId {
            get {
                return isPendingMode ? shipment?.Origin_Country__c : quote?.Origin_Country__c;
            }
        }

        @AuraEnabled
        public String originCountryName {
            get {
                return isPendingMode ? shipment?.Origin_Country__r?.Name : quote?.Origin_Country__r?.Name;
            }
        }

        @AuraEnabled
        public String originSite {
            get {
                return isPendingMode ? shipment?.Origin_Site__c : quote?.Origin_Site__c;
            }
        }

        @AuraEnabled
        public Decimal shipmentValue {
            get {
                if (isPendingMode) {
                    return shipment?.Shipment_Value__c != null ? shipment.Shipment_Value__c : 0;
                }

                if (quote?.Shipment__c != null) {
                    return quote.Shipment__r?.Shipment_Value__c;
                } else if (quote?.Active_QV_ShipmentValue__c != null) {
                    return quote.Active_QV_ShipmentValue__c;
                } else {
                    return 0;
                }
            }
        }

        @AuraEnabled
        public String shipmentNumber {
            get {
                if (isPendingMode) {
                    return shipment?.CaseNumber;
                }

                if (quote?.Shipment__c != null) {
                    return quote.Shipment__r?.CaseNumber;
                } else {
                    return '';
                }
            }
        }

        @AuraEnabled
        public String shipmentApprovalDetails {
            get {
                if (quote?.Shipment__c != null) {
                    return quote.Shipment__r?.Client_Approval_Details__c;
                } else {
                    return '';
                }
            }
        }

        @AuraEnabled
        public Boolean needsApproval {
            get {
                return quote?.Shipment__r?.Client_Approval_Status__c == FGXEnt_Constants.CLIENT_APPROVAL_STATUS_PENDING_REVIEW ? true : false;
            }
        }

        @AuraEnabled
        public Boolean shipmentApproved {
            get {
                return quote?.Shipment__r?.Client_Approval_Status__c == FGXEnt_Constants.CLIENT_APPROVAL_STATUS_APPROVED ? true : false;
            }
        }

        @AuraEnabled
        public Boolean shipmentRejected {
            get {
                return quote?.Shipment__r?.Client_Approval_Status__c == FGXEnt_Constants.CLIENT_APPROVAL_STATUS_REJECTED ? true : false;
            }
        }

        @AuraEnabled
        public String shipmentId {
            get {
                if (isPendingMode) {
                    return shipment?.Id;
                }

                if (quote?.Shipment__c != null) {
                    return quote.Shipment__c;
                } else {
                    return '';
                }
            }
        }

        @AuraEnabled
        public List<Timeline__c> timelineData {
            get {
                return timeline;
            }
            set {
                timeline = value;
            }
        }

        @AuraEnabled
        public String ownerId {
            get {
                return quote?.OwnerId;
            }
        }

        @AuraEnabled
        public Date targetDeliveryDate {
            get {
                return quote?.Target_Delivery_Date__c;
            }
        }

        @AuraEnabled
        public Map<Id, String> shipByDates {
            get {
                // return map of ship by date for each quote version
                Map<Id, String> dates = new Map<Id, String> ();
                Decimal maxDays = 0;
                if (timelineData != null) {
                    // calculate max business days
                    for (Timeline__c data : timelineData) {
                        maxDays += data.Days_Max__c;
                    }
                }

                for (Quote_Version__c version : quoteVersions) {
                    if (timelineData == null || version?.Target_Delivery_Date__c == null) {
                        dates.put(version.Id, 'No Target Date Set');
                    } else {
                        Decimal businessDayCount = maxDays;
                        Date dateTracker = version.Target_Delivery_Date__c;

                        // calculate whats the latest business day this should ship by given the target date (not counting holidays)
                        while (businessDayCount > 0) {
                            // subtract a day
                            dateTracker = dateTracker.addDays(-1);

                            // check if given day is weekend
                            Integer dayOfWeek = dateTracker.toStartOfWeek().daysBetween(dateTracker);
                            // If not, count it as a day
                            if (dayOfWeek != 0 && dayOfWeek != 6) {
                                businessDayCount--;
                            }
                        }

                        if (dateTracker <= Date.today()) {
                            dates.put(version.Id, 'Check with FGX');
                        } else {
                            // convert to date time so we can convert to string
                            DateTime dtConvert = DateTime.newInstance(dateTracker.year(), dateTracker.month(), dateTracker.day());
                            String formattedDate = dtConvert.format('MM/dd/yyyy');

                            dates.put(version.Id, formattedDate);
                        }
                    }
                }

                return dates;
            }
        }

        @AuraEnabled
        public String summary {
            get {
                return quote?.Summary__c;
            }
        }

        @AuraEnabled
        public List<Quote_Line__c> quoteLines {
            get {
                return qtLines;
            }
            set {
                qtLines = value;
            }
        }

        @AuraEnabled
        public List<Bom_Line__c> bomLines {
            get {
                return bLines;
            }
            set {
                bLines = value;
            }
        }

        @AuraEnabled
        public String owner {
            get {
                if (String.isEmpty(quote?.Owner?.FirstName) || String.isEmpty(quote?.Owner?.LastName))
                    return null;
                return quote.Owner?.FirstName + ' ' + quote.Owner?.LastName;
            }
        }

        private final Map<String, String> displayStatusMap = new Map<String, String>{
                'New' => 'In Draft',
                'Submitted' => 'In Progress',
                'In Process' => 'In Progress',
                'Pending Approval' => 'In Progress',
                'Quoted' => 'Issued',
                'Pending Client' => 'On Hold',
                'Booked' => 'Booked',
                'Lost' => 'Closed',
                'Invoiced' => 'Invoiced'
        };

        @AuraEnabled
        public Boolean isSelected;

        public Quote(Quote__c quote) {
            this.quote = quote;
        }
    }

    public class Navigation implements Comparable {
        @AuraEnabled
        public String target { get; set; }
        @AuraEnabled
        public String label { get; set; }
        @AuraEnabled
        public String subHeader { get; set; }
        @AuraEnabled
        public String pageRefType { get; set; }
        @AuraEnabled
        public String linkType { get; set; }
        @AuraEnabled
        public String icon { get; set; }
        @AuraEnabled
        public Boolean navbarVisible { get; set; }
        @AuraEnabled
        public Decimal sortOrder { get; set; }
        @AuraEnabled
        public List<NavigationItem> subLinks { get; set; }

        public Navigation(FGX_Navigation__mdt navigation) {
            this.target = navigation.Target__c;
            this.label = navigation.Label__c;
            this.subHeader = navigation.Sub_Header__c;
            this.pageRefType = navigation.Page_Ref_Type__c;
            this.linkType = navigation.Link_Type__c;
            this.icon = navigation.Icon__c;
            this.navbarVisible = navigation.Navbar_Visible__c;
            this.sortOrder = navigation.Sort_Order__c;

            if (!navigation.FGX_Navigation_Items__r.isEmpty()) {
                this.subLinks = new List<NavigationItem>();
                for (FGX_Navigation_Item__mdt item : navigation.FGX_Navigation_Items__r) {
                    subLinks.add(new NavigationItem(item));
                }
                this.subLinks.sort();
            }
        }

        public Integer compareTo(Object objToCompare) {
            Navigation nav = (Navigation) objToCompare;
            if (sortOrder == nav.sortOrder) {
                return 0;
            } else if (sortOrder < nav.sortOrder) {
                return -1;
            } else {
                return 1;
            }
        }
    }

    public class NavigationItem implements Comparable {
        @AuraEnabled
        public String target { get; set; }
        @AuraEnabled
        public String label { get; set; }
        @AuraEnabled
        public String subHeader { get; set; }
        @AuraEnabled
        public String pageRefType { get; set; }
        @AuraEnabled
        public String linkType { get; set; }
        @AuraEnabled
        public Decimal sortOrder { get; set; }

        public NavigationItem(FGX_Navigation_Item__mdt navigationItem) {
            this.target = navigationItem.Target__c;
            this.label = navigationItem.Label__c;
            this.subHeader = navigationItem.Sub_Header__c;
            this.pageRefType = navigationItem.Page_Ref_Type__c;
            this.linkType = navigationItem.Link_Type__c;
            this.sortOrder = navigationItem.Sort_Order__c;
        }

        public Integer compareTo(Object objToCompare) {
            NavigationItem navItem = (NavigationItem) objToCompare;

            if (sortOrder == navItem.sortOrder) {
                return 0;
            } else if (sortOrder < navItem.sortOrder) {
                return -1;
            } else {
                return 1;
            }
        }
    }

    public abstract class ResultMsg {

        @AuraEnabled
        public Boolean success;

        @AuraEnabled
        public Integer successCount;

        @AuraEnabled
        public String errorString;

        @AuraEnabled
        public String exceptionString {
            get {
                String value;
                if (e != null) {
                    value = e.getMessage();
                }
                return value;
            }
        }

        public Exception e;

        private List<String> getErrors(List<Database.SaveResult> saveResults) {
            List<String> errorList = new List<String>();
            for (Database.SaveResult sr : saveResults) {
                if (!sr.isSuccess()) {
                    for (Database.Error err : sr.getErrors()) {
                        errorList.add(err.getMessage());
                    }
                }
            }
            return errorList;
        }

        private List<String> getErrors(List<Database.DeleteResult> deleteResult) {
            List<String> errorList = new List<String>();
            for (Database.DeleteResult sr : deleteResult) {
                if (!sr.isSuccess()) {
                    for (Database.Error err : sr.getErrors()) {
                        errorList.add(err.getMessage());
                    }
                }
            }
            return errorList;
        }
    }

    public abstract class ContainerMsg {
    }

    public class WarehouseInbound implements Comparable {
        private Warehouse_Inbound__c inbound;

        @AuraEnabled
        public String Id {
            get {
                return inbound.Id;
            }
        }

        @AuraEnabled
        public String name {
            get {
                return inbound.Name;
            }
        }

        @AuraEnabled
        public String trackingNum {
            get {
                return inbound.Tracking_Number__c;
            }
        }

        @AuraEnabled
        public String status {
            get {
                if (this.inbound?.Status__c == 'Checked In') {
                    for (WarehousePiece piece : pieces) {
                        if (!String.isEmpty(piece.oemPn) || !String.isEmpty(piece.serialNum)) {
                            return 'Manifested';
                        }
                    }
                    return 'Received @ FGX';
                }
                return inbound.Status__c;
            }
        }

        @AuraEnabled
        public String carrierName {
            get {
                return inbound.Carrier_Name__c;
            }
        }

        @AuraEnabled
        public Date deliveryETA {
            get {
                return inbound.ETA_to_FGX__c;
            }
        }

        @AuraEnabled
        public String deliveryETAString {
            get {
                if (inbound.ETA_to_FGX__c != null) {
                    Datetime deliveryDateTime = Datetime.newInstance(inbound.ETA_to_FGX__c.year(), inbound.ETA_to_FGX__c.month(), inbound.ETA_to_FGX__c.day());
                    String formattedDate = deliveryDateTime.format('MM/dd/yy');
                    return formattedDate;
                }
                return '';
            }
        }

        @AuraEnabled
        public String notes {
            get {
                return inbound.Inbound_Notes__c;
            }
        }

        @AuraEnabled
        public String arrivalCountdown {
            get {
                return inbound.Arrival_Countdown__c;
            }
        }

        @AuraEnabled
        public String createdBy {
            get {
                User userCheck = [SELECT Id, UserType FROM User WHERE Id = :inbound.CreatedById];
                if (userCheck.UserType != 'Standard')
                    return 'External';
                return 'Internal';
            }
        }

        @AuraEnabled
        public List<WarehousePiece> pieces;

        public Integer compareTo(Object objToCompare) {
            WarehouseInbound inboundWrapper = (WarehouseInbound) objToCompare;

            Map<String, Integer> statusOrder = new Map<String, Integer>{
                    'Enroute to FGX' => 1,
                    'Received @ FGX' => 2,
                    'Manifested' => 3,
                    'Departed' => 4
            };

            Integer orderThis = statusOrder.containsKey(this.status) ? statusOrder.get(this.status) : 999;
            Integer orderOther = statusOrder.containsKey(inboundWrapper.status) ? statusOrder.get(inboundWrapper.status) : 999;

            return orderThis - orderOther;
        }

        public WarehouseInbound(Warehouse_Inbound__c inbound, List<WarehousePiece> pieces) {
            this.inbound = inbound;
            this.pieces = pieces;
        }
    }

    public class WarehousePiece {
        private Warehouse_Pieces__c piece;

        @AuraEnabled
        public String Id {
            get {
                return piece.Id;
            }
        }

        @AuraEnabled
        public String pieceId {
            get {
                return piece.Piece_ID__c;
            }
        }

        @AuraEnabled
        public String serialNum {
            get {
                return piece.Serial_Number__c;
            }
        }

        @AuraEnabled
        public String type {
            get {
                return piece.Piece_Type__c;
            }
        }

        @AuraEnabled
        public String status {
            get {
                return piece.Piece_Status__c;
            }
        }

        @AuraEnabled
        public String oemPn {
            get {
                return piece.OEM_P_N__c;
            }
        }

        @AuraEnabled
        public Double boxCount {
            get {
                return piece.Box_Count__c;
            }
        }

        public WarehousePiece(Warehouse_Pieces__c piece) {
            this.piece = piece;
        }
    }

    public class FileWrapper {
        @AuraEnabled
        public String title { get; set; }
        @AuraEnabled
        public String content { get; set; }
        @AuraEnabled
        public Id contentDocumentId { get; set; }
        @AuraEnabled
        public Id contentVersionId { get; set; }
        @AuraEnabled
        public Id attachmentId { get; set; }
        @AuraEnabled
        public Id recordId { get; set; }

        public FileWrapper(ContentVersion version) {
            this.title = version.Title;
            this.contentDocumentId = version.ContentDocumentId;
            this.contentVersionId = version.Id;
            if (version.VersionData != null) {
                this.content = EncodingUtil.base64Encode(version.VersionData);
            }
        }

        public FileWrapper(ContentVersion version, Id recordId) {
            this.recordId = recordId;
            this.title = version.Title;
            this.contentDocumentId = version.ContentDocumentId;
            this.contentVersionId = version.Id;
        }

        public FileWrapper(Attachment att) {
            this.title = att.Name;
            this.attachmentId = att.Id;
            this.content = EncodingUtil.base64Encode(att.Body);
        }

        public FileWrapper(Attachment att, Id recordId) {
            this.recordId = recordId;
            this.title = att.Name;
            this.attachmentId = att.Id;
        }
    }
}