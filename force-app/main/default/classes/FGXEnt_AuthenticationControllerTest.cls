/**
 * FGXEnt_AuthenticationControllerTest
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 6/2/24
 */

@IsTest
public with sharing class FGXEnt_AuthenticationControllerTest {

    @TestSetup static void setup() {
        Account acc = (Account) TestDataFactory.createSObject('Account', new Map<String, Object>(), true);
        Contact testContact = TestFactory.createContact('John', 'Smith', '<EMAIL>', acc.Id);
        insert testContact;

        User testUser = TestFactory.createCommunityUser();
        testUser.ContactId = testContact.Id;
        insert testUser;
    }

    @IsTest
    private static void testLogin() {
        User testUser = [SELECT Id, Username FROM User WHERE Email LIKE '%@sfdc%' LIMIT 1];

        String result;
        Test.startTest();
        result = FGXEnt_AuthenticationController.login(testUser.Username, '');
        Test.stopTest();

        System.assertEquals(null, result);
    }

    @IsTest
    private static void testForgotPassword() {
        User testUser = [SELECT Id, Username FROM User WHERE Email LIKE '%@sfdc%' LIMIT 1];

        Boolean result;
        Test.startTest();
        result = FGXEnt_AuthenticationController.forgotPassword(testUser.Username);
        Test.stopTest();

        System.assertEquals(false, result);
    }

    @IsTest
    private static void testChangePassword() {
        Test.startTest();
        String newPassword = 'newPassword';
        String verifyNewPassword = 'verifyNewPassword';
        String oldPassword = 'oldPassword';
        FGXEnt_AuthenticationController.changePassword(newPassword, verifyNewPassword, oldPassword);
        Test.stopTest();
    }

    @IsTest
    private static void testMagicLinkRequest() {
        User testUser = [SELECT Id, Username FROM User WHERE Email LIKE '%@sfdc%' LIMIT 1];

        Boolean result;
        Test.startTest();
        result = FGXEnt_AuthenticationController.magicLinkRequest(testUser.Username, null);
        Test.stopTest();

        System.assertEquals(true, result);
    }

    @IsTest
    private static void testProcessMagicLink() {
        User testUser = [SELECT Id, Username FROM User WHERE Email LIKE '%@sfdc%' LIMIT 1];
        String jwt = FGXEnt_PlatformService.getJwtForUser(testUser.Username);

        String url;
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new TokenMock('{"access_token":"test-access-token"}'));
        url = FGXEnt_AuthenticationController.processMagicLink(jwt, '/');
        Test.stopTest();

        System.assertEquals(true, String.isNotBlank(url));
    }
}