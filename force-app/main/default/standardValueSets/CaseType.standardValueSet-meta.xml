<?xml version="1.0" encoding="UTF-8"?>
<StandardValueSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <sorted>false</sorted>
    <standardValue>
        <fullName>International</fullName>
        <default>false</default>
        <label>International</label>
    </standardValue>
    <standardValue>
        <fullName>Domestic</fullName>
        <default>false</default>
        <label>Domestic</label>
    </standardValue>
    <standardValue>
        <fullName>Billing</fullName>
        <default>false</default>
        <label>Billing</label>
    </standardValue>
    <standardValue>
        <fullName>General</fullName>
        <default>false</default>
        <label>General</label>
    </standardValue>
    <standardValue>
        <fullName>System Issue</fullName>
        <default>false</default>
        <label>System Issue</label>
    </standardValue>
    <standardValue>
        <fullName>Report Damaged Shipment</fullName>
        <default>false</default>
        <label>Report Damaged Shipment</label>
    </standardValue>
    <standardValue>
        <fullName>Report Service Failure</fullName>
        <default>false</default>
        <label>Report Service Failure</label>
    </standardValue>
    <standardValue>
        <fullName>Request Re-Route</fullName>
        <default>false</default>
        <label>Request Re-Route</label>
    </standardValue>
    <standardValue>
        <fullName>Change Service</fullName>
        <default>false</default>
        <label>Change Service</label>
    </standardValue>
    <standardValue>
        <fullName>Tracking Issue</fullName>
        <default>false</default>
        <label>Tracking Issue</label>
    </standardValue>
    <standardValue>
        <fullName>Login Issues</fullName>
        <default>false</default>
        <label>Login Issues</label>
    </standardValue>
    <standardValue>
        <fullName>Portal Question</fullName>
        <default>false</default>
        <label>Portal Question</label>
    </standardValue>
    <standardValue>
        <fullName>NetCourier Question</fullName>
        <default>false</default>
        <label>NetCourier Question</label>
    </standardValue>
    <standardValue>
        <fullName>Other Issue</fullName>
        <default>false</default>
        <label>Other Issue</label>
    </standardValue>
    <standardValue>
        <fullName>Invoice Question</fullName>
        <default>false</default>
        <label>Invoice Question</label>
    </standardValue>
    <standardValue>
        <fullName>Request a Report</fullName>
        <default>false</default>
        <label>Request a Report</label>
    </standardValue>
    <standardValue>
        <fullName>Change Billing Info</fullName>
        <default>false</default>
        <label>Change Billing Info</label>
    </standardValue>
    <standardValue>
        <fullName>Transit Time Question</fullName>
        <default>false</default>
        <label>Transit Time Question</label>
    </standardValue>
    <standardValue>
        <fullName>Packing Question</fullName>
        <default>false</default>
        <label>Packing Question</label>
    </standardValue>
    <standardValue>
        <fullName>Insurance Question</fullName>
        <default>false</default>
        <label>Insurance Question</label>
    </standardValue>
    <standardValue>
        <fullName>General Inquiry</fullName>
        <default>false</default>
        <label>General Inquiry</label>
    </standardValue>
    <standardValue>
        <fullName>Suggestion</fullName>
        <default>false</default>
        <label>Suggestion</label>
    </standardValue>
    <standardValue>
        <fullName>Paperwork Needed</fullName>
        <default>false</default>
        <label>Paperwork Needed</label>
    </standardValue>
    <standardValue>
        <fullName>New Account Setup</fullName>
        <default>false</default>
        <label>New Account Setup</label>
    </standardValue>
    <standardValue>
        <fullName>Status Update</fullName>
        <default>false</default>
        <label>Status Update</label>
    </standardValue>
    <standardValue>
        <fullName>Report Lost Shipment</fullName>
        <default>false</default>
        <label>Report Lost Shipment</label>
    </standardValue>
</StandardValueSet>
