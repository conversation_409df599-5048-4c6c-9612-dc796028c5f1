<?xml version="1.0" encoding="UTF-8"?>
<StandardValueSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <sorted>false</sorted>
    <standardValue>
        <fullName>Booked</fullName>
        <default>true</default>
        <label>Booked</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Enroute to FGX</fullName>
        <default>false</default>
        <label>Enroute to FGX</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Scheduling Pickup</fullName>
        <default>false</default>
        <label>Scheduling Pickup</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Received @ FGX</fullName>
        <default>false</default>
        <label>Received @ FGX</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Crating/Packing</fullName>
        <default>false</default>
        <label>Crating/Packing</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Pending Manifest</fullName>
        <default>false</default>
        <label>Pending Manifest</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Pending Doc Approval</fullName>
        <default>false</default>
        <label>Pending Doc Approval</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Pending Permit</fullName>
        <default>false</default>
        <label>Pending Permit</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Pending Payment</fullName>
        <default>false</default>
        <label>Pending Payment</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>In Transit</fullName>
        <default>false</default>
        <label>In Transit</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Clearance Processing</fullName>
        <default>false</default>
        <label>Clearance Processing</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Permit Processing</fullName>
        <default>false</default>
        <label>Permit Processing</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Out for Delivery</fullName>
        <default>false</default>
        <label>Out for Delivery</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Delivered</fullName>
        <default>false</default>
        <label>Delivered</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>On Hold</fullName>
        <default>false</default>
        <label>On Hold</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Pending Client Action</fullName>
        <default>false</default>
        <label>Pending Client Action</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Quote Request</fullName>
        <default>false</default>
        <label>Quote Request</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Quoted</fullName>
        <default>false</default>
        <label>Quoted</label>
        <closed>false</closed>
    </standardValue>
    <standardValue>
        <fullName>Closed</fullName>
        <default>false</default>
        <label>Closed</label>
        <closed>true</closed>
    </standardValue>
</StandardValueSet>
