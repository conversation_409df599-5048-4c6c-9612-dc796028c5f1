<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>Quote__c</baseObject>
    <category>quotes</category>
    <deployed>true</deployed>
    <description>Summary report for quotes and child objects</description>
    <join>
        <join>
            <outerJoin>false</outerJoin>
            <relationship>Quote_Lines__r</relationship>
        </join>
        <outerJoin>false</outerJoin>
        <relationship>Quote_Versions__r</relationship>
    </join>
    <label>Quote Report</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CurrencyIsoCode</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_QV_CIFValue__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_QV_HWValue__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_QV_PostpayTotal__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_QV_PrepayTotal__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_QV_SWValue__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_QV_ShipmentValue__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_QV_Total__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Active_Version__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Clearance_Agent__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Account__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Contact__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Ref2__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Ref__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Address_Line_1__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Address_Line_2__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Address_Line_3__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_City__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Company_Name__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Country__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_County_Province_Other__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Google_Address__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Location_Type__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Post_Code__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_SFDC_Address__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_Search_Choice__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_State__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination_US_State__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Drop_Ship_to_FGX__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EOR_Needed__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Equipment_Condition__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IOR_Needed__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Opportunity__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Address_Line_1__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Address_Line_2__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Address_Line_3__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_City__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Company_Name__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Country__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_County_Province_Other__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Google_Address__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Location_Type__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Post_Code__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_SFDC_Address__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_Search_Choice__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_State__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin_US_State__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Packing_Configuration__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Packing_Required__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Requested_Service_Level__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Service_Metric__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Short_Description__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Special_Economic_Zone__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Status__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Summary__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Target_Delivery_Date__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Transaction_Type__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Version_Count__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Domestic_Shipment__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Contact_First_Name__c</field>
            <table>Quote__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Output_Client_Name__c</field>
            <table>Quote__c</table>
        </columns>
        <masterLabel>Quotes</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CurrencyIsoCode</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Status__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approval_Submission_Date__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Approved_By__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BOM_Condition__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BOM_Conversion_Date__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BOM_Conversion_Rate__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BOM_Currency_Code__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>BOM_Itemized_Duty_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Billing_Procedure__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CIF_Value__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Chargeable_Wt__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Ref2__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Ref__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Drop_Ship_to_FGX__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>EOR_Needed__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Hardware_Value_Only__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Hardware_Value__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>IOR_Needed__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Include_Insurance__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Itemized_Duties__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Outlay_Fee__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Packing_Type__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>ProgressBarColor__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Expiration__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Output_Insurance__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Rate_Sheet_ID__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Today__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Selected__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Service_Level__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Settings_Override__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipment_Value__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Short_Description__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Show_DT_Processing__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Show_Outlay_Fee__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Software_Value__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Transaction_Type__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Version_Number__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NumPackageLines__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>NumQuoteLines__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Post_Pay_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pre_Pay_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Quote_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_Gross_Wt__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_Volumetric_Wt__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Output_Origin__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Dest_Country_Code__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Orig_Country_Code__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Account__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Output_Dest__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Inv_Today__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Master_Quote_ID__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insight_Logistics_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insight_VAT_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insight_Duty_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Insight_IOR_Total__c</field>
            <table>Quote__c.Quote_Versions__r</table>
        </columns>
        <masterLabel>Quote Versions</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CurrencyIsoCode</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Alert_Message__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Calc_Category__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Calc_Type__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Charge__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Cost__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Description__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Disabled__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Hide__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Icon_Name__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Line_Color__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Markup__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Modifier_Amount__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Modifier_Type__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>No_Markup__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pay_Category__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Output_Category__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RS_Output_Charge__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Rate_Type__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>SortOrder__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Tool_Tip__c</field>
            <table>Quote__c.Quote_Versions__r.Quote_Lines__r</table>
        </columns>
        <masterLabel>Quote Lines</masterLabel>
    </sections>
</ReportType>
