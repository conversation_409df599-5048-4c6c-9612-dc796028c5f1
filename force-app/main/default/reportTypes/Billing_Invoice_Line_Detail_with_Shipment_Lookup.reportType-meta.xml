<?xml version="1.0" encoding="UTF-8"?>
<ReportType xmlns="http://soap.sforce.com/2006/04/metadata">
    <baseObject>Billing_Invoice__c</baseObject>
    <category>other</category>
    <deployed>true</deployed>
    <description>Includes billing invoice header and line detail along with lookups to the shipment (case)</description>
    <join>
        <outerJoin>false</outerJoin>
        <relationship>Billing_Invoice_Lines__r</relationship>
    </join>
    <label>Billing Invoice Line Detail with Shipment Lookup</label>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Owner</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CurrencyIsoCode</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>RecordType</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Amount_Due__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Billing_Address__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CC_Email_To__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Account__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Credit_Amount_Output__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Credit_Amount__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Credit_Desc_Output__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Credit_Description__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Destination__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Has_Notes__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Invoice_Date__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Invoice_Notes__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Invoice_PDF_ID__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Invoice_Total__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Origin__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>PDF_Status__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Paid_in_Full__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Payment_Terms_Output__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Payment_Terms__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Purchase_Order__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Reference__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Send_Status__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Send_to_Email__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sent_Date_Time__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Service_Level__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipment__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Short_Description__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Invoice_Subtotal__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Total_Paid__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AP_System_Name__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>AP_System_Link__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipment__c.Shipment_Value__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Client_Account__c.Id</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipment__c.Origin_Country__c</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipment__c.Origin_Country__c.Id</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Shipment__c.Destination_Country__c.Id</field>
            <table>Billing_Invoice__c</table>
        </columns>
        <masterLabel>Billing Invoices</masterLabel>
    </sections>
    <sections>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Id</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>true</checkedByDefault>
            <field>Name</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CurrencyIsoCode</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedDate</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>CreatedBy</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedDate</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastModifiedBy</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>LastActivityDate</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Amount__c</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Calc_Category__c</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Calc_Type__c</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Description__c</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Pay_Category__c</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <columns>
            <checkedByDefault>false</checkedByDefault>
            <field>Sort__c</field>
            <table>Billing_Invoice__c.Billing_Invoice_Lines__r</table>
        </columns>
        <masterLabel>Billing Invoice Lines</masterLabel>
    </sections>
</ReportType>
