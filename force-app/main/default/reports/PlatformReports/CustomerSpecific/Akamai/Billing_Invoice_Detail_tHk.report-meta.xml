<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <columns>
        <field>Billing_Invoice__c$Client_Account__c</field>
    </columns>
    <columns>
        <field>Billing_Invoice__c$Invoice_Date__c</field>
    </columns>
    <columns>
        <field>Billing_Invoice__c$Name</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Billing_Invoice__c$Invoice_Total__c</field>
    </columns>
    <columns>
        <field>Billing_Invoice__c$Reference__c</field>
    </columns>
    <columns>
        <field>Billing_Invoice__c$Shipment__c</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Billing_Invoice__c$Shipment__c.Shipment_Value__c</field>
    </columns>
    <columns>
        <field>Billing_Invoice__c$Origin__c</field>
    </columns>
    <columns>
        <field>Billing_Invoice__c$Destination__c</field>
    </columns>
    <columns>
        <field>Billing_Invoice__c.Billing_Invoice_Lines__r$Description__c</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>Billing_Invoice__c.Billing_Invoice_Lines__r$Amount__c</field>
    </columns>
    <currency>USD</currency>
    <filter>
        <criteriaItems>
            <column>Billing_Invoice__c$Client_Account__c.Id</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>Billing_Invoice__c$Shipment__c.Origin_Country__c.Id</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>Billing_Invoice__c$Shipment__c.Destination_Country__c.Id</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value></value>
        </criteriaItems>
        <criteriaItems>
            <column>Billing_Invoice__c$Reference__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value></value>
        </criteriaItems>
    </filter>
    <format>Tabular</format>
    <name>Billing Invoice Detail</name>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>Billing_Invoice_Line_Detail_with_Shipment_Lookup__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>Billing_Invoice__c$Invoice_Date__c</dateColumn>
        <interval>INTERVAL_CUSTOM</interval>
    </timeFrameFilter>
</Report>
