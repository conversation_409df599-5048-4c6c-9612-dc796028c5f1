<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Get_Action_Description_Plain_Text</name>
        <label>Get Action Description Plain Text</label>
        <locationX>242</locationX>
        <locationY>384</locationY>
        <actionName>RemoveHtmlMarkup</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Notify_Action_Item_Resolved_by_Client</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>richText</name>
            <value>
                <elementReference>$Record.Request_Description__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>RemoveHtmlMarkup</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>HtmlStripedDescription</assignToReference>
            <name>output</name>
        </outputParameters>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <name>Notify_Action_Item_Resolved_by_Client</name>
        <label>Notify Action Item Resolved by Client</label>
        <locationX>242</locationX>
        <locationY>492</locationY>
        <actionName>slackPostMessage</actionName>
        <actionType>slackPostMessage</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>slackAppIdForToken</name>
            <value>
                <stringValue>A03269G3DNE</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackWorkspaceIdForToken</name>
            <value>
                <stringValue>TN2PXQBBR</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackConversationId</name>
            <value>
                <elementReference>$Record.Case__r.Slack_Channel__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackMessage</name>
            <value>
                <elementReference>SlackMessageOnResolved</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>slackPostMessage</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <decisions>
        <name>Has_Slack_Channel</name>
        <label>Has Slack Channel</label>
        <locationX>374</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Case__r.Slack_Channel__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Action_Description_Plain_Text</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>RecordLink</name>
        <dataType>String</dataType>
        <expression>LEFT($Api.Partner_Server_URL_260, FIND( &apos;/services&apos;, $Api.Partner_Server_URL_260)) &amp; {!$Record.Id}</expression>
    </formulas>
    <interviewLabel>Action Item After Update: Action Item Resolved by Client Slack Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Action Item After Update: Action Item Resolved by Client Slack Notification</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>1 AND (2 OR 3)</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending New Record</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending FGX Review</stringValue>
            </value>
        </filters>
        <object>Action_Item__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Has_Slack_Channel</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>SlackMessageOnResolved</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>:rotating_light: Action Item Resolved By Client :rotating_light: 
*Type:* {!$Record.RecordType.Name}
*Request Title:* {!$Record.Request_Title__c}
*Request Description:* {!HtmlStripedDescription}
*Resolved By:* {!$User.FirstName} {!$User.LastName}
*Client Reply:* {!$Record.Client_Reply__c}
*Priority:* {!$Record.Priority__c}
{!RecordLink}</text>
    </textTemplates>
    <variables>
        <name>HtmlStripedDescription</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
