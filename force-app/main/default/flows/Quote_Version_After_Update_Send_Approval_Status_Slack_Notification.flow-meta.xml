<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Approval_Pending_Slack_Message</name>
        <label>Send Approval Pending Slack Message</label>
        <locationX>143</locationX>
        <locationY>492</locationY>
        <actionName>slackPostMessage</actionName>
        <actionType>slackPostMessage</actionType>
        <connector>
            <targetReference>Approval_Status_Approved</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>slackAppIdForToken</name>
            <value>
                <stringValue>A03269G3DNE</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackWorkspaceIdForToken</name>
            <value>
                <stringValue>TN2PXQBBR</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackConversationId</name>
            <value>
                <elementReference>$Record.Quote__r.Shipment__r.Slack_Channel__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackMessage</name>
            <value>
                <elementReference>SlackMessageApprovalStatusSubmitted</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>slackPostMessage</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Send_Approved_Slack_Message</name>
        <label>Send Approved Slack Message</label>
        <locationX>143</locationX>
        <locationY>792</locationY>
        <actionName>slackPostMessage</actionName>
        <actionType>slackPostMessage</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>slackAppIdForToken</name>
            <value>
                <stringValue>A03269G3DNE</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackWorkspaceIdForToken</name>
            <value>
                <stringValue>TN2PXQBBR</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackConversationId</name>
            <value>
                <elementReference>$Record.Quote__r.Shipment__r.Slack_Channel__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackMessage</name>
            <value>
                <elementReference>SlackMessageApprovalStatusApprovedMessage</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>slackPostMessage</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <decisions>
        <name>Approval_Status_Approved</name>
        <label>Approval Status: Approved</label>
        <locationX>275</locationX>
        <locationY>684</locationY>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Approval_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Approved</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Approved_Slack_Message</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <decisions>
        <name>Approval_Status_Pending_Approval</name>
        <label>Approval Status: Pending Approval</label>
        <locationX>275</locationX>
        <locationY>384</locationY>
        <defaultConnector>
            <targetReference>Approval_Status_Approved</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Approval_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending Approval</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Approval_Pending_Slack_Message</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <decisions>
        <name>Related_To_Shipment</name>
        <label>Related To Shipment?</label>
        <locationX>473</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Quote__r.Shipment__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Quoted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Approval_Status_Pending_Approval</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>RecordLink</name>
        <dataType>String</dataType>
        <expression>LEFT($Api.Partner_Server_URL_260, FIND( &apos;/services&apos;, $Api.Partner_Server_URL_260)) &amp; {!$Record.Id}</expression>
    </formulas>
    <interviewLabel>Quote Version After Update: Send Approval Status Slack Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Quote Version After Update: Send Approval Status Slack Notification</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>(1 OR 2) AND 3</filterLogic>
        <filters>
            <field>Approval_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending Approval</stringValue>
            </value>
        </filters>
        <filters>
            <field>Approval_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </filters>
        <filters>
            <field>Approval_Status__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Quote_Version__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Related_To_Shipment</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>SlackMessageApprovalStatusApprovedMessage</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Record.Name} Approved :white_check_mark:
Version #: {!$Record.Version_Number__c}
Shipment Value: {!$Record.Shipment_Value__c}
Origin: {!$Record.RS_Output_Origin__c}
Destination: {!$Record.RS_Output_Dest__c}
{!RecordLink}</text>
    </textTemplates>
    <textTemplates>
        <name>SlackMessageApprovalStatusSubmitted</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Record.Name} Submitted for Approval :hourglass:
Version #: {!$Record.Version_Number__c}
Shipment Value: {!$Record.Shipment_Value__c}
Origin: {!$Record.RS_Output_Origin__c}
Destination: {!$Record.RS_Output_Dest__c}
{!RecordLink}</text>
    </textTemplates>
</Flow>
