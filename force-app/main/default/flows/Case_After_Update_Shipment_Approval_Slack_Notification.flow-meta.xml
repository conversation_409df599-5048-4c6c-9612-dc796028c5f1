<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Approval_Slack_Notification</name>
        <label>Send Approval Slack Notification</label>
        <locationX>176</locationX>
        <locationY>384</locationY>
        <actionName>slackPostMessage</actionName>
        <actionType>slackPostMessage</actionType>
        <faultConnector>
            <targetReference>Create_Error_Log_Slack_Notification</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>slackAppIdForToken</name>
            <value>
                <stringValue>A03269G3DNE</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackWorkspaceIdForToken</name>
            <value>
                <stringValue>TN2PXQBBR</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackConversationId</name>
            <value>
                <elementReference>$Record.Slack_Channel__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>slackMessage</name>
            <value>
                <elementReference>SlackApprovalMessage</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>slackPostMessage</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <decisions>
        <name>Has_Slack_Channel</name>
        <label>Has Slack Channel?</label>
        <locationX>440</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Slack_Channel__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Approval_Slack_Notification</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>RecordLink</name>
        <dataType>String</dataType>
        <expression>LEFT($Api.Partner_Server_URL_260, FIND( &apos;/services&apos;, $Api.Partner_Server_URL_260)) &amp; {!$Record.Id}</expression>
    </formulas>
    <interviewLabel>Case After Update: Shipment Approval Slack Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case After Update: Shipment Approval Slack Notification</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Error_Log_Slack_Notification</name>
        <label>Create Error Log Slack Notification</label>
        <locationX>440</locationX>
        <locationY>492</locationY>
        <inputAssignments>
            <field>ClassName__c</field>
            <value>
                <stringValue>Case After Update: Shipment Approval Slack Notification</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Message__c</field>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ObjectIDs__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ObjectName__c</field>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </inputAssignments>
        <object>Log__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Client_Approval_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </filters>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Has_Slack_Channel</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>SlackApprovalMessage</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>:white_check_mark: Booking Approved - Please proceed with processing! :white_check_mark:
{!RecordLink}</text>
    </textTemplates>
</Flow>
