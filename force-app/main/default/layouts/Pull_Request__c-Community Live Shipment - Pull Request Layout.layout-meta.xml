<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>ChangeOwnerOne</excludeButtons>
    <excludeButtons>ChangeRecordType</excludeButtons>
    <excludeButtons>Clone</excludeButtons>
    <excludeButtons>Delete</excludeButtons>
    <excludeButtons>Edit</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>RecordShareHierarchy</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Account_Formula__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Requestor__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Date_Submitted__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Request Status</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Status__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Destination Address</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_SFDC_Address__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Company_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Address_Line_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Address_Line_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Address_Line_3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_City__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_State__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Post_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_US_State__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_County_Province_Other__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Country__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Tax_Id_c__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Location_Type__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Last_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_First_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Mobile_Phone__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Office_Phone__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Destination_Office_Phone_Extension__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Service Options</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Insurance_Required__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>IOR_Needed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Special_Economic_Zone__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Service_Metric__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Service_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Target_Delivery_Date__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Request Summary</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Request_Summary__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>Related_Shipment__c</fields>
        <fields>RECORDTYPE</fields>
        <fields>Status__c</fields>
        <fields>Unit_Manufacturer__c</fields>
        <fields>Unit_Part_Number__c</fields>
        <fields>Quantity__c</fields>
        <fields>Unit_Value__c</fields>
        <fields>Serial_Number__c</fields>
        <fields>Condition__c</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>Asset_Unit__c.Pull_Request__c</relatedList>
        <sortField>CREATED_DATE</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MassChangeOwner</excludeButtons>
        <excludeButtons>New</excludeButtons>
        <fields>Num_Pcs__c</fields>
        <fields>Packing_Type__c</fields>
        <fields>Length__c</fields>
        <fields>Width__c</fields>
        <fields>Height__c</fields>
        <fields>Length_Unit__c</fields>
        <fields>Unit_Weight__c</fields>
        <fields>Weight_Unit__c</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>Pull_Request_Package_Line__c.Pull_Request__c</relatedList>
        <sortField>CREATED_DATE</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h6Q00000FaZnm</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
