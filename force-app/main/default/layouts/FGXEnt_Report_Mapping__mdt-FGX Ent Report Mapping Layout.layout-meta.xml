<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>MasterLabel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DeveloperName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Report_Id__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Report_Object_Api_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Report_Object_Label__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Report_Visibility__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Account_Filter_Field__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Account_Filter_Id__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Report_Icon_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Download_Relevant_Files__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Report_Id_Field_Column_Name__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsProtected</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NamespacePrefix</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>MasterLabel</fields>
        <fields>DeveloperName</fields>
        <fields>NamespacePrefix</fields>
        <fields>Assigned_Field_Name__c</fields>
        <fields>Type__c</fields>
        <fields>Output_Datatype_Picklist__c</fields>
        <fields>Visible__c</fields>
        <relatedList>FGXEnt_Report_Mapping_Item__mdt.Parent_Report_Mapping__c</relatedList>
        <sortField>Type__c</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hE2000008YdlX</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
