<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>ChangeOwnerOne</excludeButtons>
    <excludeButtons>ContactHierarchy</excludeButtons>
    <excludeButtons>CreateCallList</excludeButtons>
    <excludeButtons>CreateSurveyInvitation</excludeButtons>
    <excludeButtons>DisableCustomerPortal</excludeButtons>
    <excludeButtons>EnableCustomerPortal</excludeButtons>
    <excludeButtons>LoginToNetworkAsUser</excludeButtons>
    <excludeButtons>LoginToPortalAsUser</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>RequestUpdate</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <excludeButtons>StartOutboundConversation</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <excludeButtons>ViewCustomerPortal</excludeButtons>
    <excludeButtons>XClean</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Contact Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Company__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MailingAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Place_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MetafourAddressValidated__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Default_Collection_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AddressReadOnly__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Delivery_Requirements__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns>
            <layoutItems>
                <customLink>GoogleSearch</customLink>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <customLink>GoogleMaps</customLink>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <customLink>YahooWeather</customLink>
            </layoutItems>
        </layoutColumns>
        <style>CustomLinks</style>
    </layoutSections>
    <miniLayout>
        <fields>Name</fields>
        <fields>AccountId</fields>
        <fields>Company__c</fields>
        <fields>MailingAddress</fields>
        <fields>Phone</fields>
        <fields>Email</fields>
    </miniLayout>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <component>runtime_sales_social:socialPanel</component>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.AMOUNT</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>CASES.STATUS</fields>
        <fields>CASES.SUBJECT</fields>
        <fields>Shipment_Value__c</fields>
        <fields>From__c</fields>
        <fields>To__c</fields>
        <fields>CASES.CREATED_DATE</fields>
        <fields>OWNER_NAME</fields>
        <relatedList>RelatedCaseList</relatedList>
        <sortField>CASES.CREATED_DATE</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>Owner</fields>
        <fields>StartTime</fields>
        <fields>EndTime</fields>
        <fields>Status</fields>
        <relatedList>RelatedLiveChatTranscriptList</relatedList>
    </relatedLists>
    <relatedObjects>AccountId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h6Q00000Q4C8q</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
