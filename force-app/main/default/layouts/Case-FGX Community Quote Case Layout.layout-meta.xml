<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <customButtons>Cancel_Quote</customButtons>
    <excludeButtons>Clone</excludeButtons>
    <excludeButtons>CloseCase</excludeButtons>
    <excludeButtons>Delete</excludeButtons>
    <excludeButtons>Edit</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Shipment</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Shipment_Notes__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Booking_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FGX_Service_Level__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CurrencyIsoCode</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Geography</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Packaging and Contents</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Contents_Description__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Weight__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>IATA_Contains_Batteries__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Customer Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ContactId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ContactPhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SuppliedEmail</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Origin</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Priority</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <quickActionList/>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>ContactId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <relatedList>RelatedCommentsList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedAttachmentList</relatedList>
    </relatedLists>
    <relatedObjects>ContactId</relatedObjects>
    <relatedObjects>AccountId</relatedObjects>
    <runAssignmentRulesDefault>true</runAssignmentRulesDefault>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showKnowledgeComponent>false</showKnowledgeComponent>
    <showRunAssignmentRulesCheckbox>true</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>true</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h6000000DvbJN</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
