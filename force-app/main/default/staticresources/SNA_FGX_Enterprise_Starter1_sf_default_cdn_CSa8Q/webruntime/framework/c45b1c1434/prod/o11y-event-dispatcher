!function(){"use strict";class e{static enableDebug(e){this._debug=e}static log(e){this._debug&&console.log(`ExpTagManager: ${e}`)}static warn(e){this._debug&&console.warn(`ExpTagManager: ${e}`)}static error(e){this._debug&&console.error(`ExpTagManager: ${e}`)}}e._debug=!1;const t=["cart-add","cart-remove","cart-replace","cart-update","cart-view","checkout-begin","checkout-contact-info","checkout-user-register","checkout-shipping-address","checkout-billing-address","checkout-shipping-options","checkout-payment","checkout-apply-coupon","checkout-review","checkout-submit"];
/*! js-cookie v3.0.5 | MIT */
function r(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)e[n]=r[n]}return e}!function e(t,n){function i(e,i,s){if("undefined"!=typeof document){"number"==typeof(s=r({},n,s)).expires&&(s.expires=new Date(Date.now()+864e5*s.expires)),s.expires&&(s.expires=s.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var a in s)s[a]&&(o+="; "+a,!0!==s[a]&&(o+="="+s[a].split(";")[0]));return document.cookie=e+"="+t.write(i,e)+o}}return Object.create({set:i,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var r=document.cookie?document.cookie.split("; "):[],n={},i=0;i<r.length;i++){var s=r[i].split("="),o=s.slice(1).join("=");try{var a=decodeURIComponent(s[0]);if(n[a]=t.read(o,a),e===a)break}catch(e){}}return e?n[e]:n}},remove:function(e,t){i(e,"",r({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,r({},this.attributes,t))},withConverter:function(t){return e(r({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const n="AppendRename";const i="AppendName";const s={[n]:new class{transform(t,r,n){const i=t.fromKey;if(!Object.prototype.hasOwnProperty.call(r,i))return void e.error(`No field of given name exists to rename does not have field: ${i}`);const s=`${r[i].charAt(0).toLowerCase()}${r[i].slice(1)}`;Object.assign(n,{...n,...Object.fromEntries(Object.entries(r).filter((e=>e[0]!==i)).map((([e,t])=>[`${s}${e.charAt(0).toUpperCase()}${e.slice(1)}`,t])))})}},[i]:new class{transform(e,t,r){const n=t=>{if(!Object.prototype.hasOwnProperty.call(t,e.key))return t;const{[e.key]:r,...n}=t;return delete t[e.key],Object.assign({},n,{[e.name]:r})},i=e=>(e=n(e),Object.prototype.hasOwnProperty.call(e,"attributes")&&(e.attributes=n(e.attributes)),e);if(!Array.isArray(t))return Object.assign(t,i(t));Object.assign(t,t.map(i))}}},o=(e,t="id")=>({type:i,key:t,name:e}),a={cart:{schema:{namespace:"sf.commerce",name:"Cart",pbjsSchema:{nested:{sf:{nested:{commerce:{nested:{Cart:{fields:{coupon:{id:104,type:"string"},totalProductsAmount:{id:105,type:"double"},orderId:{id:108,type:"string"},sourceChannel:{id:4,type:"string"},channelType:{id:6,type:"string"},uniqueProductCount:{id:106,type:"uint32"},webstoreId:{id:2,type:"string"},name:{id:101,type:"string"},subscriptionQuantity:{id:107,type:"double"},eventName:{id:1,type:"string"},correlationId:{id:5,type:"string"},currency:{id:103,type:"string"},id:{id:102,type:"string"},visitorId:{id:3,type:"string"}}}}}}}}}},lineItems:{schema:{namespace:"sf.commerce",name:"CartItem",pbjsSchema:{nested:{sf:{nested:{commerce:{nested:{CartItem:{fields:{totalProductAmount:{id:106,type:"double"},quantity:{id:104,type:"double"},productId:{id:107,type:"string"},lineItemId:{id:102,type:"string"},sourceChannel:{id:4,type:"string"},adjustedTotalProductAmount:{id:110,type:"double"},cartId:{id:101,type:"string"},saleType:{id:112,type:"string"},channelType:{id:6,type:"string"},webstoreId:{id:2,type:"string"},price:{id:103,type:"double"},imageUrl:{id:111,type:"string"},name:{id:105,type:"string"},eventName:{id:1,type:"string"},correlationId:{id:5,type:"string"},sku:{id:109,type:"string"},productType:{id:108,type:"string"},visitorId:{id:3,type:"string"}}}}}}}}}},plugins:[o("lineItemId")],catalogObject:{plugins:[{fromKey:"type",type:n}]}}}},c={cart:{schema:{namespace:"sf.commerce",name:"Checkout",pbjsSchema:{nested:{sf:{nested:{commerce:{nested:{Checkout:{fields:{uiLayoutType:{id:103,type:"string"},sourceChannel:{id:4,type:"string"},cartId:{id:101,type:"string"},eventName:{id:1,type:"string"},correlationId:{id:5,type:"string"},channelType:{id:6,type:"string"},isExpressCheckout:{id:105,type:"bool"},checkoutId:{id:102,type:"string"},initialOrn:{id:104,type:"string"},webstoreId:{id:2,type:"string"},visitorId:{id:3,type:"string"},isManaged:{id:106,type:"bool"}}}}}}}}}},plugins:[o("cartId")]}},d={cart:{schema:{namespace:"sf.commerce",name:"Payment",pbjsSchema:{nested:{sf:{nested:{commerce:{nested:{Payment:{fields:{sourceChannel:{id:4,type:"string"},cartId:{id:101,type:"string"},channelType:{id:6,type:"string"},initialOrn:{id:105,type:"string"},webstoreId:{id:2,type:"string"},isManualCapture:{id:106,type:"bool"},paymentMethods:{rule:"repeated",id:107,type:"string"},eventName:{id:1,type:"string"},paymentMethod:{id:104,type:"string"},correlationId:{id:5,type:"string"},isExpressPayment:{id:103,type:"bool"},checkoutId:{id:102,type:"string"},visitorId:{id:3,type:"string"}}}}}}}}}},plugins:[o("cartId")]}},p={order:{schema:{namespace:"sf.commerce",name:"Order",pbjsSchema:{nested:{sf:{nested:{commerce:{nested:{Order:{fields:{totalProductAmount:{id:104,type:"double"},orderId:{id:101,type:"string"},sourceChannel:{id:4,type:"string"},cartId:{id:103,type:"string"},grandTotalAmount:{id:105,type:"double"},eventName:{id:1,type:"string"},correlationId:{id:5,type:"string"},channelType:{id:6,type:"string"},currency:{id:102,type:"string"},webstoreId:{id:2,type:"string"},visitorId:{id:3,type:"string"}}}}}}}}}},plugins:[o("orderId"),o("grandTotalAmount","adjustedTotalProductAmount")]}},l=new Map([...t.filter((e=>e.startsWith("cart"))).map((e=>[e,a])),...t.filter((e=>e.startsWith("checkout"))).map((e=>[e,c])),...["checkout-payment","checkout-payment-render"].map((e=>[e,d])),["order-accepted",p]]);class u{constructor(e){this.registryMap=new Map,e&&(this.registryMap=e)}addTransformer(e,t){this.registryMap.set(e,t)}hasEvent(e){return this.registryMap.has(e)}getTransformer(e){return this.registryMap.get(e)}}const y=t=>{var r;for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){if("fields"===e){const n=t[e],i={};for(const e in n)if(Object.prototype.hasOwnProperty.call(n,e)){const t=n[e].type,s=null!==(r=n[e].rule)&&void 0!==r?r:void 0;i[e]="repeated"===s?t+"[]":"uint32"===t?"number":t}return i}if("object"==typeof t[e]&&null!==t[e]){const r=y(t[e]);if(void 0!==r)return r}}return e.error("Invalid schema passed: "+t),{}},m=(e,t)=>Object.fromEntries(Object.entries(e).filter((([r,n])=>{if(Object.prototype.hasOwnProperty.call(t,r)){if(t[r].includes("[]")){const e=t[r].substring(0,t[r].length-2);return!(!Array.isArray(n)||0!==n.filter((t=>typeof t!==e)).length)}return typeof e[r]===(e=>{switch(e){case"double":case"float":case"int32":case"int64":case"uint32":case"uint64":case"sint32":case"sint64":case"fixed32":case"fixed64":case"sfixed32":case"sfixed64":return"number";case"bool":return"boolean";case"string":return"string";case"bytes":return"Uint8Array";default:return e}})(t[r])}return!1}))),g=(e,t="",r={})=>{for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){const i=t?`${t}.${n}`:n;"object"!=typeof e[n]||null===e[n]||Array.isArray(e[n])?Object.prototype.hasOwnProperty.call(r,n)||(r[n]=e[n]):g(e[n],i,r)}return r},h=e=>{if("object"!=typeof e||null===e)return null;if("schema"in e)return e.schema;for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&!Array.isArray(e[t])){const r=h(e[t]);if(null!==r)return r}return null};class f{constructor(e){this._registry=e}transform(e,t){const r=((e,t)=>{var r,n,i;const s=null!==(r=e.cart)&&void 0!==r?r:{},o=t.guestUUID,{source:a,site:c}=t,{correlationId:d}=null!==(n=null==s?void 0:s.attributes)&&void 0!==n?n:{},{channel:p}=null!=a?a:{};return{correlationId:d,eventName:e.name,sourceChannel:p,visitorId:o,webstoreId:null===(i=null==c?void 0:c.webstore)||void 0===i?void 0:i.id,channelType:null==c?void 0:c.templateDevName}})(e,t),n=this.getRegistryEvents(e);return(0==n.length?[{schema:h(this._registry),payload:r}]:n).map((e=>{const t=Object.entries({...e.payload,...r}).filter((([e,t])=>void 0!==t)).reduce(((e,[t,r])=>({...e,[t]:r})),{});return{schema:e.schema,payload:t}}))}getRegistryEvents(e){const t=[],r=(e,n)=>{const i={},o=[];Object.prototype.hasOwnProperty.call(n,"plugins")&&n.plugins.map((t=>s[t.type].transform(t,e,void 0)));for(const t in e)"object"==typeof e[t]&&"attributes"!==t?o.push({k:t,o:e[t]}):null!==e[t]&&("attributes"===t?Object.assign(i,e.attributes):i[t]=e[t]);if(o.forEach((e=>{const t=Object.prototype.hasOwnProperty.call(n,e.k)?n[e.k]:void 0,o=n[e.k];if(o&&Object.prototype.hasOwnProperty.call(o,"plugins")&&o.plugins.map((t=>s[t.type].transform(t,e.o,i))),t)Object.prototype.hasOwnProperty.call(t,"schema")&&(Array.isArray(e.o)?e.o.forEach((e=>r(e,t))):r(e.o,t));else{const t=g(e.o);Object.keys(t).filter((e=>!Object.prototype.hasOwnProperty.call(i,e))).forEach((e=>{i[e]=t[e]}))}})),Object.prototype.hasOwnProperty.call(n,"schema")){const e=y(n.schema);t.push({schema:n.schema,payload:m(i,e)})}};return r(e,this._registry),t}}var b;null===(b=window.ECEngmtEvtDispatchers)||void 0===b||b.push({dispatcherName:"O11Y",dispatcher:new class{constructor(){this.registry=new u(l)}send(t,r){if(!window.WEBSDK||!window.WEBSDK.sendEngagementEvent)return void e.error("No o11y sendEvent method in window.");const{interactions:n,...i}=t;e.log(`Received:\ninteractions=${JSON.stringify(n)}`),n.filter((e=>e&&e.name)).forEach((t=>{if(!this.registry.hasEvent(t.name))return void e.error(`Unrecognized interaction event ${t.name}`);const r=this.registry.getTransformer(t.name);let n;n=r.transform?r.transform(t,i):new f(r).transform(t,i),n.forEach((e=>window.WEBSDK.sendEngagementEvent(e.schema,e.payload)))}))}}})}();
