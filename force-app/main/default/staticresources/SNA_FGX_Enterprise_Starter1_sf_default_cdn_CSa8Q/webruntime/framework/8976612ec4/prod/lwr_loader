/**
* Copyright (c) 2021, salesforce.com, inc.
* All rights reserved.
* SPDX-License-Identifier: MIT
* For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/MIT
*/
/* LWR Legacy Module Loader Shim v0.11.9 */
!function(){"use strict";var e=function(e){return e[e.Start=0]="Start",e[e.End=1]="End",e}(e||{});let t;function r(e){t=e}const o=globalThis.performance,s=void 0!==o&&"function"==typeof o.mark&&"function"==typeof o.clearMarks&&"function"==typeof o.measure&&"function"==typeof o.clearMeasures;function n(e,t){return t?`${e}-${t}`:e}function i(e,t,r){const o=n(e,t);return t&&r?`${o}_${r}`:o}function a(e,t){const r=e||t?{...t}:null;return r&&e&&(r.specifier=e),r}function l({id:r,specifier:n,specifierIndex:l,metadata:d}){if(t)t({id:r,phase:e.Start,specifier:n,metadata:d});else if(s){const e=i(r,n,l),t=a(n,d);o.mark(e,{detail:t})}}function d({id:r,specifier:l,specifierIndex:d,metadata:c}){if(t)t({id:r,phase:e.End,specifier:l,metadata:c});else if(s){const e=i(r,l,d),t=n(r,l),s=a(l,c);o.measure(t,{start:e,detail:s}),o.clearMarks(e),o.clearMeasures(t)}}function c(e,t,o,s){const{autoBoot:n,customInit:i}=e;if(function(e,t){if(!e&&!t)throw new Error("The customInit hook is required when autoBoot is false");if(e&&t)throw new Error("The customInit hook must not be defined when autoBoot is true")}(n,i),i){i({initializeApp:t,define:o,onBootstrapError:s,attachDispatcher:r},e)}}const u="function"==typeof setTimeout,p="undefined"!=typeof console;class f{__init(){this.defineCache={}}__init2(){this.orderedDefs=[]}constructor(e){f.prototype.__init.call(this),f.prototype.__init2.call(this),u&&(this.watchdogTimerId=this.startWatchdogTimer()),this.global=e,this.config=e.LWR,this.loaderModule="lwr/loaderLegacy/v/0_11_9",this.errorHandler=this.config.onError;const t=this.tempDefine.bind(this);e.LWR.define=t,this.bootReady=this.config.autoBoot;try{this.createProfilerModule(this.config),c(Object.freeze(this.config),this.postCustomInit.bind(this),t,(e=>{this.errorHandler=e}))}catch(e){this.enterErrorState(e)}}canInit(){const e=this.config.requiredModules.every((e=>this.orderedDefs.includes(e)));return this.bootReady&&e}tempDefine(...e){const t=e[0];this.defineCache[t]=e,this.orderedDefs.push(t),this.canInit()&&(u&&clearTimeout(this.watchdogTimerId),this.initApp())}postCustomInit(){this.bootReady=!0,this.canInit()&&this.initApp()}initApp(){try{const e={baseUrl:this.config.baseUrl,profiler:{logOperationStart:l,logOperationEnd:d},serverData:this.config.serverData,appMetadata:{appId:this.config.appId,bootstrapModule:this.config.bootstrapModule,rootComponent:this.config.rootComponent,rootComponents:this.config.rootComponents}},t=function(e,t,r,o){if(!t||"function"!=typeof t[2])throw new Error(`Expected loader with specifier "${e}" to be a module`);const s={};t[2].call(null,s);const{Loader:n}=s,i=new n(r);return o&&o.length&&i.registerExternalModules(o),i.define(e,["exports"],(e=>{Object.assign(e,{define:i.define.bind(i),load:i.load.bind(i),services:i.services})}),t[3]),i}(this.loaderModule,this.defineCache[this.loaderModule],e,this.config.preloadModules);this.mountApp(t)}catch(e){this.enterErrorState(e)}}waitForDOMContentLoaded(){return void 0===typeof document||"interactive"===document.readyState||"complete"===document.readyState?Promise.resolve():new Promise((e=>{document.addEventListener("DOMContentLoaded",(()=>{e()}))}))}createProfilerModule(e){e.define("lwr/profiler/v/0_11_9",["exports"],(e=>{Object.assign(e,{logOperationStart:l,logOperationEnd:d})}),{})}mountApp(e){const{bootstrapModule:t,rootComponent:r,importMappings:o,rootComponents:s,serverData:n,endpoints:i}=this.config;this.global.LWR=Object.freeze({define:e.define.bind(e),rootComponent:r,rootComponents:s,serverData:n,importMappings:o,endpoints:i}),this.orderedDefs.forEach((t=>{t!==this.loaderModule&&e.define(...this.defineCache[t])}));const{disableInitDefer:a}=this.config;e.registerImportMappings(o).then((()=>{if(!a)return this.waitForDOMContentLoaded()})).then((()=>e.load(t))).catch((e=>{this.enterErrorState(new Error(`Application ${r||t} could not be loaded: ${e}`))}))}enterErrorState(e){l({id:"lwr.bootstrap.error"}),this.errorHandler?this.errorHandler(e):p&&console.error(`An error occurred during LWR bootstrap. ${e.message}`,e.stack)}startWatchdogTimer(){return setTimeout((()=>{this.enterErrorState(new Error("Failed to load required modules - timed out"))}),6e4)}}const h=globalThis;h.LWR.requiredModules=h.LWR.requiredModules||[],h.LWR.requiredModules.indexOf("lwr/loaderLegacy/v/0_11_9")<0&&h.LWR.requiredModules.push("lwr/loaderLegacy/v/0_11_9"),new f(h)}(),LWR.define("lwr/loaderLegacy/v/0_11_9",["exports"],(function(exports){"use strict";const templateRegex=/\{([0-9]+)\}/g;function templateString(e,t){return e.replace(templateRegex,((e,r)=>t[r]))}function generateErrorMessage(e,t){const r=Array.isArray(t)?templateString(e.message,t):e.message;return`LWR${e.code}: ${r}`}class LoaderError extends Error{constructor(e,t){super(),this.message=generateErrorMessage(e,t)}}function invariant(e,t){if(!e)throw new LoaderError(t)}const MISSING_NAME=Object.freeze({code:3e3,message:"A module name is required.",level:0}),FAIL_INSTANTIATE=Object.freeze({code:3004,message:"Failed to instantiate module: {0}",level:0}),NO_AMD_REQUIRE=Object.freeze({code:3005,message:"AMD require not supported.",level:0}),FAILED_DEP=Object.freeze({code:3006,level:0,message:"Failed to load dependency: {0}"}),INVALID_DEPS=Object.freeze({code:3007,message:"Unexpected value received for dependencies argument; expected an array.",level:0}),FAIL_LOAD=Object.freeze({code:3008,level:0,message:"Error loading {0}"}),UNRESOLVED=Object.freeze({code:3009,level:0,message:"Unable to resolve bare specifier: {0}"}),NO_BASE_URL=Object.freeze({code:3010,level:0,message:"baseUrl not set"});Object.freeze({code:3011,level:0,message:"Cannot set a loader service multiple times"});const INVALID_HOOK=Object.freeze({code:3012,level:0,message:"Invalid hook received"}),INVALID_LOADER_SERVICE_RESPONSE=Object.freeze({code:3013,level:0,message:"Invalid response received from hook"}),MODULE_LOAD_TIMEOUT=Object.freeze({code:3014,level:0,message:"Error loading {0} - timed out"}),HTTP_FAIL_LOAD=Object.freeze({code:3015,level:0,message:"Error loading {0}, status code {1}"}),STALE_HOOK_ERROR=Object.freeze({code:3016,level:0,message:"An error occurred handling module conflict"});Object.freeze({code:3017,level:0,message:"Marking module(s) as externally loaded, but they are already loaded: {0}"});const FAIL_HOOK_LOAD=Object.freeze({code:3018,level:0,message:'Error loading "{0}" from hook'}),EXPORTER_ERROR=Object.freeze({code:3021,level:0,message:'Error evaluating module "{0}", error was {1}'}),BAD_IMPORT_MAP=Object.freeze({code:3011,level:0,message:"import map is not valid"}),hasDocument="undefined"!=typeof document,hasSetTimeout="function"==typeof setTimeout,hasConsole="undefined"!=typeof console;function getBaseUrl(){let e;if(hasDocument){const t=document.querySelector("base[href]");e=t&&t.href}if(!e&&"undefined"!=typeof location){e=location.href.split("#")[0].split("?")[0];const t=e.lastIndexOf("/");-1!==t&&(e=e.slice(0,t+1))}return e}function isUrl(e){return-1!==e.indexOf("://")}function resolveIfNotPlainOrUrl(e,t){if(-1!==e.indexOf("\\")&&(e=e.replace(/\\/g,"/")),"/"===e[0]&&"/"===e[1])return t.slice(0,t.indexOf(":")+1)+e;if("."===e[0]&&("/"===e[1]||"."===e[1]&&("/"===e[2]||2===e.length&&(e+="/"))||1===e.length&&(e+="/"))||"/"===e[0]){const r=t.slice(0,t.indexOf(":")+1);let o;if("/"===t[r.length+1]?"file:"!==r?(o=t.slice(r.length+2),o=o.slice(o.indexOf("/")+1)):o=t.slice(8):o=t.slice(r.length+("/"===t[r.length]?1:0)),"/"===e[0])return t.slice(0,t.length-o.length-1)+e;const s=o.slice(0,o.lastIndexOf("/")+1)+e,n=[];let i=-1;for(let e=0;e<s.length;e++)-1!==i?"/"===s[e]&&(n.push(s.slice(i,e+1)),i=-1):"."===s[e]?"."!==s[e+1]||"/"!==s[e+2]&&e+2!==s.length?"/"===s[e+1]||e+1===s.length?e+=1:i=e:(n.pop(),e+=2):i=e;return-1!==i&&n.push(s.slice(i)),t.slice(0,t.length-o.length)+n.join("")}}function resolveUrl(e,t){return resolveIfNotPlainOrUrl(e,t)||(isUrl(e)?e:resolveIfNotPlainOrUrl("./"+e,t))}function createScript(e){const t=document.createElement("script");return t.async=!0,t.crossOrigin="anonymous",t.src=e,t}let lastWindowError$1,lastWindowErrorUrl;function loadModuleDef(e){return new Promise((function(t,r){if(hasDocument){const o=createScript(e);o.addEventListener("error",(()=>{r(new LoaderError(FAIL_LOAD,[e]))})),o.addEventListener("load",(()=>{document.head.removeChild(o),lastWindowErrorUrl===e?r(lastWindowError$1):t()})),document.head.appendChild(o)}}))}hasDocument&&window.addEventListener("error",(e=>{lastWindowErrorUrl=e.filename,lastWindowError$1=e.error}));const MODULE_LOAD_TIMEOUT_TIMER=6e4,SUPPORTS_TRUSTED_TYPES="undefined"!=typeof trustedTypes;
/*!
     * Copyright (C) 2023 salesforce.com, inc.
     */function createTrustedTypesPolicy(e,t){return trustedTypes.createPolicy(e,t)}function createFallbackPolicy(e,t){return t}const createPolicy=SUPPORTS_TRUSTED_TYPES?createTrustedTypesPolicy:createFallbackPolicy,policyOptions={createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e};try{createPolicy("default",{createHTML:e=>e,createScript(e){if("null"===e||"undefined"===e)return e},createScriptURL:e=>e})}catch(e){}const trusted=createPolicy("trusted",policyOptions);let lastWindowError;function isCustomResponse(e){return Object.prototype.hasOwnProperty.call(e,"data")&&!Object.prototype.hasOwnProperty.call(e,"blob")}function isFetchResponse(e){return"function"==typeof e.blob}function isResponseAPromise(e){return!(!e||!e.then)}async function evaluateLoadHookResponse(response,id){return Promise.resolve().then((async()=>{if(!response||!response.status)throw new LoaderError(INVALID_LOADER_SERVICE_RESPONSE);if(200!==response.status)throw new LoaderError(HTTP_FAIL_LOAD,[id,`${response.status}`]);const isResponse=isFetchResponse(response);let code;if(isCustomResponse(response))code=response.data;else{if(!isResponse)throw new LoaderError(INVALID_LOADER_SERVICE_RESPONSE);code=await response.text()}if(!code)throw new LoaderError(FAIL_LOAD,[id]);code=`${code}\n//# sourceURL=${id}`;try{eval(trusted.createScript(code))}catch(e){throw new LoaderError(FAIL_LOAD,[id])}if(lastWindowError)throw new LoaderError(FAIL_LOAD,[id]);return!0}))}async function evaluateLoadHook(e,t){return hasSetTimeout?new Promise(((r,o)=>{const s=setTimeout((()=>{o(new LoaderError(MODULE_LOAD_TIMEOUT,[e]))}),MODULE_LOAD_TIMEOUT_TIMER);t.then((e=>{r(e)})).catch((()=>{o(new LoaderError(FAIL_HOOK_LOAD,[e]))})).finally((()=>{clearTimeout(s)}))})):t}function reportError(e){hasConsole&&console.error(e)}function evaluateHandleStaleModuleHooks(e,t){const{name:r,oldHash:o,newHash:s}=t;for(let t=0;t<e.length;t++){const n=e[t];try{if(null!==n({name:r,oldHash:o,newHash:s}))break}catch(e){reportError(new LoaderError(STALE_HOOK_ERROR))}}}hasDocument&&globalThis.addEventListener("error",(e=>{lastWindowError=e.error}));const LOADER_PREFIX="lwr.loader.",MODULE_DEFINE=`${LOADER_PREFIX}module.define`,MODULE_DYNAMIC_LOAD=`${LOADER_PREFIX}moduleRegistry.dynamicLoad`,MODULE_FETCH=`${LOADER_PREFIX}module.fetch`,MODULE_ERROR=`${LOADER_PREFIX}module.error`;class ModuleRegistry{constructor(e){ModuleRegistry.prototype.__init.call(this),ModuleRegistry.prototype.__init2.call(this),ModuleRegistry.prototype.__init3.call(this),this.baseUrl=e.baseUrl||"",this.profiler=e.profiler}async load(e,t){const r=t?{importer:t}:{};this.profiler.logOperationStart({id:MODULE_DYNAMIC_LOAD,specifier:e,metadata:r});const o=await this.resolve(e,t),s=await this.getModuleRecord(o,e);return s.evaluated?s.module:(s.evaluationPromise||(s.evaluationPromise=this.topLevelEvaluation(s)),s.evaluationPromise)}async resolve(e,t){const r=this.baseUrl;let o,s=e;const n=this.resolveHook;let i=!0;if(n){for(let e=0;e<n.length;e++){const t=(0,n[e])(s,{parentUrl:r});let i;if((t||null===t)&&(i=isResponseAPromise(t)?await t:t),!this.isValidResolveResponse(i))throw new LoaderError(INVALID_LOADER_SERVICE_RESPONSE);if(null!==i){if("string"==typeof i){if(resolveIfNotPlainOrUrl(i,r))throw new LoaderError(INVALID_LOADER_SERVICE_RESPONSE);s=i;continue}if(o=i&&i.url&&(resolveIfNotPlainOrUrl(i.url,r)||i.url),!o)throw new LoaderError(INVALID_LOADER_SERVICE_RESPONSE);break}}if(s!==e){if(!o&&this.namedDefineRegistry.has(s))return s;e=s}}if(!o){const t=resolveIfNotPlainOrUrl(e,r)||e;if(this.moduleRegistry.has(t))return t;if(this.resolver){const e=this.resolver.resolve(t,r);if(o=e&&e.uri,i=e?!!e.defaultUri:i,this.namedDefineRegistry.has(t)){const e=this.namedDefineRegistry.get(t);if(e.external||e.defined){if(!this.moduleRegistry.get(o)||!this.aliases.has(t))return t}}}else o=t}if(!o||!isUrl(o)){if(this.namedDefineRegistry.has(e))return e;throw new LoaderError(UNRESOLVED,[e])}return i&&t&&isUrl(o)&&(o+=`?importer=${encodeURIComponent(t)}`),o}has(e){return this.moduleRegistry.has(e)}define(e,t,r,o){const s=this.namedDefineRegistry.get(e);if(s&&s.defined)return void(this.lastDefine=s);const n={name:e,dependencies:t,exporter:r,signatures:o,defined:!0};s&&s.external&&s.external.resolveExternal(n),this.profiler.logOperationStart({id:MODULE_DEFINE,specifier:e}),this.namedDefineRegistry.set(e,n),this.lastDefine=n,o.hashes&&Object.entries(o.hashes).forEach((([e,t])=>{this.checkModuleSignature(e,t)}))}registerExternalModules(e){e.map((e=>{if(!this.namedDefineRegistry.has(e)){let t,r;const o=new Promise(((o,s)=>{t=o,r=setTimeout((()=>{s(new LoaderError(MODULE_LOAD_TIMEOUT,[e]))}),MODULE_LOAD_TIMEOUT_TIMER)})).finally((()=>{clearTimeout(r)})),s={name:e,defined:!1,external:{resolveExternal:t,moduleDefPromise:o}};this.namedDefineRegistry.set(e,s)}}))}checkModuleSignature(e,t){const r=this.namedDefineRegistry.get(e);if(!r){const r={name:e,signatures:{ownHash:t},defined:!1};return void this.namedDefineRegistry.set(e,r)}const o=r.signatures?r.signatures.ownHash:void 0;if(o&&t!==o){const r=this.handleStaleModuleHook;r&&evaluateHandleStaleModuleHooks(r,{name:e,oldHash:o,newHash:t})}}setImportResolver(e){this.resolver=e}__init(){this.namedDefineRegistry=new Map}__init2(){this.moduleRegistry=new Map}__init3(){this.aliases=new Map}getExistingModuleRecord(e,t){const r=this.moduleRegistry.get(e);if(r)return this.storeModuleAlias(t,e),r;if(e!==t){const e=this.aliases.get(t);if(e){const t=this.moduleRegistry.get(e);if(t)return t}}return r}async getModuleRecord(e,t){const r=this.getExistingModuleRecord(e,t);if(r)return r;const o=this.getModuleDef(e,t),s=o.then((e=>{const t=(e&&e.dependencies||[]).map((e=>{if("exports"!==e)return invariant("require"!==e,NO_AMD_REQUIRE),this.getModuleDependencyRecord.call(this,e)})).filter((e=>void 0!==e));return Promise.all(t)})),n={id:e,module:Object.create(null),dependencyRecords:s,instantiation:o,evaluated:!1,evaluationPromise:null};return this.moduleRegistry.set(e,n),this.storeModuleAlias(t,e),s.then((()=>n))}storeModuleAlias(e,t){e!==t&&(this.aliases.has(e)?hasConsole&&this.aliases.get(e):this.aliases.set(e,t))}async getModuleDependencyRecord(e){const t=await this.resolve(e);return this.getModuleRecord(t,e)}async topLevelEvaluation(e){return await this.instantiateAll(e,{}),this.evaluateModule(e,{})}async instantiateAll(e,t){if(!t[e.id]){t[e.id]=!0;const r=await e.dependencyRecords;if(r)for(let e=0;e<r.length;e++){const o=r[e];await this.instantiateAll(o,t)}}}async evaluateModule(e,t){const r=await e.dependencyRecords;r.length>0&&(t[e.id]=!0,await this.evaluateModuleDependencies(r,t));const{exporter:o,dependencies:s}=await e.instantiation,n={},i=s?await Promise.all(s.map((async e=>{if("exports"===e)return n;const t=await this.resolve(e),r=this.moduleRegistry.get(t);if(!r)throw new LoaderError(FAILED_DEP,[t]);const o=r.module;if(!r.evaluated)return this.getCircularDependencyWrapper(o);if(o)return o.__defaultInterop?o.default:o;throw new LoaderError(FAILED_DEP,[t])}))):[];if(e.evaluated)return e.module;let a;try{a=o(...i)}catch(t){throw new LoaderError(EXPORTER_ERROR,[e.id,t.message||t])}void 0!==a?(a={default:a},Object.defineProperty(a,"__defaultInterop",{value:!0})):this.isNamedExportDefaultOnly(n)&&Object.defineProperty(n,"__useDefault",{value:!0});const l=a||n;for(const t in l)Object.defineProperty(e.module,t,{enumerable:!0,set(e){l[t]=e},get:()=>l[t]});return l.__useDefault&&Object.defineProperty(e.module,"__useDefault",{value:!0}),l.__defaultInterop&&Object.defineProperty(e.module,"__defaultInterop",{value:!0}),l.__esModule&&Object.defineProperty(e.module,"__esModule",{value:!0}),e.evaluated=!0,Object.freeze(e.module),e.module}isNamedExportDefaultOnly(e){return void 0!==e&&2===Object.getOwnPropertyNames(e).length&&Object.prototype.hasOwnProperty.call(e,"default")&&Object.prototype.hasOwnProperty.call(e,"__esModule")}getCircularDependencyWrapper(e){const t=()=>e.__useDefault||e.__defaultInterop?e.default:e;return t.__circular__=!0,t}async evaluateModuleDependencies(e,t){for(let r=0;r<e.length;r++){const o=e[r];o.evaluated||t[o.id]||(t[o.id]=!0,await this.evaluateModule(o,t))}}async getModuleDef(e,t){this.lastDefine=void 0;const r=isUrl(e)?t!==e?t:void 0:e;let o=r&&this.namedDefineRegistry.get(r);if(o&&o.external)return o.external.moduleDefPromise;if(o&&o.defined)return o;const s=this.baseUrl,n=r||t;return this.profiler.logOperationStart({id:MODULE_FETCH,specifier:n}),Promise.resolve().then((async()=>{const t=this.loadHook;if(t)for(let r=0;r<t.length;r++){const o=(0,t[r])(e,s),n=isResponseAPromise(o)?await evaluateLoadHook(e,o):o;if(void 0===n)throw new LoaderError(INVALID_LOADER_SERVICE_RESPONSE);if(n&&null!==n)return evaluateLoadHookResponse(n,e)}return!1})).then((t=>{if(!0!==t&&hasDocument)return loadModuleDef(e)})).then((()=>{if(o=r&&this.namedDefineRegistry.get(r),o||(o=this.lastDefine),!o)throw new LoaderError(FAIL_INSTANTIATE,[e]);return this.profiler.logOperationEnd({id:MODULE_FETCH,specifier:n}),o})).catch((e=>{throw e instanceof LoaderError||this.profiler.logOperationStart({id:MODULE_ERROR,specifier:n}),e}))}addLoaderPlugin(e){if("object"!=typeof e)throw new LoaderError(INVALID_HOOK);const{loadModule:t,resolveModule:r}=e;r&&(this.resolveHook?this.resolveHook.push(r):this.resolveHook=[r]),t&&(this.loadHook?this.loadHook.push(t):this.loadHook=[t])}registerHandleStaleModuleHook(e){this.handleStaleModuleHook?this.handleStaleModuleHook.push(e):this.handleStaleModuleHook=[e]}isValidResolveResponse(e){return null===e||"string"==typeof e||e&&"string"==typeof e.url}}function getMatch(e,t){if(t[e])return e;let r=e.length;do{const o=e.slice(0,r+1);if(o in t)return o}while(-1!==(r=e.lastIndexOf("/",r-1)))}function targetWarning(e,t,r){hasConsole&&console.warn("Package target "+r+", resolving target '"+t+"' for "+e)}function applyPackages(e,t,r){const o=getMatch(e,t);if(o){const r=t[o];if(null===r)return;if(!(e.length>o.length&&"/"!==r[r.length-1])){if(e.length>o.length&&"/"===r[r.length-1]&&r.lastIndexOf(o)===r.length-o.length){return{uri:r.substring(0,r.lastIndexOf(o))+encodeURIComponent(e)}}return{uri:r+e.slice(o.length)}}targetWarning(o,r,"should have a trailing '/'")}else if(r&&!isUrl(e)){return{uri:r+encodeURIComponent(e),defaultUri:!0}}}function resolveImportMapEntry(e,t,r){e.scopes||(e.scopes={}),e.imports||(e.imports={});const o=e.scopes;let s=r&&getMatch(r,o);for(;s;){const e=applyPackages(t,o[s]);if(e)return e;s=getMatch(s.slice(0,s.lastIndexOf("/")),o)}return applyPackages(t,e.imports,e.default)||isUrl(t)&&{uri:t}||void 0}function resolveAndComposePackages(e,t,r,o,s){for(const n in e){const i=resolveIfNotPlainOrUrl(n,r)||n,a=e[n];if("string"!=typeof a)continue;const l=resolveImportMapEntry(o,resolveIfNotPlainOrUrl(a,r)||a,s);l?t[i]=l.uri:targetWarning(n,a,"bare specifier did not resolve")}}function resolveAndComposeImportMap(e,t,r={imports:{},scopes:{}}){const o={imports:Object.assign({},r.imports),scopes:Object.assign({},r.scopes),default:e.default};if(e.imports&&resolveAndComposePackages(e.imports,o.imports,t,r),e.scopes)for(const s in e.scopes){const n=resolveUrl(s,t);resolveAndComposePackages(e.scopes[s],o.scopes[n]||(o.scopes[n]={}),t,r,n)}return e.default&&(o.default=resolveIfNotPlainOrUrl(e.default,t)),o}class ImportMapResolver{constructor(e){this.importMap=e}resolve(e,t){return resolveImportMapEntry(this.importMap,e,t)}}const IMPORTMAP_SCRIPT_TYPE="lwr-importmap";function iterateDocumentImportMaps(e,t){const r=document.querySelectorAll(`script[type="${IMPORTMAP_SCRIPT_TYPE}"]`+t),o=Array.from(r).filter((e=>!e.src||(hasConsole&&console.warn("LWR does not support import maps from script src"),!1)));Array.prototype.forEach.call(o,e)}async function getImportMapFromScript(e){return Promise.resolve(e.innerHTML)}async function evaluateImportMaps(e){let t={imports:{},scopes:{}},r=Promise.resolve(t);if(hasDocument){if(e||(e=getBaseUrl()),!e)throw new LoaderError(NO_BASE_URL);iterateDocumentImportMaps((o=>{r=r.then((()=>getImportMapFromScript(o))).then((e=>{try{return JSON.parse(e)}catch(e){throw new LoaderError(BAD_IMPORT_MAP)}})).then((r=>(t=resolveAndComposeImportMap(r,o.src||e,t),t)))}),"")}return r}class Loader{constructor(e){let t=(e=e||{}).baseUrl,r=e.profiler;if(t&&(t=t.replace(/\/?$/,"/")),t||(t=getBaseUrl()),!t)throw new LoaderError(NO_BASE_URL);this.baseUrl=t,r||(r={logOperationStart:()=>{},logOperationEnd:()=>{}}),this.registry=new ModuleRegistry({baseUrl:t,profiler:r}),this.services=Object.freeze({addLoaderPlugin:this.registry.addLoaderPlugin.bind(this.registry),handleStaleModule:this.registry.registerHandleStaleModuleHook.bind(this.registry),appMetadata:e.appMetadata,serverData:e.serverData||{}})}define(e,t,r,o){invariant("string"==typeof e,MISSING_NAME);let s=r,n=t,i=o;"function"==typeof n&&(s=t,n=[],i=r),i=i||{},invariant(Array.isArray(n),INVALID_DEPS),this.registry.define(e,n,s,i)}async load(e,t){return this.registry.load(e,t)}has(e){return this.registry.has(e)}async resolve(e,t){return this.registry.resolve(e,t)}async registerImportMappings(e){let t;if(t=e?resolveAndComposeImportMap(e,this.baseUrl,this.parentImportMap):await evaluateImportMaps(this.baseUrl),this.parentImportMap=t,this.parentImportMap){const e=new ImportMapResolver(this.parentImportMap);this.registry.setImportResolver(e)}}registerExternalModules(e){this.registry.registerExternalModules(e)}}exports.Loader=Loader,Object.defineProperty(exports,"__esModule",{value:!0})}));
