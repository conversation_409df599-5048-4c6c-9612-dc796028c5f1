<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta charset="utf-8"/>
		<meta http-equiv="X-UA-Compatible" content="IE=Edge"/>
		<script src="https://rawgit.com/jonathantneal/svg4everybody/master/dist/svg4everybody.js"></script>
		<script>svg4everybody();</script>
		<title>SVG &lt;symbol&gt; sprite preview | svg-sprite</title>
		<style>@charset "UTF-8";body{padding:0;margin:0;color:#666;background:#fafafa;font-family:Arial,Helvetica,sans-serif;font-size:1em;line-height:1.4}header{display:block;padding:3em 3em 2em 3em;background-color:#fff}header p{margin:2em 0 0 0}section{border-top:1px solid #eee;padding:2em 3em 0 3em}section ul{margin:0;padding:0}section li{display:inline;display:inline-block;background-color:#fff;position:relative;margin:0 2em 2em 0;vertical-align:top;border:1px solid #ccc;padding:1em 1em 3em 1em;cursor:default}.icon-box{margin:0;width:144px;height:144px;position:relative;background:#ccc url("data:image/gif;base64,R0lGODlhDAAMAIAAAMzMzP///yH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4wLWMwNjEgNjQuMTQwOTQ5LCAyMDEwLzEyLzA3LTEwOjU3OjAxICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1LjEgV2luZG93cyIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDozQjk4OTI0MUY5NTIxMUUyQkJDMEI5NEFEM0Y1QTYwQyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDozQjk4OTI0MkY5NTIxMUUyQkJDMEI5NEFEM0Y1QTYwQyI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjNCOTg5MjNGRjk1MjExRTJCQkMwQjk0QUQzRjVBNjBDIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjNCOTg5MjQwRjk1MjExRTJCQkMwQjk0QUQzRjVBNjBDIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEAAAAAAAsAAAAAAwADAAAAhaEH6mHmmzcgzJAUG/NVGrfOZ8YLlABADs=") top left repeat;border:1px solid #ccc;display:table-cell;vertical-align:middle;text-align:center}.icon{display:inline;display:inline-block}h1{margin-top:0}h2{margin:0;padding:0;font-size:1em;font-weight:normal;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:absolute;left:1em;right:1em;bottom:1em}footer{display:block;margin:0;padding:0 3em 3em 3em}footer p{margin:0;font-size:.7em}footer a{color:#0f7595;margin-left:0}</style>
		
<!--
	
Sprite shape dimensions
====================================================================================================
You will need to set the sprite shape dimensions via CSS when you use them as inline SVG, otherwise
they would become a huge 100% in size. You may use the following dimension classes for doing so.
They might well be outsourced to an external stylesheet of course.

-->

<style type="text/css">
	.svg-add-dims { width: 24px; height: 24px; }
	.svg-adduser-dims { width: 24px; height: 24px; }
	.svg-announcement-dims { width: 24px; height: 24px; }
	.svg-answer-dims { width: 24px; height: 24px; }
	.svg-apex-dims { width: 24px; height: 24px; }
	.svg-approval-dims { width: 24px; height: 24px; }
	.svg-apps-dims { width: 24px; height: 24px; }
	.svg-arrowdown-dims { width: 24px; height: 24px; }
	.svg-arrowup-dims { width: 24px; height: 24px; }
	.svg-attach-dims { width: 24px; height: 24px; }
	.svg-back-dims { width: 24px; height: 24px; }
	.svg-ban-dims { width: 24px; height: 24px; }
	.svg-bold-dims { width: 24px; height: 24px; }
	.svg-bookmark-dims { width: 24px; height: 24px; }
	.svg-breadcrumbs-dims { width: 24px; height: 24px; }
	.svg-broadcast-dims { width: 24px; height: 24px; }
	.svg-brush-dims { width: 24px; height: 24px; }
	.svg-bucket-dims { width: 24px; height: 24px; }
	.svg-builder-dims { width: 24px; height: 24px; }
	.svg-call-dims { width: 24px; height: 24px; }
	.svg-capslock-dims { width: 24px; height: 24px; }
	.svg-cases-dims { width: 24px; height: 24px; }
	.svg-center_align_text-dims { width: 24px; height: 24px; }
	.svg-change_owner-dims { width: 24px; height: 24px; }
	.svg-change_record_type-dims { width: 24px; height: 24px; }
	.svg-chart-dims { width: 24px; height: 24px; }
	.svg-chat-dims { width: 24px; height: 24px; }
	.svg-check-dims { width: 24px; height: 24px; }
	.svg-checkin-dims { width: 24px; height: 24px; }
	.svg-chevrondown-dims { width: 24px; height: 24px; }
	.svg-chevronleft-dims { width: 24px; height: 24px; }
	.svg-chevronright-dims { width: 24px; height: 24px; }
	.svg-chevronup-dims { width: 24px; height: 24px; }
	.svg-clear-dims { width: 24px; height: 24px; }
	.svg-clock-dims { width: 24px; height: 24px; }
	.svg-close-dims { width: 24px; height: 24px; }
	.svg-comments-dims { width: 24px; height: 24px; }
	.svg-company-dims { width: 24px; height: 24px; }
	.svg-connected_apps-dims { width: 24px; height: 24px; }
	.svg-contract-dims { width: 24px; height: 24px; }
	.svg-contract_alt-dims { width: 24px; height: 24px; }
	.svg-copy-dims { width: 24px; height: 24px; }
	.svg-crossfilter-dims { width: 24px; height: 24px; }
	.svg-custom_apps-dims { width: 24px; height: 24px; }
	.svg-cut-dims { width: 24px; height: 24px; }
	.svg-dash-dims { width: 24px; height: 24px; }
	.svg-database-dims { width: 24px; height: 24px; }
	.svg-datadotcom-dims { width: 24px; height: 24px; }
	.svg-dayview-dims { width: 24px; height: 24px; }
	.svg-delete-dims { width: 24px; height: 24px; }
	.svg-deprecate-dims { width: 24px; height: 24px; }
	.svg-description-dims { width: 24px; height: 24px; }
	.svg-desktop-dims { width: 24px; height: 24px; }
	.svg-dislike-dims { width: 24px; height: 24px; }
	.svg-dock_panel-dims { width: 24px; height: 24px; }
	.svg-down-dims { width: 24px; height: 24px; }
	.svg-download-dims { width: 24px; height: 24px; }
	.svg-edit-dims { width: 24px; height: 24px; }
	.svg-edit_form-dims { width: 24px; height: 24px; }
	.svg-email-dims { width: 24px; height: 24px; }
	.svg-end_call-dims { width: 24px; height: 24px; }
	.svg-erect_window-dims { width: 24px; height: 24px; }
	.svg-error-dims { width: 24px; height: 24px; }
	.svg-event-dims { width: 24px; height: 24px; }
	.svg-expand-dims { width: 24px; height: 24px; }
	.svg-expand_alt-dims { width: 24px; height: 24px; }
	.svg-fallback-dims { width: 24px; height: 24px; }
	.svg-favorite-dims { width: 24px; height: 24px; }
	.svg-feed-dims { width: 24px; height: 24px; }
	.svg-file-dims { width: 24px; height: 24px; }
	.svg-filter-dims { width: 24px; height: 24px; }
	.svg-filterList-dims { width: 24px; height: 24px; }
	.svg-flow-dims { width: 24px; height: 24px; }
	.svg-forward-dims { width: 24px; height: 24px; }
	.svg-frozen-dims { width: 24px; height: 24px; }
	.svg-full_width_view-dims { width: 24px; height: 24px; }
	.svg-groups-dims { width: 24px; height: 24px; }
	.svg-help-dims { width: 24px; height: 24px; }
	.svg-home-dims { width: 24px; height: 24px; }
	.svg-identity-dims { width: 24px; height: 24px; }
	.svg-image-dims { width: 24px; height: 24px; }
	.svg-inbox-dims { width: 24px; height: 24px; }
	.svg-info-dims { width: 24px; height: 24px; }
	.svg-info_alt-dims { width: 24px; height: 24px; }
	.svg-insert_tag_field-dims { width: 24px; height: 24px; }
	.svg-insert_template-dims { width: 24px; height: 24px; }
	.svg-italic-dims { width: 24px; height: 24px; }
	.svg-jump_to_bottom-dims { width: 24px; height: 24px; }
	.svg-jump_to_top-dims { width: 24px; height: 24px; }
	.svg-justify_text-dims { width: 24px; height: 24px; }
	.svg-kanban-dims { width: 24px; height: 24px; }
	.svg-keyboard_dismiss-dims { width: 24px; height: 24px; }
	.svg-knowledge_base-dims { width: 24px; height: 24px; }
	.svg-layers-dims { width: 24px; height: 24px; }
	.svg-layout-dims { width: 24px; height: 24px; }
	.svg-left-dims { width: 24px; height: 24px; }
	.svg-left_align_text-dims { width: 24px; height: 24px; }
	.svg-level_up-dims { width: 24px; height: 24px; }
	.svg-like-dims { width: 24px; height: 24px; }
	.svg-link-dims { width: 24px; height: 24px; }
	.svg-list-dims { width: 24px; height: 24px; }
	.svg-location-dims { width: 24px; height: 24px; }
	.svg-lock-dims { width: 24px; height: 24px; }
	.svg-log_a_call-dims { width: 24px; height: 24px; }
	.svg-logout-dims { width: 24px; height: 24px; }
	.svg-magicwand-dims { width: 24px; height: 24px; }
	.svg-mark_all_as_read-dims { width: 24px; height: 24px; }
	.svg-matrix-dims { width: 24px; height: 24px; }
	.svg-merge-dims { width: 24px; height: 24px; }
	.svg-metrics-dims { width: 24px; height: 24px; }
	.svg-minimize_window-dims { width: 24px; height: 24px; }
	.svg-moneybag-dims { width: 24px; height: 24px; }
	.svg-monthlyview-dims { width: 24px; height: 24px; }
	.svg-move-dims { width: 24px; height: 24px; }
	.svg-muted-dims { width: 24px; height: 24px; }
	.svg-new-dims { width: 24px; height: 24px; }
	.svg-new_window-dims { width: 24px; height: 24px; }
	.svg-news-dims { width: 24px; height: 24px; }
	.svg-note-dims { width: 24px; height: 24px; }
	.svg-notebook-dims { width: 24px; height: 24px; }
	.svg-notification-dims { width: 24px; height: 24px; }
	.svg-office365-dims { width: 24px; height: 24px; }
	.svg-offline-dims { width: 24px; height: 24px; }
	.svg-open-dims { width: 24px; height: 24px; }
	.svg-open_folder-dims { width: 24px; height: 24px; }
	.svg-opened_folder-dims { width: 24px; height: 24px; }
	.svg-overflow-dims { width: 24px; height: 24px; }
	.svg-package-dims { width: 24px; height: 24px; }
	.svg-package_org-dims { width: 24px; height: 24px; }
	.svg-package_org_beta-dims { width: 24px; height: 24px; }
	.svg-page-dims { width: 24px; height: 24px; }
	.svg-palette-dims { width: 24px; height: 24px; }
	.svg-paste-dims { width: 24px; height: 24px; }
	.svg-people-dims { width: 24px; height: 24px; }
	.svg-phone_landscape-dims { width: 24px; height: 24px; }
	.svg-phone_portrait-dims { width: 24px; height: 24px; }
	.svg-photo-dims { width: 24px; height: 24px; }
	.svg-picklist-dims { width: 24px; height: 24px; }
	.svg-power-dims { width: 24px; height: 24px; }
	.svg-preview-dims { width: 24px; height: 24px; }
	.svg-priority-dims { width: 24px; height: 24px; }
	.svg-process-dims { width: 24px; height: 24px; }
	.svg-push-dims { width: 24px; height: 24px; }
	.svg-puzzle-dims { width: 24px; height: 24px; }
	.svg-question-dims { width: 24px; height: 24px; }
	.svg-questions_and_answers-dims { width: 24px; height: 24px; }
	.svg-record-dims { width: 24px; height: 24px; }
	.svg-record_create-dims { width: 24px; height: 24px; }
	.svg-redo-dims { width: 24px; height: 24px; }
	.svg-refresh-dims { width: 24px; height: 24px; }
	.svg-relate-dims { width: 24px; height: 24px; }
	.svg-remove_formatting-dims { width: 24px; height: 24px; }
	.svg-remove_link-dims { width: 24px; height: 24px; }
	.svg-replace-dims { width: 24px; height: 24px; }
	.svg-reply-dims { width: 24px; height: 24px; }
	.svg-reply_all-dims { width: 24px; height: 24px; }
	.svg-reset_password-dims { width: 24px; height: 24px; }
	.svg-resource_absence-dims { width: 24px; height: 24px; }
	.svg-resource_capacity-dims { width: 24px; height: 24px; }
	.svg-resource_territory-dims { width: 24px; height: 24px; }
	.svg-retweet-dims { width: 24px; height: 24px; }
	.svg-richtextbulletedlist-dims { width: 24px; height: 24px; }
	.svg-richtextindent-dims { width: 24px; height: 24px; }
	.svg-richtextnumberedlist-dims { width: 24px; height: 24px; }
	.svg-richtextoutdent-dims { width: 24px; height: 24px; }
	.svg-right-dims { width: 24px; height: 24px; }
	.svg-right_align_text-dims { width: 24px; height: 24px; }
	.svg-rotate-dims { width: 24px; height: 24px; }
	.svg-rows-dims { width: 24px; height: 24px; }
	.svg-salesforce1-dims { width: 24px; height: 24px; }
	.svg-search-dims { width: 24px; height: 24px; }
	.svg-settings-dims { width: 24px; height: 24px; }
	.svg-setup-dims { width: 24px; height: 24px; }
	.svg-setup_assistant_guide-dims { width: 24px; height: 24px; }
	.svg-share-dims { width: 24px; height: 24px; }
	.svg-share_mobile-dims { width: 24px; height: 24px; }
	.svg-share_post-dims { width: 24px; height: 24px; }
	.svg-shield-dims { width: 24px; height: 24px; }
	.svg-side_list-dims { width: 24px; height: 24px; }
	.svg-signpost-dims { width: 24px; height: 24px; }
	.svg-sms-dims { width: 24px; height: 24px; }
	.svg-snippet-dims { width: 24px; height: 24px; }
	.svg-socialshare-dims { width: 24px; height: 24px; }
	.svg-sort-dims { width: 24px; height: 24px; }
	.svg-spinner-dims { width: 24px; height: 24px; }
	.svg-standard_objects-dims { width: 24px; height: 24px; }
	.svg-stop-dims { width: 24px; height: 24px; }
	.svg-strikethrough-dims { width: 24px; height: 24px; }
	.svg-success-dims { width: 24px; height: 24px; }
	.svg-summary-dims { width: 24px; height: 24px; }
	.svg-summarydetail-dims { width: 24px; height: 24px; }
	.svg-switch-dims { width: 24px; height: 24px; }
	.svg-sync-dims { width: 24px; height: 24px; }
	.svg-table-dims { width: 24px; height: 24px; }
	.svg-tablet_landscape-dims { width: 24px; height: 24px; }
	.svg-tablet_portrait-dims { width: 24px; height: 24px; }
	.svg-tabset-dims { width: 24px; height: 24px; }
	.svg-task-dims { width: 24px; height: 24px; }
	.svg-text_background_color-dims { width: 24px; height: 24px; }
	.svg-text_color-dims { width: 24px; height: 24px; }
	.svg-threedots-dims { width: 24px; height: 24px; }
	.svg-threedots_vertical-dims { width: 24px; height: 24px; }
	.svg-thunder-dims { width: 24px; height: 24px; }
	.svg-tile_card_list-dims { width: 24px; height: 24px; }
	.svg-topic-dims { width: 24px; height: 24px; }
	.svg-touch_action-dims { width: 24px; height: 24px; }
	.svg-trail-dims { width: 24px; height: 24px; }
	.svg-turn_off_notifications-dims { width: 24px; height: 24px; }
	.svg-undelete-dims { width: 24px; height: 24px; }
	.svg-undeprecate-dims { width: 24px; height: 24px; }
	.svg-underline-dims { width: 24px; height: 24px; }
	.svg-undo-dims { width: 24px; height: 24px; }
	.svg-unlock-dims { width: 24px; height: 24px; }
	.svg-unmuted-dims { width: 24px; height: 24px; }
	.svg-up-dims { width: 24px; height: 24px; }
	.svg-upload-dims { width: 24px; height: 24px; }
	.svg-user-dims { width: 24px; height: 24px; }
	.svg-user_role-dims { width: 24px; height: 24px; }
	.svg-volume_high-dims { width: 24px; height: 24px; }
	.svg-volume_low-dims { width: 24px; height: 24px; }
	.svg-volume_off-dims { width: 24px; height: 24px; }
	.svg-warning-dims { width: 24px; height: 24px; }
	.svg-weeklyview-dims { width: 24px; height: 24px; }
	.svg-wifi-dims { width: 24px; height: 24px; }
	.svg-work_order_type-dims { width: 24px; height: 24px; }
	.svg-world-dims { width: 24px; height: 24px; }
	.svg-yubi_key-dims { width: 24px; height: 24px; }
	.svg-zoomin-dims { width: 24px; height: 24px; }
	.svg-zoomout-dims { width: 24px; height: 24px; }
</style>
<!--
====================================================================================================
-->

	</head>
	<body>
	
<!--
	
Inline <symbol> SVG sprite
====================================================================================================
This is an inlined version of the generated SVG sprite. The single images may be <use>d everywhere
below within this document. Please see

	https://github.com/jkphl/svg-sprite/blob/master/docs/configuration.md#defs--symbol-mode

for further details on how to create this embeddable sprite variant.

-->

<svg width="0" height="0" style="position:absolute">
		<symbol viewBox="0 0 24 24" id="add"><path  d="M13.8 13.4h7.7c.3 0 .7-.3.7-.7v-1.4c0-.4-.4-.7-.7-.7h-7.7c-.2 0-.4-.2-.4-.4V2.5c0-.3-.3-.7-.7-.7h-1.4c-.4 0-.7.4-.7.7v7.7c0 .2-.2.4-.4.4H2.5c-.3 0-.7.3-.7.7v1.4c0 .*******.7h7.7c.2 0 .4.2.4.4v7.7c0 .*******.7h1.4c.4 0 .7-.4.7-.7v-7.7c0-.2.2-.4.4-.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="adduser"><path d="M10.1 17.1c0-1.3.4-2.7 1.1-3.8.8-1.4 1.6-1.9 2.3-3 1.2-1.7 1.4-4.1.7-6-.8-1.9-2.5-3-4.6-2.9S6 2.7 5.3 4.6c-.7 2-.4 4.5 1.3 6.1.6.7 1.3 1.7.9 2.6-.3 1-1.4 1.4-2.2 1.7-1.8.8-4 1.9-4.3 4.1-.4 1.7.8 3.5 2.7 3.5h7.8c.4 0 .6-.4.4-.7-1.1-1.4-1.8-3.1-1.8-4.8zm7.4-5.6c-3.1 0-5.5 2.5-5.5 5.6s2.4 5.5 5.5 5.5 5.5-2.5 5.5-5.5-2.5-5.6-5.5-5.6zm2.8 6c0 .3-.2.5-.5.5h-1.3v1.4c0 .3-.3.4-.5.4h-1c-.2 0-.4-.1-.4-.4V18h-1.4c-.3 0-.4-.2-.4-.5v-.9c0-.3.1-.4.4-.4h1.4v-1.4c0-.3.2-.5.4-.5h1c.2 0 .5.2.5.5v1.4h1.3c.3 0 .5.1.5.4v.9z" /></symbol>
		<symbol viewBox="0 0 24 24" id="announcement"><path d="M10.5 21l-.6-.5c-.7-.5-.7-1.4-.7-1.9v-1.3c0-.4-.3-.7-.7-.7H5.8c-.4 0-.7.3-.7.7v3.6c0 1.2.7 2.2 1.9 2.2h2.2c1.4 0 1.5-.9 1.5-.9s.2-.9-.2-1.2zM20.8 8.3V2c0-1.1-1.4-1.4-2.2-.7l-4.1 3.9c-.6.5-1.4.8-2.3.8h-7C2.8 6 .9 8.1.9 10.5v.1c0 2.4 1.9 4.2 4.3 4.2h7c.9 0 1.7.3 2.4.9l4 4c.8.7 2.2.4 2.2-.7v-6.3c1.4 0 2.2-.9 2.2-2.2 0-1.2-.8-2.2-2.2-2.2z" /></symbol>
		<symbol viewBox="0 0 24 24" id="answer"><path  d="M12 1.8C5.9 1.8.9 6.4.9 12c0 1.8.5 3.5 1.4 5 .1.2.1.4.1.6l-1 3.2c-.2.6.4 1.1 1 .9l3.2-1.1c.2-.1.4-.1.6.1 1.7.9 3.7 1.5 5.8 1.5 6.2 0 11.1-4.5 11.1-10.2C23 6.4 18.1 1.8 12 1.8zm5.3 7.9l-5.6 5.6c-.3.2-.5.3-.8.3-.3 0-.6-.1-.8-.3l-2.7-2.7c-.2-.2-.2-.6 0-.7l.8-.8c.2-.2.5-.2.8 0l1.9 2 4.8-4.8c.3-.3.6-.3.8 0l.8.7c.2.2.2.6 0 .7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="apex"><path d="M22.4 18.5H9.9c-.3 0-.7.3-.7.7v1.3c0 .*******.7h12.5c.4 0 .7-.3.7-.7v-1.3c0-.4-.3-.7-.7-.7zm-10.7-8.4L2.8 2.9c-.3-.2-.7-.2-1 .1l-.7 1.2c-.3.3-.2.7.1.9l6.4 5.1c.2.2.2.6 0 .7L1.2 16c-.3.2-.4.7-.1 1l.7 1.2c.3.3.7.4 1 .1l8.9-7.1c.4-.3.4-.9 0-1.1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="approval"><path  d="M20.9 13.5h-4.1c-1.2 0-2.2-1-2.2-2.2.2-3.3 1.7-3.5 1.8-5.6.2-2.2-1.2-4.2-3.4-4.7-2.8-.6-5.4 1.6-5.4 4.4 0 2.4 1.6 2.4 1.8 5.9 0 1.2-1 2.2-2.2 2.2H3.1c-1.2 0-2.2.9-2.2 2.2v1.5c0 .4.3.7.8.7h20.6c.5 0 .8-.3.8-.7v-1.5c0-1.3-1-2.2-2.2-2.2zm0 6.6H3.1c-.4 0-.7.3-.7.7v.1c0 1.2 1 2.2 2.2 2.2h14.8c1.2 0 2.2-1 2.2-2.2v-.1c0-.4-.3-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="apps"><path  d="M6 1.8H3.2c-.8 0-1.4.6-1.4 1.4V6c0 .8.6 1.4 1.4 1.4H6c.8 0 1.4-.6 1.4-1.4V3.2c0-.8-.6-1.4-1.4-1.4zm0 14.8H3.2c-.8 0-1.4.6-1.4 1.4v2.8c0 .8.6 1.4 1.4 1.4H6c.8 0 1.4-.6 1.4-1.4V18c0-.8-.6-1.4-1.4-1.4zm0-7.4H3.2c-.8 0-1.4.6-1.4 1.4v2.8c0 .8.6 1.4 1.4 1.4H6c.8 0 1.4-.6 1.4-1.4v-2.8c0-.8-.6-1.4-1.4-1.4zm7.4-7.4h-2.8c-.8 0-1.4.6-1.4 1.4V6c0 .8.6 1.4 1.4 1.4h2.8c.8 0 1.4-.6 1.4-1.4V3.2c0-.8-.6-1.4-1.4-1.4zm0 14.8h-2.8c-.8 0-1.4.6-1.4 1.4v2.8c0 .8.6 1.4 1.4 1.4h2.8c.8 0 1.4-.6 1.4-1.4V18c0-.8-.6-1.4-1.4-1.4zm0-7.4h-2.8c-.8 0-1.4.6-1.4 1.4v2.8c0 .8.6 1.4 1.4 1.4h2.8c.8 0 1.4-.6 1.4-1.4v-2.8c0-.8-.6-1.4-1.4-1.4zm7.4-7.4H18c-.8 0-1.4.6-1.4 1.4V6c0 .8.6 1.4 1.4 1.4h2.8c.8 0 1.4-.6 1.4-1.4V3.2c0-.8-.6-1.4-1.4-1.4zm0 14.8H18c-.8 0-1.4.6-1.4 1.4v2.8c0 .8.6 1.4 1.4 1.4h2.8c.8 0 1.4-.6 1.4-1.4V18c0-.8-.6-1.4-1.4-1.4zm0-7.4H18c-.8 0-1.4.6-1.4 1.4v2.8c0 .8.6 1.4 1.4 1.4h2.8c.8 0 1.4-.6 1.4-1.4v-2.8c0-.8-.6-1.4-1.4-1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="arrowdown"><path  d="M4.4 14.3c-.3.4-.3.9 0 1.3l7 6.7c.3.4.9.4 1.2 0l7-6.7c.4-.4.4-.9 0-1.3l-1.3-1.2c-.3-.4-.9-.4-1.3 0l-2.1 2.1c-.4.4-1.1.1-1.1-.4V2.3c0-.5-.4-.9-.9-.9h-1.8c-.5 0-.9.5-.9.9v12.5c0 .5-.7.8-1.1.4L7 13.1c-.4-.4-1-.4-1.3 0l-1.3 1.2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="arrowup"><path  d="M19.1 9.7c.4-.4.4-.9 0-1.3l-6.9-6.7c-.4-.4-.9-.4-1.3 0L4 8.4c-.4.4-.4.9 0 1.3l1.3 1.2c.3.4.9.4 1.3 0l2.1-2.1c.4-.4 1-.1 1 .4v12.5c0 .5.5.9 1 .9h1.8c.5 0 .9-.5.9-.9V9.2c0-.5.7-.8 1-.4l2.2 2.1c.4.4.9.4 1.3 0l1.2-1.2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="attach"><path  d="M8.1 16.9c.3.3.7.3 1 0l4.6-4.6c.3-.3.9-.3 1.3 0 .4.4.4 1 0 1.4l-5.7 5.6c-1.2 1.2-3.3 1.2-4.5 0l-.1-.1c-1.2-1.2-1.2-3.3 0-4.5l10-10c1.3-1.3 3.3-1.3 4.6 0 1.3 1.3 1.3 3.3 0 4.6-.2.3-.3.6-.1.9.3.5.5 1 .6 1.6.1.3.6.4.8.2l.7-.7c2.4-2.4 2.4-6.2 0-8.6h-.1C18.9.4 15 .4 12.7 2.7l-10 10c-2.4 2.3-2.4 6.2 0 8.5l.1.1c2.3 2.4 6.1 2.4 8.5 0l5.7-5.7c1.5-1.4 1.4-3.8-.1-5.3-1.5-1.4-3.9-1.3-5.3.1L7.1 15c-.3.2-.3.7 0 1l1 .9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="back"><path  d="M22.4 10.6H7.1c-.4 0-.6-.5-.3-.8l4.4-4.4c.3-.3.3-.7 0-1l-1-1c-.3-.3-.7-.3-1 0l-8 8.1c-.3.3-.3.7 0 1l8 8.1c.3.3.7.3 1 0l1-1c.2-.3.2-.7 0-1l-4.5-4.4c-.2-.3-.1-.8.4-.8h15.3c.4 0 .7-.3.7-.7v-1.3c0-.4-.3-.8-.7-.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="ban"><path  d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zM3.7 12c0-4.6 3.7-8.3 8.3-8.3 1.8 0 3.5.5 4.8 1.5L5.2 16.8c-1-1.3-1.5-3-1.5-4.8zm8.3 8.3c-1.8 0-3.5-.5-4.8-1.5L18.8 7.2c1 1.3 1.5 3 1.5 4.8 0 4.6-3.7 8.3-8.3 8.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="bold"><path  d="M18.9 8.8c0-2.8-2.2-5.1-4.8-5.1H6.5c-.5 0-1 .4-1 .9v15.2c0 .6.5 1 1 1h7.6c2.6 0 4.8-2.3 4.8-5.1 0-1.3-.5-2.5-1.3-3.5.8-.9 1.3-2.1 1.3-3.4zm-4.8 8.7H8.8v-3.7h5.3c.9 0 1.6.9 1.6 1.9s-.7 1.8-1.6 1.8zm0-6.9H8.8V6.9h5.3c.9 0 1.6.9 1.6 1.9s-.7 1.8-1.6 1.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="bookmark"><path  d="M17.2 22.9l-4.6-4.6c-.2-.3-.6-.3-.9 0l-4.9 4.6c-.3.3-.8.1-.8-.3V2.8C6 1.8 6.8.9 7.8.9h8.4c1 0 1.8.9 1.8 1.9v19.8c0 .4-.5.6-.8.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="breadcrumbs"><path  d="M3.9 19.4c-.3 0-.5-.2-.5-.5V5c0-.2.2-.4.5-.4h3.7c.2 0 .*******l5.3 6.7c.2.3.2.6 0 .9l-5.4 6.8c-.1.1-.3.2-.6.2H3.9zm16.5-7.9L15 4.8c-.2-.2-.6-.3-.9 0l-1.1.8c-.3.3-.4.7-.1 1l4.4 5.4-4.4 5.4c-.2.3-.2.8.1 1l1.1.9c.3.2.7.2.9-.1l5.4-6.7c.2-.4.2-.7 0-1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="broadcast"><path d="M.9 8.8c0 2.7 1.1 5.2 3 7.1.4.4 1 .4 1.4 0 .4-.5.4-1.1 0-1.5C3.8 12.9 3 10.9 3 8.8c0-2.2.8-4.2 2.3-5.7.4-.4.4-1 0-1.4-.4-.4-1-.4-1.4 0C2 3.6.9 6.1.9 8.8zm5.9 4.1c.4.4 1 .4 1.5 0 .4-.4.4-1 0-1.4-.7-.8-1.2-1.7-1.2-2.7 0-1.1.4-2 1.2-2.7.4-.4.4-1.1 0-1.5-.5-.4-1.1-.4-1.5 0-1.1 1.1-1.7 2.6-1.7 4.2 0 1.5.6 3 1.7 4.1zM20.1 1.7c-.4-.4-1.1-.4-1.5 0-.4.4-.4 1 0 1.4 1.5 1.5 2.3 3.5 2.4 5.7-.1 2.1-.9 4.1-2.4 5.6-.4.4-.4 1 0 1.5.4.4 1.1.4 1.5 0C22 14 23 11.5 23 8.8c0-2.7-1-5.2-2.9-7.1zm-1.2 7.1c0-1.6-.7-3.1-1.8-4.2-.4-.4-1-.4-1.4 0-.4.4-.4 1.1 0 1.5.7.7 1.1 1.6 1.1 2.7 0 1-.4 1.9-1.1 2.7-.4.4-.4 1 0 1.4.4.4 1 .4 1.4 0 1.1-1.1 1.8-2.6 1.8-4.1zM10.9 11.2c-1-.4-1.6-1.3-1.6-2.5C9.3 7.2 10.5 6 12 6c1.5 0 2.7 1.2 2.7 2.7 0 1.2-.7 2.1-1.7 2.5v10.9c0 .3-.2.5-.5.5h-1.1c-.2 0-.5-.2-.5-.5V11.2z" /></symbol>
		<symbol viewBox="0 0 24 24" id="brush"><path d="M22.8 1.2c-1.6-1.6-10.3 3.4-15.7 12-.2.4-.1.9.3 1.1 1.2.6 2.2 1.6 2.7 2.8.2.5.7.6 1.1.3C19.5 12 24.4 2.9 22.8 1.2zm-17.3 15c-.7 0-1.3.4-1.8.9h-.1c-.2 0-.4.3-.6.7-.7 1.2-.9 2.7-2 4.3-.2.3-.1.7.2.8 1.6.5 4.4 0 5.8-1v.1c.4-.1.3-.3.4-.3.5-.9 1-1.4 1-2.3-.1-1.7-1.3-3.2-2.9-3.2z" /></symbol>
		<symbol viewBox="0 0 24 24" id="bucket"><path  d="M22.6 5.1c0-2.9-4.5-4.2-8.8-4.2S5.1 2.2 5.1 5.1v.2C1.1 6.5.5 9 .5 10.4c0 1.4.7 2.8 1.9 3.9 1 .8 2.3 1.3 3.6 1.4h.4c3-.1 5.9-1.1 6.8-2.7-.5-.4-.7-.9-.7-1.5 0-1 .8-1.8 1.8-1.8s1.9.8 1.9 1.8c0 .8-.5 1.5-1.2 1.7-.9 2.6-4.6 4.3-9 4.3v2.8c0 1.5 3.5 2.8 7.8 2.8s7.9-1.3 7.9-2.8V7.1c.6-.6.9-1.2.9-2zm-8.8-1.4c3.1 0 5 .7 5.8 1.2.1.1.1.3 0 .4-.8.5-2.7 1.2-5.8 1.2s-4.9-.7-5.7-1.2c-.1-.1-.1-.3 0-.4.8-.5 2.7-1.2 5.7-1.2zM3.6 12.8c-.8-.6-1.3-1.5-1.3-2.4 0-2 1.9-3 3.6-3.4l.1.1v6.7c-.9 0-1.8-.4-2.4-1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="builder"><path d="M5.3 7.8H1.6c-.4 0-.7.4-.7.7v11.8c0 1 .9 1.9 1.9 1.9h2.5c.4 0 .7-.4.7-.7v-13c0-.3-.3-.7-.7-.7zm17.1 0H8.5c-.3 0-.7.4-.7.7v13c0 .*******.7h12.7c1 0 1.9-.9 1.9-1.9V8.5c0-.3-.3-.7-.7-.7zm-1.2-6H2.8c-1 0-1.9.9-1.9 1.9v1.6c0 .*******.7h20.8c.4 0 .7-.3.7-.7V3.7c0-1-.9-1.9-1.9-1.9z" /></symbol>
		<symbol viewBox="0 0 24 24" id="call"><path  d="M22.4 17.5l-2.8-2.3c-.7-.5-1.6-.5-2.2 0L15 16.9c-.3.3-.7.2-1-.1l-3.6-3.2L7.2 10c-.3-.3-.3-.6-.1-1l1.7-2.4c.5-.6.5-1.5 0-2.2L6.5 1.6C5.8.8 4.6.7 3.8 1.5L1.4 3.9c-.4.3-.6.9-.6 1.4.3 4.7 2.4 9.1 5.5 12.3s7.6 5.2 12.3 5.5c.6 0 1.1-.2 1.4-.6l2.4-2.4c.9-.7.9-2 0-2.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="capslock"><path d="M20.1 9.7l-7.5-8.5c-.3-.4-.9-.4-1.2 0L3.9 9.7c-.3.4-.1.9.4.9h3.5v5.8c0 .*******.7h7c.3 0 .7-.3.7-.7v-5.8h3.5c.5 0 .7-.5.4-.9zm-4.6 10.1h-7c-.3 0-.7.4-.7.7v1.9c0 .*******.7h7c.3 0 .7-.3.7-.7v-1.9c0-.3-.4-.7-.7-.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="cases"><path d="M4.2 1.6c0-.4-.4-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v14.3c0 .*******.7h1.9c.3 0 .7-.3.7-.7V1.6zm18.9 0c0-.4-.3-.7-.7-.7h-1.9c-.3 0-.7.3-.7.7v14.3c0 .*******.7h1.9c.4 0 .7-.3.7-.7V1.6zM17.3.9h-1.4c-.3 0-.7.4-.7.8v5.7c0 .2.1.3.3.4.8.4 1.5.9 2.1 1.5.1.2.4.1.4-.1V1.7c0-.4-.3-.8-.7-.8zM11.1 7h1.8c.3 0 .5-.2.5-.4V1.7c0-.4-.3-.8-.7-.8h-1.4c-.4 0-.7.4-.7.8v4.9c0 .2.2.5.5.4zM6.4 9.3c.6-.6 1.4-1.1 2.1-1.5.2-.1.3-.2.3-.4V1.7c0-.4-.4-.8-.7-.8H6.7c-.4 0-.7.4-.7.8v7.5c0 .2.2.3.4.1zm5.6-.5c-3.3 0-6 2.7-6 6 0 1 .3 2 .7 2.9L3.5 21c-.3.2-.3.6 0 .9l.9 1c.3.3.7.3 1 0l3.2-3.2c1 .7 2.2 1.1 3.4 1.1 3.3 0 6-2.7 6-6s-2.7-6-6-6zm0 9.2c-1.8 0-3.2-1.4-3.2-3.2s1.4-3.2 3.2-3.2 3.2 1.4 3.2 3.2S13.8 18 12 18z" /></symbol>
		<symbol viewBox="0 0 24 24" id="center_align_text"><path  d="M22.2 3c0-.4-.4-.7-.7-.7h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.3.7-.7V3zm-2.8 5.5c0-.3-.3-.7-.7-.7H5.3c-.4 0-.7.4-.7.7v1.4c0 .*******.7h13.4c.4 0 .7-.3.7-.7V8.5zm-.9 11.1c0-.4-.4-.7-.7-.7H6.2c-.3 0-.7.3-.7.7V21c0 .*******.7h11.6c.3 0 .7-.3.7-.7v-1.4zm3.7-5.5c0-.4-.4-.7-.7-.7h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.4.7-.7v-1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="change_owner"><path  d="M12.6 17.4c-1.4-.6-1.6-1.1-1.6-1.7 0-.5.4-1 .8-1.4.8-.7 1.2-1.8 1.2-3 0-2.2-1.3-3.9-3.8-3.9s-3.8 1.7-3.8 3.9c0 1.2.3 2.3 1.2 3 .4.4.8.9.8 1.4 0 .6-.2 1.1-1.6 1.7-2.1.8-4 1.7-4 3.5 0 1.2 1 2.2 2.1 2.2h10.6c1.2 0 2.1-1 2.1-2.2 0-1.7-2-2.7-4-3.5zm7.9-8.6c0-3.4-2.8-6.3-6.2-6.3V.9l-3.1 2.6c-.2.1-.1.3 0 .5l3.1 2.5V4.8c2.2 0 3.9 1.8 3.9 4h-1.6l2.6 3.1c.1.1.3.1.5 0l2.5-3.1h-1.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="change_record_type"><path  d="M9.2 17.3c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v5.1c0 .*******.7h6.9c.4 0 .7-.3.7-.7v-5.1zm-5.5-7.1H1.5c-.5 0-.7.4-.4.6l3.7 3.8c.1.2.4.2.6 0l3.7-3.8c.3-.3 0-.6-.4-.6H6.5c0-2.4 2.3-4.7 4.6-4.7V2.8c-4.2 0-7.4 3.2-7.4 7.4zm15.6-.8c-.2-.2-.5-.2-.7 0L15 13.2c-.3.3-.1.6.4.6h2.2c0 2.8-1.9 4.7-4.7 4.7v2.7c4.2 0 7.5-3.2 7.5-7.4h2.2c.5 0 .7-.4.4-.6l-3.7-3.8zm3.8-7.8c0-.4-.3-.7-.7-.7h-6.9c-.4 0-.7.3-.7.7v5.1c0 .*******.7h6.9c.4 0 .7-.3.7-.7V1.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="chart"><path d="M21 10.8L11.5 16c-.6.3-1.3-.1-1.3-.8V3.9c0-.5-.5-.9-.9-.7-4.6 1.3-8 5.8-7.4 10.9.5 4.6 4.2 8.4 8.9 8.9 6.2.7 11.4-4.1 11.4-10.1 0-.5-.1-1.1-.2-1.6s-.6-.7-1-.5zm-8.2 2.1l9.1-4.8c.5-.3.7-1 .3-1.5-2-2.9-5.3-5-9-5.6-.6-.1-1.2.4-1.2 1v10.5c0 .4.4.6.8.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="chat"><path  d="M12 1.8C5.9 1.8 1 6.4 1 12c0 1.7.5 3.4 1.3 4.8.1.3.2.6.1.8l-1.4 4c-.2.3.2.6.6.6l3.9-1.6c.3-.1.5 0 .8.1 1.7.9 3.7 1.5 5.8 1.5 6 0 11-4.5 11-10.2C23 6.4 18.1 1.8 12 1.8zm-5.5 12c-1.1 0-1.9-.8-1.9-1.8s.8-1.8 1.9-1.8 1.8.8 1.8 1.8-.8 1.8-1.8 1.8zm5.5 0c-1 0-1.8-.8-1.8-1.8s.8-1.8 1.8-1.8 1.8.8 1.8 1.8-.8 1.8-1.8 1.8zm5.5 0c-1 0-1.8-.8-1.8-1.8s.8-1.8 1.8-1.8 1.9.8 1.9 1.8-.8 1.8-1.9 1.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="check"><path  d="M8.8 19.6L1.2 12c-.3-.3-.3-.8 0-1.1l1-1c.3-.3.8-.3 1 0L9 15.7c.1.2.5.2.6 0L20.9 4.4c.2-.3.7-.3 1 0l1 1c.3.3.3.7 0 1L9.8 19.6c-.2.3-.7.3-1 0z"/></symbol>
		<symbol viewBox="0 0 24 24" id="checkin"><path  d="M12 .9C7.2.9 3.2 4.8 3.2 9.7c0 6.1 6.3 11.7 8.2 13.2.4.3.8.3 1.2 0 1.9-1.5 8.2-7.1 8.2-13.2 0-4.9-4-8.8-8.8-8.8zm0 12.5c-2 0-3.7-1.7-3.7-3.7S10 6 12 6s3.7 1.7 3.7 3.7-1.7 3.7-3.7 3.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="chevrondown"><path  d="M22 8.2l-9.5 9.6c-.3.2-.7.2-1 0L2 8.2c-.2-.3-.2-.7 0-1l1-1c.3-.3.8-.3 1.1 0l7.4 7.5c.3.3.7.3 1 0l7.4-7.5c.3-.2.8-.2 1.1 0l1 1c.2.3.2.7 0 1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="chevronleft"><path  d="M15.8 22l-9.6-9.4c-.3-.3-.3-.8 0-1.1l9.6-9.4c.3-.3.7-.3 1 0l1 1c.3.3.3.7 0 1l-7.6 7.4c-.3.3-.3.8 0 1.1l7.5 7.4c.3.3.3.7 0 1l-1 1c-.2.2-.6.2-.9 0z"/></symbol>
		<symbol viewBox="0 0 24 24" id="chevronright"><path  d="M8.3 2l9.5 9.5c.3.3.3.7 0 1L8.3 22c-.3.2-.8.2-1.1 0l-1-1c-.2-.3-.2-.8 0-1.1l7.6-7.4c.2-.3.2-.7 0-1L6.3 4.1C6 3.8 6 3.3 6.3 3l1-1c.3-.2.7-.2 1 0z"/></symbol>
		<symbol viewBox="0 0 24 24" id="chevronup"><path  d="M2 15.8l9.5-9.6c.3-.2.7-.2 1 0l9.5 9.6c.2.3.2.7 0 1l-1 1c-.3.3-.8.3-1.1 0l-7.4-7.6c-.3-.2-.7-.2-1 0l-7.4 7.6c-.3.2-.8.2-1.1 0l-1-1c-.2-.3-.2-.7 0-1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="clear"><path  d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zm2.3 11.5l3.6 3.6c.1.2.1.4 0 .6l-1.3 1.3c-.2.2-.5.2-.7 0l-3.6-3.6c-.2-.2-.4-.2-.6 0l-3.6 3.6c-.2.2-.5.2-.7 0l-1.3-1.3c-.1-.2-.1-.4 0-.6l3.6-3.6c.2-.2.2-.5 0-.7L6.1 8.1c-.2-.2-.2-.5 0-.7l1.3-1.3c.2-.1.4-.1.6 0l3.7 3.7c.2.2.4.2.6 0l3.6-3.6c.2-.2.5-.2.7 0l1.3 1.3c.1.2.1.4 0 .6l-3.6 3.6c-.2.2-.2.5 0 .7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="clock"><path d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zm0 19.4c-4.6 0-8.3-3.7-8.3-8.3S7.4 3.7 12 3.7s8.3 3.7 8.3 8.3-3.7 8.3-8.3 8.3zm1.6-8.2c-.2-.1-.2-.3-.2-.5V7.2c0-.4-.3-.7-.7-.7h-1.4c-.4 0-.7.3-.7.7v5.5c0 .2.1.4.2.5l3.4 3.5c.3.2.7.2 1 0l1-1c.2-.3.2-.7 0-1l-2.6-2.6z" /></symbol>
		<symbol viewBox="0 0 24 24" id="close"><path  d="M14.3 11.7l6-6c.3-.3.3-.7 0-1l-.9-1c-.3-.2-.7-.2-1 0l-6 6.1c-.2.2-.5.2-.7 0l-6-6.1c-.3-.3-.7-.3-1 0l-1 1c-.2.2-.2.7 0 .9l6.1 6.1c.2.2.2.4 0 .6l-6.1 6.1c-.3.3-.3.7 0 1l1 1c.2.2.7.2.9 0l6.1-6.1c.2-.2.4-.2.6 0l6.1 6.1c.2.2.7.2.9 0l1-1c.3-.3.3-.7 0-1l-6-6c-.2-.2-.2-.5 0-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="comments"><path  d="M22.1 14.3c-.1-.2-.1-.4 0-.5.6-1.1 1-2.3 1-3.6 0-4.1-3.5-7.4-7.9-7.4-2 0-3.8.8-5.2 2 4.7.5 8.5 4.4 8.5 9.1 0 1.1-.3 2.3-.7 3.3.5-.2 1-.4 1.5-.7.2-.1.4-.1.5 0l2.9 1.1c.2.1.5-.2.4-.4l-1-2.9zM8.8 6.5C4.4 6.5.9 9.8.9 13.9c0 1.3.4 2.5 1 3.5.1.2.1.4 0 .6l-1 2.8c-.1.3.2.5.4.4l2.9-1.1c.1 0 .3 0 .5.1 1.2.7 2.6 1 4.1 1 4.3 0 7.8-3.3 7.8-7.4 0-4-3.5-7.3-7.8-7.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="company"><path  d="M9.7 1.8H3.2c-.8 0-1.4.6-1.4 1.4v18.5c0 .2.3.5.5.5h1.9c.2 0 .4-.2.4-.5v-2.8c0-.3.2-.4.5-.4h2.7c.3 0 .5.1.5.4v2.8c0 .3.2.5.5.5h1.4c.5 0 .9-.5.9-1v-18c0-.8-.6-1.4-1.4-1.4zM5.5 16.4c0 .1-.1.2-.2.2H3.9c-.1 0-.2-.1-.2-.2v-2.3c0-.2.1-.3.2-.3h1.4c.1 0 .2.1.2.3v2.3zm0-4.6c0 .1-.1.2-.2.2H3.9c-.1 0-.2-.1-.2-.2V9.5c0-.2.1-.3.2-.3h1.4c.1 0 .2.1.2.3v2.3zm0-4.6c0 .1-.1.2-.2.2H3.9c-.1 0-.2-.1-.2-.2V4.8c0-.1.1-.2.2-.2h1.4c.1 0 .2.1.2.2v2.4zm3.7 9.2c0 .1-.1.2-.2.2H7.6c-.1 0-.2-.1-.2-.2v-2.3c0-.2.1-.3.2-.3H9c.1 0 .2.1.2.3v2.3zm0-4.6c0 .1-.1.2-.2.2H7.6c-.1 0-.2-.1-.2-.2V9.5c0-.2.1-.3.2-.3H9c.1 0 .2.1.2.3v2.3zm0-4.6c0 .1-.1.2-.2.2H7.6c-.1 0-.2-.1-.2-.2V4.8c0-.1.1-.2.2-.2H9c.1 0 .2.1.2.2v2.4zm11.6-.7h-6.5c-.8 0-1.4.6-1.4 1.3v13.9c0 .2.3.5.5.5h1.8c.3 0 .5-.2.5-.5v-2.8c0-.3.2-.4.5-.4h2.7c.3 0 .5.1.5.4v2.8c0 .3.2.5.4.5h1.4c.5 0 1-.5 1-1V7.8c0-.7-.6-1.3-1.4-1.3zm-4.2 9.9c0 .1-.1.2-.2.2H15c-.1 0-.2-.1-.2-.2v-2.3c0-.2.1-.3.2-.3h1.4c.1 0 .2.1.2.3v2.3zm0-4.6c0 .1-.1.2-.2.2H15c-.1 0-.2-.1-.2-.2V9.5c0-.2.1-.3.2-.3h1.4c.1 0 .2.1.2.3v2.3zm3.7 4.6c0 .1-.1.2-.2.2h-1.4c-.1 0-.2-.1-.2-.2v-2.3c0-.2.1-.3.2-.3h1.4c.1 0 .2.1.2.3v2.3zm0-4.6c0 .1-.1.2-.2.2h-1.4c-.1 0-.2-.1-.2-.2V9.5c0-.2.1-.3.2-.3h1.4c.1 0 .2.1.2.3v2.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="connected_apps"><path  d="M11 14.4l-1.8 8.1c-.1.5.5.8.8.4l9.7-12c.3-.3.1-.7-.3-.7h-5.2c-.4 0-.6-.5-.4-.7L18.4 2c.2-.5-.1-1.1-.6-1.1H9.6c-.5 0-.9.3-1.1.8L4.7 12.9c-.2.5.1.9.6.9h5.3c.2 0 .5.3.4.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="contract"><path  d="M13.7 11.1h7.1c.4 0 .6-.5.2-.9l-2.3-2.3 4.2-4.2c.2-.2.2-.7 0-.9l-1.7-1.7c-.2-.2-.6-.2-.9.1l-4.1 4.1L13.8 3c-.4-.3-.9-.2-.9.3v7.1c0 .3.4.7.8.7zm-3.4 1.8H3.2c-.4 0-.6.5-.2.9l2.3 2.3-4.2 4.2c-.2.2-.2.7 0 .9l1.7 1.7c.2.2.6.2.9 0l4.2-4.2 2.3 2.3c.4.4.9.2.9-.2v-7.1c0-.3-.4-.8-.8-.8zm2.6.8v7.1c0 .4.5.6.9.2l2.3-2.3 4.2 4.2c.2.2.7.2.9 0l1.7-1.7c.2-.2.2-.6-.1-.9l-4.1-4.1 2.3-2.4c.3-.4.2-.9-.3-.9h-7.1c-.3 0-.7.4-.7.8zm-1.8-3.4V3.2c0-.4-.5-.6-.9-.2L7.9 5.3 3.7 1.1c-.2-.2-.7-.2-.9 0L1.1 2.8c-.2.2-.2.6 0 .9l4.2 4.2L3 10.2c-.4.4-.2.9.2.9h7.1c.3 0 .8-.4.8-.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="contract_alt"><path  d="M13.7 11h7.1c.4 0 .6-.5.2-.8l-2.3-2.4 4.2-4.2c.2-.2.2-.6 0-.8l-1.7-1.7c-.2-.2-.6-.2-.9 0l-4.1 4.2L13.8 3c-.4-.4-.9-.2-.9.2v7.1c0 .4.4.7.8.7zm-3.4 1.9H3.2c-.4 0-.6.5-.2.9l2.3 2.3-4.2 4.2c-.2.2-.2.7 0 .9l1.7 1.7c.2.2.6.2.9 0l4.2-4.2 2.3 2.3c.4.4.9.2.9-.2v-7.1c0-.3-.4-.8-.8-.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="copy"><path  d="M20.3.9h-12c-1 0-1.8.9-1.8 1.9v.9h11c1.1 0 1.9.8 1.9 1.8v13h.9c1 0 1.9-.9 1.9-1.9V2.8c0-1-.9-1.9-1.9-1.9zm-2.8 6.5c0-1-.8-1.9-1.8-1.9h-12c-1 0-1.9.9-1.9 1.9v13.8c0 1 .9 1.9 1.9 1.9h12c1 0 1.8-.9 1.8-1.9V7.4zm-8.3 3.2c0 .3-.2.5-.4.5H5.1c-.3 0-.5-.2-.5-.5v-.9c0-.3.2-.5.5-.5h3.7c.2 0 .4.2.4.5v.9zm3.7 7.4c0 .3-.2.5-.4.5H5.1c-.3 0-.5-.2-.5-.5v-.9c0-.3.2-.5.5-.5h7.4c.2 0 .4.2.4.5v.9zm1.9-3.7c0 .3-.2.5-.5.5H5.1c-.3 0-.5-.2-.5-.5v-.9c0-.3.2-.5.5-.5h9.2c.3 0 .5.2.5.5v.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="crossfilter"><path  d="M16.2 4.2c-.8 0-1.6 0-2.3.3.9.7 1.6 1.5 2.2 2.4h.1c2.8 0 5 2.3 5 5.1s-2.2 5.1-5 5.1c-.7 0-1.4-.2-2-.4.3-.5.7-1.1.9-1.7.1-.2.2-.4.2-.6.3-.7.4-1.6.4-2.4 0-4.3-3.5-7.8-7.9-7.8S0 7.7 0 12s3.5 7.8 7.8 7.8c.8 0 1.6 0 2.3-.3-.9-.7-1.6-1.5-2.2-2.4h-.1c-2.8 0-5-2.3-5-5.1s2.2-5.1 5-5.1c.7 0 1.4.2 2.1.4-1 1.3-1.6 2.9-1.6 4.7 0 4.3 3.5 7.8 7.9 7.8S24 16.3 24 12s-3.5-7.8-7.8-7.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="custom_apps"><path  d="M22.8 5.6c-.1-.2-.4-.3-.6-.1l-3.8 3.7c-.3.3-.7.3-1 0l-2.6-2.6c-.3-.3-.3-.7 0-1l3.8-3.8c.1-.1 0-.5-.2-.6-.6-.2-1.3-.3-2-.3-3.9 0-7 3.4-6.6 7.4.1.7.3 1.2.5 1.8l-8.6 8.5c-1.1 1.1-1.1 2.7 0 3.7.5.5 1.2.8 1.8.8s1.3-.3 1.9-.8l8.5-8.6c.6.2 1.2.4 1.8.5 4 .4 7.4-2.7 7.4-6.6 0-.7-.1-1.4-.3-2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="cut"><path d="M18.8 14.5c-.8-.2-1.5-.1-2.2.1L6.4 1.1C6.3.9 6 .9 5.8 1l-.4.3c-.8.6-.9 1.7-.3 2.6l4.9 6.4c.2.3.2.6 0 .9l-2.7 3.4c-.6-.2-1.4-.2-2.1-.1-1.7.4-3.1 1.7-3.3 3.5-.4 2.7 2 5 4.8 4.6 1.7-.3 3-1.6 3.4-3.2.2-1.2 0-2.2-.5-3l1.9-2.6c.3-.4.8-.4 1.1 0l1.9 2.6c-.5.9-.7 1.9-.5 3 .3 1.6 1.7 2.9 3.4 3.2 2.8.4 5.2-1.9 4.8-4.6-.4-1.8-1.8-3.2-3.4-3.5zM6 19.9c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.3.6 1.3 1.4-.6 1.4-1.3 1.4zm12 0c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.3.6 1.3 1.4c0 .8-.6 1.4-1.3 1.4zM14.4 8.7c.2.3.6.3.8 0l3.7-4.8c.5-.7.4-1.6-.1-2.3h.1-.1c-.1-.1-.7-.6-.7-.6-.1-.1-.5-.1-.6.1l-4.1 5.4c-.2.2-.2.6 0 .8l1 1.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="dash"><path  d="M23.1 12.7c0 .4-.3.7-.7.7H1.6c-.4 0-.7-.3-.7-.7v-1.4c0-.4.3-.7.7-.7h20.8c.4 0 .7.3.7.7v1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="database"><path  d="M21 4.2C21 2.4 16.9 1 12 1S3 2.4 3 4.2v1.1C3 7 7.1 8.4 12 8.4s9-1.4 9-3.1V4.2zM3 7.8c0 1.4 4.1 2.5 9 2.5s9-1.1 9-2.5v2.3c0 1.8-4.1 3.2-9 3.2s-9-1.4-9-3.2V7.8zm0 0c0 1.4 4.1 2.5 9 2.5s9-1.1 9-2.5v2.3c0 1.8-4.1 3.2-9 3.2s-9-1.4-9-3.2V7.8zm0 4.9c0 1.4 4.1 2.5 9 2.5s9-1.1 9-2.5V15c0 1.7-4.1 3.1-9 3.1S3 16.8 3 15v-2.3zm0 4.9c0 1.4 4.1 2.5 9 2.5s9-1.1 9-2.5v2.2c0 1.8-4.1 3.2-9 3.2s-9-1.4-9-3.2v-2.2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="datadotcom"><path  d="M19.6 22.7h-2.4c-.2-.9-.3-1.7-.3-2.4-1.2 1.9-3.1 2.8-5.6 2.8-2.3 0-4-.8-5.2-2.3-1.2-1.5-1.7-3.5-1.7-5.9 0-1.6.3-2.9.9-4.2s1.4-2.3 2.5-2.9c1.1-.8 2.4-1.2 3.9-1.2 2.1 0 3.8.7 5.1 2.1V1h2.6v18.2c0 1.2.1 2.3.2 3.5zM16.8 16v-5c-.6-.8-1.3-1.3-2.1-1.7-.8-.4-1.7-.6-2.7-.6-1.5 0-2.7.6-3.6 1.7-.9 1.2-1.3 2.7-1.3 4.5 0 1.9.4 3.3 1.3 4.4.8 1.1 2 1.7 3.5 1.7 1.4 0 2.6-.5 3.5-1.4.9-1 1.4-2.2 1.4-3.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="dayview"><path  d="M20.3 3.2H18v-.9c0-.7-.6-1.4-1.4-1.4-.7 0-1.4.6-1.4 1.4v.9H8.8v-.9c0-.7-.6-1.4-1.4-1.4C6.6.9 6 1.5 6 2.3v.9H3.7c-1 0-1.9.9-1.9 1.9v1.1c0 .*******.7h19c.3 0 .7-.3.7-.7V5.1c0-1-.9-1.9-1.9-1.9zm1.2 6h-19c-.3 0-.7.4-.7.7v11.3c0 1 .9 1.9 1.9 1.9h16.6c1 0 1.9-.9 1.9-1.9V9.9c0-.3-.4-.7-.7-.7zm-8.1 10.2v.1c0 .3-.5.8-.9.8s-1-.5-1-.9v-4.6l-.7.7c-.1.1-.2.2-.4.2-.4 0-.7-.3-.7-.7 0-.2.1-.4.2-.5l1.8-1.8c.2-.2.4-.3.7-.3.5 0 1 .4 1 .9v6.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="delete"><path d="M21 4.6h-5.8V2.8c0-1-.8-1.9-1.8-1.9h-2.8c-1 0-1.8.9-1.8 1.9v1.8H3c-.4 0-.7.3-.7.7v1.4c0 .*******.7h18c.4 0 .7-.3.7-.7V5.3c0-.4-.3-.7-.7-.7zM10.6 3.2c0-.2.2-.4.5-.4h1.8c.3 0 .5.2.5.4v1.4h-2.8V3.2zm8.6 6H4.8c-.3 0-.6.4-.6.7v10.9c0 1.3 1 2.3 2.3 2.3h11c1.3 0 2.3-1 2.3-2.3V9.9c0-.3-.3-.7-.6-.7zm-8.6 10.2c0 .3-.2.4-.4.4h-1c-.2 0-.4-.1-.4-.4v-6.5c0-.3.2-.4.4-.4h1c.2 0 .4.1.4.4v6.5zm4.6 0c0 .3-.2.4-.4.4h-1c-.2 0-.4-.1-.4-.4v-6.5c0-.3.2-.4.4-.4h1c.2 0 .4.1.4.4v6.5z" /></symbol>
		<symbol viewBox="0 0 24 24" id="deprecate"><path  d="M22.2 3.2H1.8c-.5 0-.9.4-.9 1v12c0 .*******.9h7.5c.5 2.6 2.7 4.6 5.5 4.6s5-2 5.4-4.6h2c.5 0 .9-.4.9-.9v-12c0-.6-.4-1-.9-1zm-4 15.1l-1.3 1.3-2.1-2.2-2.2 2.2-1.2-1.3 2.1-2.1-2.1-2.2 1.2-1.3 2.2 2.2 2.1-2.2 1.3 1.3-2.1 2.2 2.1 2.1zm3-3.1h-1c-.4-2.6-2.7-4.6-5.4-4.6s-5.1 2-5.5 4.6H2.8V5.1h18.4v10.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="description"><path  d="M20.3 1.8H3.7c-1 0-1.9.9-1.9 1.9v16.6c0 1 .9 1.9 1.9 1.9h16.6c1 0 1.9-.9 1.9-1.9V3.7c0-1-.9-1.9-1.9-1.9zM5.5 6.5c0-.3.2-.5.5-.5h4.6c.3 0 .5.2.5.5v4.6c0 .3-.2.4-.5.4H6c-.3 0-.5-.1-.5-.4V6.5zm11.1 12c0 .2-.2.4-.4.4H6c-.3 0-.5-.2-.5-.4v-1c0-.2.2-.4.5-.4h10.2c.2 0 .4.2.4.4v1zm1.9-3.7c0 .2-.2.4-.5.4H6c-.3 0-.5-.2-.5-.4v-1c0-.2.2-.4.5-.4h12c.3 0 .5.2.5.4v1zm0-3.7c0 .3-.2.4-.5.4h-4.6c-.3 0-.5-.1-.5-.4v-.9c0-.3.2-.5.5-.5H18c.3 0 .5.2.5.5v.9zm0-3.7c0 .3-.2.4-.5.4h-4.6c-.3 0-.5-.1-.5-.4v-.9c0-.3.2-.5.5-.5H18c.3 0 .5.2.5.5v.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="desktop"><path d="M23.1 2.8c0-1-.9-1.9-1.9-1.9H2.8C1.8.9.9 1.8.9 2.8v12c0 1 .9 1.8 1.9 1.8h18.4c1 0 1.9-.8 1.9-1.8v-12zm-2.8 10.4c0 .3-.3.6-.7.6H4.4c-.4 0-.7-.3-.7-.6V4.4c0-.4.3-.7.7-.7h15.2c.4 0 .7.3.7.7v8.8zm-5.1 7.1h-1.4c-.2 0-.4-.2-.4-.5v-.9c0-.3-.2-.4-.5-.4h-1.8c-.3 0-.5.1-.5.4v.9c0 .3-.2.5-.4.5H8.8c-1 0-1.9.8-1.9 1.9v.2c0 .*******.7h8.8c.4 0 .7-.3.7-.7v-.2c0-1.1-.9-1.9-1.9-1.9z" /></symbol>
		<symbol viewBox="0 0 24 24" id="dislike"><path  d="M5.5 13.6V4.2c0-1.1-.8-1.9-1.8-1.9H2.5c-.3 0-.7.3-.7.7v10.6c0 .*******.7h2.3c.4 0 .7-.3.7-.7zM22.2 12V6.2c0-4-3.2-4.4-6.7-4.4-3.3 0-4.3 1.3-7.5 1.4-.3 0-.6.4-.6.7v9.3c0 .3.3.6.7.6 2.2 0 3.9 2.4 3.9 4.9v2.8c0 .*******.7h1.1c1.1 0 1.9-.9 1.9-1.9v-4.6c0-1 .8-1.9 1.8-1.9h2.8c1 0 1.9-.8 1.9-1.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="dock_panel"><path  d="M.9 16.2v-12C.9 2.4 2.3.9 4.1.9h12c1.8 0 3.2 1.5 3.2 3.3v1.3h-2.7V4.2c0-.3-.2-.5-.5-.5h-12c-.3 0-.5.2-.5.5v12c0 .2.2.4.5.4h1.4v2.8H4.1C2.3 19.4.9 18 .9 16.2zm20.3-8.8h-12c-1 0-1.9.8-1.9 1.8v12c0 1 .9 1.9 1.9 1.9h12c1 0 1.8-.9 1.8-1.9v-12c0-1-.8-1.8-1.8-1.8zM19.3 19c0 .2-.1.4-.4.4h-5.3c-.3 0-.5-.2-.5-.4v-.9c0-.2.2-.4.5-.4h2.2c.3 0 .4-.3.2-.5l-4.8-4.8c-.2-.1-.2-.4 0-.6l.6-.6c.2-.1.4-.1.6 0l4.8 4.8c.2.2.4.1.4-.2v-2.2c0-.2.3-.4.5-.4h.9c.2 0 .3.2.3.4V19z"/></symbol>
		<symbol viewBox="0 0 24 24" id="down"><path  d="M3.8 6.5h16.4c.4 0 .8.6.4 1l-8 9.8c-.3.3-.9.3-1.2 0l-8-9.8c-.4-.4-.1-1 .4-1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="download"><path d="M22.4 14.3H21c-.4 0-.7.3-.7.7v4.6c0 .4-.3.7-.7.7H4.4c-.4 0-.7-.3-.7-.7V15c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v6.2c0 1 .9 1.9 1.9 1.9h18.4c1 0 1.9-.9 1.9-1.9V15c0-.4-.3-.7-.7-.7zm-10.9 3.1c.3.2.7.2 1 0l6.2-6.3c.3-.3.3-.7 0-.9l-.9-1c-.3-.3-.7-.3-1 0l-2.6 2.6c-.3.2-.8.1-.8-.4V1.6c0-.4-.4-.7-.7-.7h-1.4c-.4 0-.7.3-.7.7v9.8c0 .4-.5.6-.8.3L7.2 9.1c-.2-.2-.6-.2-.9 0l-1 1.1c-.3.2-.3.6 0 .9l6.2 6.3z" /></symbol>
		<symbol viewBox="0 0 24 24" id="edit"><path d="M4.4 15.4l4.1 4.1c.2.2.5.2.6 0L19.4 9.2c.2-.2.2-.4 0-.6l-4.1-4.1c-.2-.2-.4-.2-.6 0L4.4 14.8c-.2.2-.2.5 0 .6zM16.7 2.6c-.2.2-.2.5 0 .7l4 4c.2.2.5.2.7 0l1.1-1.1c.8-.7.8-1.8 0-2.6l-2.1-2.1c-.8-.8-1.9-.8-2.7 0l-1 1.1zM1 22.2c-.1.5.3.9.8.8l5-1.2c.2 0 .3-.1.4-.2l.1-.1c.1-.1.1-.4-.1-.6l-4.1-4.1c-.2-.2-.5-.2-.6-.1l-.1.1c-.1.1-.2.3-.2.4l-1.2 5z" /></symbol>
		<symbol viewBox="0 0 24 24" id="edit_form"><path d="M14.5 7.1h3.8c.3 0 .5-.3.5-.5s-.1-.3-.2-.4l-4.7-4.7c-.1-.1-.2-.1-.3-.1-.3 0-.5.2-.5.5v3.7c0 .8.6 1.5 1.4 1.5zm8.3 4.8l-.4-.5c-.2-.2-.7-.2-1 0l-5.5 5.5v1.3c0 .1 0 .2.1.2h1.4l5.4-5.5c.4-.3.4-.8 0-1zm-4.4 8.6h-3.3c-.7 0-1.3-.6-1.3-1.3v-2.5c0-.4.1-.8.4-1l4.4-4.4c.1-.1.2-.3.2-.5v-.9c0-.4-.3-.7-.7-.7h-5c-1.2 0-2.2-1-2.2-2.1v-5c0-.4-.3-.7-.7-.7H3C1.9 1.4.9 2.4.9 3.5v17c0 1.1 1 2.1 2.1 2.1h13.6c1 0 2-.7 2.1-1.7.1-.2-.1-.4-.3-.4zM3.8 7.8c0-.4.3-.7.7-.7h2.8c.5 0 .7.3.7.7v.6c0 .4-.3.7-.7.7H4.5c-.4 0-.7-.3-.7-.7v-.6zm7.1 9.1c0 .4-.3.7-.7.7H4.5c-.4 0-.7-.3-.7-.7v-.7c0-.3.3-.6.7-.6h5.7c.4 0 .7.3.7.6v.7zm1.5-4.2c0 .4-.4.7-.7.7H4.5c-.4 0-.7-.3-.7-.7V12c0-.4.3-.7.7-.7h7.1c.4 0 .7.3.7.7v.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="email"><path d="M11.5 13.9c.3.3.7.3 1 0l10.4-9.7c.2-.4.1-1-.6-1l-20.6.1c-.6 0-1.1.5-.6.9l10.4 9.7zM23.1 8c0-.5-.6-.8-.9-.4L14 15.1c-.6.5-1.3.8-2 .8s-1.4-.3-2-.8L1.9 7.6c-.4-.4-.9-.1-.9.4C.9 7.8.9 18.5.9 18.5c0 1 .9 1.8 1.9 1.8h18.4c1 0 1.9-.8 1.9-1.8V8z" /></symbol>
		<symbol viewBox="0 0 24 24" id="end_call"><path  d="M22.4 2.6l-1-1c-.3-.3-.8-.2-1.1.2L9.5 12.6 7.2 10c-.3-.3-.3-.6-.1-1l1.7-2.4c.5-.6.5-1.5 0-2.2L6.5 1.6C5.8.8 4.6.7 3.8 1.5L1.4 3.9c-.4.3-.6.9-.6 1.4.3 4.2 2 8.3 4.6 11.3l-3.6 3.7c-.4.3-.4.8-.2 1.1l1 1c.3.3.8.2 1.1-.2L22.2 3.7c.4-.3.5-.8.2-1.1zm0 14.9l-2.8-2.3c-.7-.5-1.6-.5-2.2 0L15 16.9c-.3.3-.7.2-1-.1l-1.1-1-3.9 4c2.8 1.8 6.1 3.1 9.6 3.3.6 0 1.1-.2 1.4-.6l2.4-2.4c.9-.7.9-2 0-2.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="erect_window"><path  d="M23.1 3c0 .4-.3.7-.7.7H1.6c-.4 0-.7-.3-.7-.7V1.6c0-.4.3-.7.7-.7h20.8c.4 0 .7.3.7.7V3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="error"><path  d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zm5.5 11.9c-.1.3-.3.6-.7.6H7.2c-.4 0-.6-.2-.7-.6v-1.6c.1-.3.3-.6.7-.6h9.6c.4 0 .6.3.7.6v1.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="event"><path d="M21.5 9.2h-19c-.3 0-.7.4-.7.7v11.3c0 1 .9 1.9 1.9 1.9h16.6c1 0 1.9-.9 1.9-1.9V9.9c0-.3-.4-.7-.7-.7zM8.8 19.4c0 .3-.2.4-.5.4H6.5c-.3 0-.5-.1-.5-.4v-1.9c0-.2.2-.4.5-.4h1.8c.3 0 .5.2.5.4v1.9zm0-4.6c0 .2-.2.4-.5.4H6.5c-.3 0-.5-.2-.5-.4v-1.9c0-.3.2-.4.5-.4h1.8c.3 0 .5.1.5.4v1.9zm4.6 4.6c0 .3-.2.4-.5.4h-1.8c-.3 0-.5-.1-.5-.4v-1.9c0-.2.2-.4.5-.4h1.8c.3 0 .5.2.5.4v1.9zm0-4.6c0 .2-.2.4-.5.4h-1.8c-.3 0-.5-.2-.5-.4v-1.9c0-.3.2-.4.5-.4h1.8c.3 0 .5.1.5.4v1.9zm4.6 4.6c0 .3-.2.4-.5.4h-1.8c-.3 0-.5-.1-.5-.4v-1.9c0-.2.2-.4.5-.4h1.8c.3 0 .5.2.5.4v1.9zm0-4.6c0 .2-.2.4-.5.4h-1.8c-.3 0-.5-.2-.5-.4v-1.9c0-.3.2-.4.5-.4h1.8c.3 0 .5.1.5.4v1.9zm2.3-11.6H18v-.9c0-.7-.6-1.4-1.4-1.4-.7 0-1.4.6-1.4 1.4v.9H8.8v-.9c0-.7-.6-1.4-1.4-1.4C6.6.9 6 1.5 6 2.3v.9H3.7c-1 0-1.9.9-1.9 1.9v1.1c0 .*******.7h19c.3 0 .7-.3.7-.7V5.1c0-1-.9-1.9-1.9-1.9z" /></symbol>
		<symbol viewBox="0 0 24 24" id="expand"><path  d="M22.5.9h-7.1c-.5 0-.6.4-.3.8L17.4 4l-4.2 4.1c-.2.3-.2.6 0 .9l1.8 1.7c.2.2.6.2.8 0L20 6.5l2.3 2.3c.4.3.8.2.8-.3V1.4c0-.2-.3-.5-.6-.5zM1.6 23.1h7.1c.5 0 .6-.5.3-.9l-2.3-2.3 4.1-4.2c.3-.2.3-.7 0-.9l-1.7-1.7c-.2-.2-.6-.2-.8 0l-4.2 4.2L1.8 15c-.4-.4-.9-.2-.9.2v7.1c0 .4.4.8.7.8zm21.5-.6v-7.1c0-.5-.4-.6-.8-.3L20 17.4l-4.1-4.2c-.3-.2-.6-.2-.9 0L13.3 15c-.2.2-.2.6 0 .8l4.2 4.2-2.3 2.3c-.3.4-.2.8.3.8h7.1c.2 0 .5-.3.5-.6zM.9 1.6v7.1c0 .5.5.6.9.3l2.3-2.3 4.2 4.1c.2.3.7.3.9 0l1.7-1.7c.2-.2.2-.6 0-.8L6.7 4.1 9 1.8c.4-.4.2-.9-.2-.9H1.7c-.4 0-.8.4-.8.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="expand_alt"><path  d="M22.5.9h-7.1c-.5 0-.6.4-.3.8L17.4 4l-4.2 4.1c-.2.3-.2.6 0 .9l1.8 1.7c.2.2.6.2.8 0L20 6.5l2.3 2.3c.4.3.8.2.8-.3V1.4c0-.2-.3-.5-.6-.5zM1.6 23.1h7.1c.5 0 .6-.5.3-.9l-2.3-2.3 4.1-4.2c.3-.2.3-.7 0-.9l-1.7-1.7c-.2-.2-.6-.2-.8 0l-4.2 4.2L1.8 15c-.4-.4-.9-.2-.9.2v7.1c0 .4.4.8.7.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="fallback"><path  d="M12.9 1.6l-1.4 6.8c0 .2.2.4.5.4h7.2c.5 0 .8.6.6 1l-7.9 12.9c-.3.7-1.3.4-1.3-.3l1.4-8c0-.2-.2-.1-.5-.1H3.9c-.5 0-.9-.8-.6-1.2l8.3-11.8c.4-.6 1.3-.4 1.3.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="favorite"><path  d="M12.6 1.4l2.2 7c.1.2.3.4.6.4h6.9c.7 0 1 .9.5 1.3l-5.7 4.2c-.2.1-.3.5-.2.7l2.7 7.2c.2.6-.5 1.2-1.1.7l-6-4.5c-.3-.2-.6-.2-.9 0l-6.1 4.5c-.5.5-1.3-.1-1-.7L7.1 15c.1-.2 0-.6-.3-.7l-5.6-4.2c-.6-.4-.2-1.3.4-1.3h6.9c.4 0 .6-.1.7-.4l2.2-7c.1-.7 1.1-.6 1.2 0z"/></symbol>
		<symbol viewBox="0 0 24 24" id="feed"><path  d="M11.6 19.8c-.1 0-.3 0-.5-.1-.3-.1-.5-.5-.5-.7L7.9 8.3l-2.2 5.1c-.2.4-.5.6-.9.6H1.6c-.4 0-.7-.2-.7-.6v-.7c0-.4.3-.7.7-.7H4l3.2-7.2c.2-.4.6-.7 1.1-.6.4 0 .8.3.9.7l2.7 10.8 3.7-7.9c.1-.4.6-.6 1-.6.3.1.7.4.9.7l1.8 4.1h3.1c.4 0 .7.4.7.7v.7c0 .4-.3.7-.7.7h-3.8c-.4 0-.7-.3-.9-.6l-1.2-2.7-3.9 8.5c-.2.3-.5.5-1 .5z"/></symbol>
		<symbol viewBox="0 0 24 24" id="file"><path d="M4.5 16.7V5.2c-1.2 0-2.2 1-2.2 2.1v13.6c0 1.2 1 2.2 2.2 2.2h10.7c1.2 0 2.2-1 2.2-2.1H8.8c-2.4 0-4.3 0-4.3-4.3zM21 7.3h-3.6c-1.2 0-2.2-.9-2.2-2.1V1.6c0-.4-.3-.7-.7-.7H8.8C7.6.9 6.6 1.9 6.6 3v13.6c0 1.2 1 2.1 2.2 2.1h10.7c1.2 0 2.2-.9 2.2-2.1V8.1c0-.4-.3-.8-.7-.8zm.6-3l-3.4-3.2c-.1-.1-.2-.2-.3-.2-.3 0-.5.3-.5.5v2.4c0 .8.6 1.4 1.4 1.4h2.4c.3 0 .5-.2.5-.5 0-.1-.1-.2-.1-.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="filter"><path d="M11.3 14.7c-.3-.3-.7-.3-1 0l-1.7 1.6c-.2.3-.8.1-.8-.3V9.9c0-.3-.3-.7-.6-.7H5.8c-.4 0-.7.4-.7.7V16c0 .4-.5.6-.8.3l-1.7-1.6c-.2-.3-.7-.3-.9 0l-1.1 1c-.2.3-.2.7 0 1L6 22c.2.2.6.2.9 0l5.4-5.4c.3-.3.3-.7 0-1l-1-.9zM23.5 4.4c0-.4-.3-.7-.7-.7h-17c-.4 0-.7.3-.7.7v1.4c0 .*******.7h17c.4 0 .7-.4.7-.7V4.4zm0 5.5c0-.3-.3-.7-.7-.7H10.4c-.4 0-.7.4-.7.7v1.4c0 .*******.7h12.4c.4 0 .7-.3.7-.7V9.9zm0 5.6c0-.4-.3-.7-.7-.7H15c-.4 0-.7.3-.7.7v1.3c0 .*******.7h7.8c.4 0 .7-.3.7-.7v-1.3z" /></symbol>
		<symbol viewBox="0 0 24 24" id="filterList"><path  d="M22.3 1.8H1.8c-.7 0-1 .8-.6 1.3l9 10.5c.2.3.4.8.4 1.2v6.7c0 .*******.7h1.4c.4 0 .6-.4.6-.7v-6.7c0-.4.2-.9.5-1.2l9.1-10.5c.4-.5.1-1.3-.6-1.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="flow"><path d="M23 4.9c-.9-1.9-3.4-5.4-7.9-3.3-2.8 1.3-4.4 2-4.4 2L6.6 5.4c-1.1.5-3.6-.3-5-.8-.4-.1-.8.3-.6.7.9 1.9 3.4 5.4 7.9 3.3 2.8-1.3 8.5-3.7 8.5-3.7 1.1-.6 3.6.2 5 .7.4.1.8-.3.6-.7zm-9.7 5.9c-.5.3-2.5 1.2-2.5 1.2l-2.1.9c-1 .5-3.2-.2-4.5-.7-.3-.2-.6.3-.5.6.9 1.9 3 5.2 7 3.2 2.5-1.3 4.6-2.1 4.6-2.1 1-.6 3.2.2 4.5.7.3.1.6-.3.5-.7-.9-1.8-3-5.1-7-3.1zM11.8 19c-.4.2-1.1.6-1.1.6-.8.5-2.4-.1-3.4-.6-.2-.1-.5.3-.3.7.6 1.6 2.2 4.6 5.2 2.8l1.1-.7c.8-.4 2.4.2 3.4.6.2.2.5-.2.3-.6-.6-1.7-2.1-4.5-5.2-2.8zm.2-7.4" /></symbol>
		<symbol viewBox="0 0 24 24" id="forward"><path  d="M1.6 13.4h15.3c.4 0 .6.5.3.8l-4.4 4.4c-.3.3-.3.7 0 1l1 1c.3.3.7.3 1 0l8-8.1c.3-.3.3-.7 0-1l-8-8.1c-.3-.3-.7-.3-1 0l-1 1c-.2.3-.2.7 0 1l4.5 4.4c.2.3.1.8-.4.8H1.6c-.4 0-.7.3-.7.7v1.3c0 .4.3.8.7.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="frozen"><path  d="M12.5 1.4c.2 0 .4.2.4.4V23c0 .3-.2.5-.4.5h-1c-.2 0-.4-.2-.4-.5V1.8c0-.2.2-.4.4-.4h1zM12 7.9L8.3 4.2c-.2-.2-.2-.5 0-.6l.6-.7c.2-.2.5-.2.7 0L12 5.4l2.4-2.5c.2-.2.5-.2.7 0l.6.7c.2.1.2.4 0 .6L12 7.9m0 9l3.7 3.8c.2.2.2.4 0 .6l-.6.7c-.2.2-.5.2-.7 0L12 19.5 9.6 22c-.2.2-.5.2-.7 0l-.6-.7c-.2-.2-.2-.4 0-.6l3.7-3.8"/><path  d="M21.7 7.2c.2.2.1.5-.1.6L3.3 18.6c-.2.1-.5.1-.6-.2l-.5-.8c-.1-.2-.1-.5.2-.6L20.6 6.2c.3-.2.5-.1.7.2l.4.8zm-5.8 3L17.2 5c0-.2.2-.4.5-.3l.9.2c.3.1.4.3.3.6L18 8.9l3.4.8c.3.1.4.3.3.6l-.2.9c0 .2-.3.4-.5.3l-5.1-1.3m-7.8 4.5l-1.3 5.1c0 .3-.2.5-.5.4l-.9-.3c-.3 0-.4-.2-.3-.5L6 16l-3.4-.9c-.3 0-.4-.2-.3-.5l.2-.9c0-.3.3-.4.5-.3l5.1 1.3"/><path  d="M2.7 6.4c.2-.2.4-.3.7-.2L21.6 17c.2.2.3.4.2.7l-.5.8c-.1.2-.4.3-.6.1L2.4 7.8c-.2-.1-.3-.4-.2-.6l.5-.8zm5.4 3.8L3 11.5c-.3 0-.5-.1-.6-.3l-.2-.9c0-.3.1-.5.3-.6l3.4-.8L5 5.5c0-.3.1-.5.4-.6l.8-.2c.3 0 .5.1.6.3l1.3 5.2m7.7 4.5l5.2-1.3c.2-.1.5 0 .5.3l.2.9c.1.3 0 .5-.3.5L18 16l.9 3.4c.1.3 0 .5-.3.5l-.9.3c-.3 0-.5-.1-.5-.4l-1.4-5.1"/></symbol>
		<symbol viewBox="0 0 24 24" id="full_width_view"><path d="M22.4 1.8H1.6c-.4 0-.7.4-.7.7v2.3c0 .*******.7h20.8c.4 0 .7-.3.7-.7V2.5c-.1-.3-.3-.7-.7-.7zM4.9 7.4H1.7c-.4 0-.7.3-.7.7v13.7c0 .*******.7h3.2c.4 0 .7-.3.7-.7V8.1c0-.4-.3-.7-.7-.7zm17.5 0h-3.2c-.4 0-.7.3-.7.7v13.7c0 .*******.7h3.2c.4 0 .7-.3.7-.7V8.1c0-.4-.3-.7-.7-.7zm-6.5 0H8.2c-.4 0-.7.3-.7.7v13.7c0 .*******.7h7.7c.4 0 .7-.3.7-.7V8.1c0-.4-.3-.7-.7-.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="groups"><path d="M7.3 12.9c-.6-.9-.9-2.1-.9-3.3 0-2.1.8-3.9 2.2-4.9-.4-.9-1.4-1.5-2.6-1.5-2 0-3.1 1.7-3.1 3.6 0 1 .3 1.9 1 2.5.3.3.7.8.7 1.3s-.2.9-1.4 1.4c-1.6.7-3.2 1.8-3.2 3.3 0 1 .7 1.8 1.7 1.8h1.5c.2 0 .4-.2.6-.4.7-1.3 2.1-2.2 3.3-2.8.4-.1.5-.7.2-1zm13.5-.9c-1.1-.5-1.3-.9-1.3-1.4s.3-1 .7-1.3c.7-.7 1-1.5 1-2.5 0-1.9-1.1-3.6-3.2-3.6-1.2 0-2.1.6-2.6 1.5 1.4 1 2.2 2.8 2.2 4.9 0 1.2-.3 2.4-.9 3.3-.3.4-.1.9.2 1 1.2.6 2.6 1.5 3.3 2.8.2.2.4.4.6.4h1.5c1 0 1.7-.8 1.7-1.8 0-1.5-1.5-2.6-3.2-3.3zm-5.7 3.4c-1.3-.6-1.5-1.1-1.5-1.6 0-.6.4-1.1.8-1.4.7-.7 1.2-1.7 1.2-2.8 0-2.1-1.3-3.9-3.6-3.9S8.5 7.5 8.5 9.6c0 1.1.5 2.1 1.2 2.8.4.4.8.9.8 1.4 0 .6-.2 1-1.5 1.6-1.8.8-3.6 1.6-3.6 3.3 0 1.1.8 2 1.8 2h9.6c1.1 0 1.9-.9 1.9-2 0-1.6-1.8-2.5-3.6-3.3z" /></symbol>
		<symbol viewBox="0 0 24 24" id="help"><path  d="M13.1 17.5h-2.3c-.4 0-.6-.2-.6-.6v-.7c0-1.9 1.2-3.7 3-4.3.6-.2 1.1-.5 1.5-1 2.3-2.8.2-6.1-2.6-6.2-1 0-1.9.3-2.7 1-.6.6-1 1.3-1 2.1-.1.2-.4.5-.7.5H5.4c-.5 0-.8-.4-.7-.8.1-1.7.9-3.3 2.2-4.5C8.4 1.6 10.2.8 12.3.9c3.8.1 6.9 3.3 7.1 7.1.1 3.2-1.9 6.1-4.9 7.2-.4.2-.7.5-.7 1v.6c0 .5-.3.7-.7.7zm.7 4.9c0 .4-.3.7-.6.7h-2.4c-.3 0-.6-.3-.6-.7v-2.3c0-.4.3-.7.6-.7h2.4c.3 0 .6.3.6.7v2.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="home"><path  d="M22.6 12.5h-2.3v10.1c0 .3-.2.5-.5.5h-4.6c-.2 0-.4-.2-.4-.5v-7.8H9.2v7.8c0 .3-.2.5-.4.5H4.2c-.3 0-.5-.2-.5-.5V12.5H1.4c-.2 0-.4-.1-.4-.3-.1-.2-.1-.4.1-.5L11.7 1.1c.2-.2.5-.2.6 0l10.6 10.6c.2.1.2.3.1.5s-.2.3-.4.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="identity"><path d="M21.2 3.7h-5.1s.1.3.1.5c0 1.8-1.5 3.2-3.3 3.2h-2.7C8.4 7.4 6.9 6 6.9 4.2c0-.2 0-.5.1-.5H2.8c-1 0-1.9.8-1.9 1.8v13.9c0 1 .9 1.8 1.9 1.8h18.4c1 0 1.9-.8 1.9-1.8V5.5c0-1-.9-1.8-1.9-1.8zM10 17.5H4.8c-.6 0-1.1-.5-1.1-1.1 0-.9 1-1.4 2-1.9.7-.2.8-.5.8-.8 0-.3-.2-.6-.5-.8-.4-.4-.6-.9-.6-1.5 0-1.2.7-2.2 1.9-2.2s2 1 2 2.2c0 .6-.3 1.1-.7 1.5-.2.2-.4.5-.4.8 0 .2.1.5.8.8 1 .5 2 1 2 1.9.1.6-.4 1.1-1 1.1zm10.3-1.8c0 .3-.2.5-.5.5h-6.4c-.3 0-.5-.2-.5-.5v-.9c0-.3.2-.5.5-.5h6.4c.3 0 .5.2.5.5v.9zm.9-3.7c0 .3-.2.5-.4.5h-7.4c-.3 0-.5-.2-.5-.5v-.9c0-.3.2-.5.5-.5h7.4c.2 0 .4.2.4.5v.9zm-11-6.5h2.7c.8 0 1.4-.6 1.4-1.3s-.6-1.4-1.4-1.4h-2.7c-.8 0-1.4.6-1.4 1.4s.6 1.3 1.4 1.3z" /></symbol>
		<symbol viewBox="0 0 24 24" id="image"><path  d="M23.1 4.6c0-1-.9-1.8-1.9-1.8H2.8c-1 0-1.9.8-1.9 1.8v14.8c0 1 .9 1.8 1.9 1.8h18.4c1 0 1.9-.8 1.9-1.8V4.6zm-4.8 12.9H4.9c-.6 0-.9-.6-.6-1l4.1-7.1c.1-.3.6-.3.7 0l2.5 4.2c.2.3.6.3.8.1l2-2.9c.1-.3.6-.3.7 0l3.7 5.8c.3.4 0 .9-.5.9zm-1.2-8.3c-1 0-1.9-.8-1.9-1.8s.9-1.9 1.9-1.9 1.8.9 1.8 1.9-.8 1.8-1.8 1.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="inbox"><path  d="M23.1 3.7c0-1-.9-1.9-1.9-1.9H2.8c-1 0-1.9.9-1.9 1.9v16.6c0 1 .9 1.9 1.9 1.9h18.4c1 0 1.9-.9 1.9-1.9V3.7zM8.8 16.2c0 .2-.2.4-.5.4H4.2c-.3 0-.5-.2-.5-.4v-1.9c0-.3.2-.5.5-.5h4.1c.3 0 .5.2.5.5v1.9zm0-4.7c0 .3-.2.5-.5.5H4.2c-.3 0-.5-.2-.5-.5V9.7c0-.3.2-.5.5-.5h4.1c.3 0 .5.2.5.5v1.8zm0-4.6c0 .3-.2.5-.5.5H4.2c-.3 0-.5-.2-.5-.5V5.1c0-.3.2-.5.5-.5h4.1c.3 0 .5.2.5.5v1.8zm11.5 12c0 .3-.2.5-.5.5h-8.7c-.3 0-.5-.2-.5-.5V5.1c0-.3.2-.5.5-.5h8.7c.3 0 .5.2.5.5v13.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="info"><path  d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zm0 5.6c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4-1.4-.6-1.4-1.4.6-1.4 1.4-1.4zm2.3 9.7c0 .2-.2.4-.5.4h-3.6c-.3 0-.5-.1-.5-.4v-.9c0-.3.2-.5.5-.5.2 0 .4-.2.4-.4v-1.9c0-.2-.2-.5-.4-.5-.3 0-.5-.1-.5-.4v-.9c0-.3.2-.5.5-.5h2.7c.3 0 .5.2.5.5v3.7c0 .2.2.4.4.4.3 0 .5.2.5.5v.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="info_alt"><path  d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zm0 19.4c-4.6 0-8.3-3.7-8.3-8.3S7.4 3.7 12 3.7s8.3 3.7 8.3 8.3-3.7 8.3-8.3 8.3zm0-13.8c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4-1.4-.6-1.4-1.4.6-1.4 1.4-1.4zm2.3 9.7c0 .2-.2.4-.5.4h-3.6c-.3 0-.5-.1-.5-.4v-.9c0-.3.2-.5.5-.5.2 0 .4-.2.4-.4v-1.9c0-.2-.2-.5-.4-.5-.3 0-.5-.1-.5-.4v-.9c0-.3.2-.5.5-.5h2.7c.3 0 .5.2.5.5v3.7c0 .2.2.4.4.4.3 0 .5.2.5.5v.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="insert_tag_field"><path  d="M7.5 5.6l-1-.8c-.4-.3-.7-.2-1 0L.1 11.6c-.1.2-.1.6 0 .9l5.4 6.7c.3.2.7.3 1 0l1.1-.8c.3-.3.3-.7.1-1L3.3 12l4.4-5.4c.2-.3.1-.7-.2-1zm16.4 6l-5.4-6.7c-.3-.3-.7-.4-1-.1l-1.1.9c-.3.2-.3.7-.1.9l4.4 5.4-4.4 5.4c-.2.3-.1.8.1 1l1.1.9c.3.2.7.2 1-.1l5.4-6.7c.1-.4.1-.7 0-.9zM14.6 5l-1.4-.3c-.4-.1-.8.1-.9.5L8.9 18.3c-.1.3.1.7.5.8l1.4.3c.4.1.8-.1.9-.5l3.4-13.1c.1-.4-.1-.7-.5-.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="insert_template"><path  d="M22.4 17.5h-2.1v-2c0-.4-.3-.7-.7-.7h-1.4c-.3 0-.7.3-.7.7v2h-2c-.4 0-.7.4-.7.7v1.4c0 .*******.7h2v2.1c0 .*******.7h1.4c.4 0 .7-.3.7-.7v-2.1h2.1c.4 0 .7-.3.7-.7v-1.4c0-.3-.3-.7-.7-.7zm-6.7-3.9c0-.4.3-.7.7-.7h1.1V2.8c0-1-.8-1.9-1.8-1.9H2.8C1.8.9.9 1.8.9 2.8v12.9c0 1 .9 1.8 1.9 1.8h10.1v-1.1c0-.4.3-.7.7-.7h2.1v-2.1zM7.4 5.1c0 .3-.2.4-.5.4H4.2c-.3 0-.5-.1-.5-.4v-.9c0-.3.2-.5.5-.5h2.7c.3 0 .5.2.5.5v.9zm5.5 7.4c0 .2-.2.4-.4.4H4.2c-.3 0-.5-.2-.5-.4v-1c0-.2.2-.4.5-.4h8.3c.2 0 .4.2.4.4v1zm1.9-3.7c0 .2-.2.4-.5.4H4.2c-.3 0-.5-.2-.5-.4v-1c0-.2.2-.4.5-.4h10.1c.3 0 .5.2.5.4v1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="italic"><path  d="M17.5 5.7v-.6c0-.5-.4-.9-.9-.9h-6.4c-.6 0-1 .4-1 .9V6c0 .5.4.9 1 .9.7 0 1.3.8 1.2 1.5l-1.7 8.1c-.1.6-.7 1-1.2 1H7.4c-.5 0-.9.5-.9 1v.9c0 .*******.9h6.4c.6 0 1-.4 1-.9v-.9c0-.5-.4-1-1-1-.7 0-1.3-.7-1.2-1.4l1.7-8.2c.1-.6.7-1 1.2-1h.8c.7 0 1.2-.5 1.2-1.2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="jump_to_bottom"><path d="M18.7 14l-6.2 6.3c-.3.3-.7.3-1 0L5.3 14c-.3-.2-.3-.7 0-1l1-1c.3-.3.7-.3 1 0l4.2 4.3c.3.3.7.3 1 0l4.2-4.3c.3-.2.7-.2 1 0l1 1.1c.3.2.3.7 0 .9zm0-8.3L12.5 12c-.3.3-.7.3-1 0L5.3 5.7c-.3-.3-.3-.8 0-1l1-1.1c.3-.2.7-.2 1 0L11.5 8c.3.3.7.3 1 0l4.2-4.3c.3-.3.7-.3 1 0l1 1c.3.3.3.7 0 1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="jump_to_top"><path d="M5.3 10l6.2-6.4c.3-.2.7-.2 1 0l6.2 6.4c.3.2.3.7 0 1l-1 1c-.3.3-.7.3-1 0l-4.2-4.3c-.3-.3-.7-.3-1 0L7.3 12c-.3.2-.7.2-1 0l-1-1.1c-.3-.2-.3-.7 0-.9zm0 8.3l6.2-6.3c.3-.3.7-.3 1 0l6.2 6.3c.3.3.3.8 0 1l-1 1.1c-.3.2-.7.2-1 0L12.5 16c-.3-.3-.7-.3-1 0l-4.2 4.3c-.3.3-.7.3-1 0l-1-1c-.3-.3-.3-.7 0-1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="justify_text"><path  d="M22.2 3c0-.4-.4-.7-.7-.7h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.3.7-.7V3zm0 5.5c0-.3-.4-.7-.7-.7h-19c-.3 0-.7.4-.7.7v1.4c0 .*******.7h19c.3 0 .7-.3.7-.7V8.5zm0 11.1c0-.4-.4-.7-.7-.7h-19c-.3 0-.7.3-.7.7V21c0 .*******.7h19c.3 0 .7-.3.7-.7v-1.4zm0-5.5c0-.4-.4-.7-.7-.7h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.4.7-.7v-1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="kanban"><path  d="M14.8 8.1c0-.4-.4-.7-.7-.7H9.9c-.3 0-.7.3-.7.7v12.4c0 .*******.7h4.2c.3 0 .7-.3.7-.7V8.1zm-8.3 0c0-.4-.4-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v14.3c0 .*******.7h4.2c.3 0 .7-.3.7-.7V8.1zm16.6 0c0-.4-.3-.7-.7-.7h-4.2c-.3 0-.7.3-.7.7v10.6c0 .*******.7h4.2c.4 0 .7-.3.7-.7V8.1zm0-6.5c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v2.3c0 .*******.7h20.8c.4 0 .7-.3.7-.7V1.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="keyboard_dismiss"><path  d="M22.2 15.7c.5 0 .9-.4.9-.9v-12c0-.5-.4-.9-.9-.9H1.8c-.5 0-.9.4-.9.9v12c0 .*******.9h20.4zM2.8 13.9V3.7h18.4v10.2H2.8zm3.5-7.7c0 .4-.3.7-.7.7h-.4c-.4 0-.7-.3-.7-.7v-.5c0-.3.3-.7.7-.7h.4c.4 0 .7.4.7.7v.5zM9 5.7c0-.3-.4-.7-.7-.7h-.5c-.4 0-.7.4-.7.7v.5c0 .*******.7h.5c.3 0 .7-.3.7-.7v-.5zm5.3.5c0 .4-.4.7-.7.7h-.5c-.4 0-.7-.3-.7-.7v-.5c0-.3.3-.7.7-.7h.5c.3 0 .7.4.7.7v.5zm-2.7 0c0 .4-.3.7-.7.7h-.4c-.4 0-.7-.3-.7-.7v-.5c0-.3.3-.7.7-.7h.4c.4 0 .7.4.7.7v.5zm7.9-.5c0-.3-.3-.7-.6-.7h-.5c-.4 0-.7.4-.7.7v.5c0 .*******.7h.5c.3 0 .6-.3.6-.7v-.5zm-2.6 0c0-.3-.3-.7-.7-.7h-.5c-.3 0-.6.4-.6.7v.5c0 .4.3.7.6.7h.5c.4 0 .7-.3.7-.7v-.5zM6.3 9c0 .3-.3.6-.7.6h-.4c-.4 0-.7-.3-.7-.6v-.5c0-.4.3-.7.7-.7h.4c.4 0 .7.3.7.7V9zM9 8.5c0-.4-.4-.7-.7-.7h-.5c-.4 0-.7.3-.7.7V9c0 .3.3.6.7.6h.5c.3 0 .7-.3.7-.6v-.5zm5.3.5c0 .3-.4.6-.7.6h-.5c-.4 0-.7-.3-.7-.6v-.5c0-.4.3-.7.7-.7h.5c.3 0 .7.3.7.7V9zm-2.7 0c0 .3-.3.6-.7.6h-.4c-.4 0-.7-.3-.7-.6v-.5c0-.4.3-.7.7-.7h.4c.4 0 .7.3.7.7V9zm7.9-.5c0-.4-.3-.7-.6-.7h-.5c-.4 0-.7.3-.7.7V9c0 .3.3.6.7.6h.5c.3 0 .6-.3.6-.6v-.5zm-2.6 0c0-.4-.3-.7-.7-.7h-.5c-.3 0-.6.3-.6.7V9c0 .3.3.6.6.6h.5c.4 0 .7-.3.7-.6v-.5zM6.3 11.7c0 .4-.3.7-.7.7h-.4c-.4 0-.7-.3-.7-.7v-.4c0-.4.3-.7.7-.7h.4c.4 0 .7.3.7.7v.4zm10.6-.4c0-.4-.3-.7-.7-.7H7.8c-.4 0-.7.3-.7.7v.4c0 .*******.7h8.4c.4 0 .7-.3.7-.7v-.4zm2.6 0c0-.4-.3-.7-.6-.7h-.5c-.4 0-.7.3-.7.7v.4c0 .*******.7h.5c.3 0 .6-.3.6-.7v-.4zM8.4 17.2h7.2c.3 0 .4.2.2.4L12.3 22c-.2.2-.4.2-.5 0l-3.6-4.4c-.2-.2-.1-.4.2-.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="knowledge_base"><path d="M4.4 16.2h6c.4 0 .7-.4.7-.7V4.6c0-.8-.9-1.4-1.5-1.4H4.4c-.4 0-.7.4-.7.7v11.6c0 .*******.7zM22.7 5.4c-.3-.1-.5.1-.5.4v11.5c0 .4-.4.7-.7.7h-19c-.3 0-.7-.3-.7-.7V5.9c0-.4-.3-.6-.6-.5-.7.4-1.2 1.1-1.2 2V18c0 1 .8 1.8 1.8 1.8h7.7c.3 0 .7.4.7.7s.3.7.6.7h2.4c.3 0 .6-.3.6-.7s.4-.7.7-.7h7.7c1 0 1.8-.8 1.8-1.8V7.4c0-1-.3-1.8-1.3-2zm-9.1 10.8h6c.4 0 .7-.4.7-.7V3.9c0-.3-.3-.7-.7-.7h-5.2c-.7 0-1.5.6-1.5 1.4v10.9c0 .*******.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="layers"><path  d="M16.6 9.2c0-1-.8-1.8-1.8-1.8h-12c-1 0-1.9.8-1.9 1.8v12c0 1 .9 1.9 1.9 1.9h12c1 0 1.8-.9 1.8-1.9v-12zM19.8.9h-12C6 .9 4.6 2.4 4.6 4.2v1.3h12c1 0 1.9.9 1.9 1.9v12h1.3c1.8 0 3.3-1.4 3.3-3.2v-12c0-1.8-1.5-3.3-3.3-3.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="layout"><path  d="M22.2 23.1H1.8c-.5 0-.9-.4-.9-.9V1.8c0-.5.4-.9.9-.9h20.4c.5 0 .9.4.9.9v20.4c0 .5-.4.9-.9.9zM2.8 21.2h18.4V2.8H2.8v18.4zM18 9.2H6c-.3 0-.5-.2-.5-.4V6c0-.3.2-.5.5-.5h12c.3 0 .5.2.5.5v2.8c0 .2-.2.4-.5.4zm-9.2 9.3H6c-.3 0-.5-.2-.5-.5v-5.5c0-.3.2-.5.5-.5h2.8c.2 0 .4.2.4.5V18c0 .3-.2.5-.4.5zm9.2 0h-5.5c-.3 0-.5-.2-.5-.5v-5.5c0-.3.2-.5.5-.5H18c.3 0 .5.2.5.5V18c0 .3-.2.5-.5.5z"/></symbol>
		<symbol viewBox="0 0 24 24" id="left"><path  d="M17.5 3.8v16.4c0 .4-.6.8-1 .4l-9.8-8c-.3-.3-.3-.9 0-1.2l9.8-8c.4-.4 1-.1 1 .4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="left_align_text"><path  d="M22.2 3c0-.4-.4-.7-.7-.7h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.3.7-.7V3zm-3.7 5.5c0-.3-.4-.7-.7-.7H2.5c-.3 0-.7.4-.7.7v1.4c0 .*******.7h15.3c.3 0 .7-.3.7-.7V8.5zm0 11.1c0-.4-.4-.7-.7-.7H2.5c-.3 0-.7.3-.7.7V21c0 .*******.7h15.3c.3 0 .7-.3.7-.7v-1.4zm3.7-5.5c0-.4-.4-.7-.7-.7h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.4.7-.7v-1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="level_up"><path  d="M1.6 20.3c-.3 0-.7.3-.7.7v1.4c0 .*******.7h11c.4 0 .8-.4.8-.7V7.1c0-.5.4-.6.8-.4l2.6 2.6c.2.3.6.3.9 0l1-.9c.3-.3.3-.7 0-1l-6.2-6.3c-.3-.3-.7-.3-1 0L5.3 7.3c-.3.3-.3.7 0 1l.9 1c.3.3.7.3 1 0l2.6-2.6c.3-.3.8-.1.8.3v12.6c0 .8-.7.7-.7.7H1.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="like"><path d="M4.8 9.7H2.5c-.3 0-.7.3-.7.7V21c0 .*******.7h1.2c1 0 1.8-.8 1.8-1.9v-9.4c0-.4-.3-.7-.7-.7zm15.5.5h-2.8c-1 0-1.8-.9-1.8-1.9V3.7c0-1-.8-1.9-1.9-1.9h-1.1c-.4 0-.7.4-.7.7v2.8c0 2.5-1.7 4.9-3.9 4.9-.4 0-.7.3-.7.6v9.3c0 .3.3.7.6.7 3.2.1 4.2 1.4 7.5 1.4 3.5 0 6.7-.4 6.7-4.4V12c0-1-.9-1.8-1.9-1.8z" /></symbol>
		<symbol viewBox="0 0 24 24" id="link"><path d="M12.6 19.2l-1-.1s-.7-.1-1-.3c-.2 0-.4 0-.5.2l-.3.2c-1.3 1.3-3.5 1.5-4.9.3-1.5-1.4-1.6-3.8-.1-5.2l3.5-3.5c.4-.5 1-.7 1.5-.9.8-.2 1.6-.2 2.2.1.5.2.9.4 1.2.8.2.2.4.4.5.6.2.3.6.4.8.1l1.3-1.3c.2-.2.2-.5.1-.7-.2-.3-.4-.5-.7-.7-.3-.4-.7-.7-1.1-.9-.6-.4-1.4-.7-2.1-.8-1.5-.3-3-.1-4.3.6-.5.3-1.1.7-1.5 1.1l-3.3 3.3C.4 14.6.2 18.6 2.6 21c2.4 2.7 6.6 2.8 9.1.2l1.2-1.1c.3-.3.1-.8-.3-.9zM21 2.7C18.5.3 14.5.5 12.1 3l-1 1c-.3.3-.1.8.3.9.6 0 1.3.2 1.9.4.2 0 .5 0 .6-.2l.2-.2c1.4-1.3 3.5-1.5 4.9-.3 1.6 1.4 1.6 3.8.2 5.2l-3.5 3.5c-.5.5-1 .7-1.6.9-.7.2-1.5.2-2.2-.1-.4-.2-.8-.4-1.2-.8-.2-.2-.3-.4-.5-.6-.1-.3-.6-.4-.8-.1l-1.3 1.3c-.2.2-.2.5 0 .7.2.3.4.5.6.7.3.3.8.7 1.1.9.7.4 1.4.7 2.2.8 1.4.3 3 .1 4.2-.6.6-.3 1.1-.7 1.5-1.1l3.5-3.5c2.6-2.5 2.5-6.7-.2-9.1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="list"><path d="M3.7 4.8c0-.3-.3-.6-.7-.6H1.6c-.4 0-.7.3-.7.6v1.4c0 .*******.7H3c.4 0 .7-.3.7-.7V4.8zm19.4 0c0-.3-.3-.6-.7-.6H6.2c-.3 0-.7.3-.7.6v1.4c0 .*******.7h16.2c.4 0 .7-.3.7-.7V4.8zM3.7 11.3c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v1.4c0 .*******.7H3c.4 0 .7-.3.7-.7v-1.4zm17.5 0c0-.4-.3-.7-.7-.7H6.2c-.3 0-.7.3-.7.7v1.4c0 .*******.7h14.3c.4 0 .7-.3.7-.7v-1.4zM3.7 17.8c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v1.4c0 .3.3.6.7.6H3c.4 0 .7-.3.7-.6v-1.4zm19.4 0c0-.4-.3-.7-.7-.7H6.2c-.3 0-.7.3-.7.7v1.4c0 .3.4.6.7.6h16.2c.4 0 .7-.3.7-.6v-1.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="location"><path  d="M22.5 4.4l-6.6-3.3c-.3-.2-.7-.2-1 0L8.8 4.2 2.6 1.1c-.4-.2-.8-.2-1.2 0-.3.2-.5.6-.5.9v16.6c0 .5.3.8.6 1l6.7 3.3c.3.2.7.2.9 0l6.2-3.1 6.2 3.1c.1.1.3.2.5.2s.4-.1.6-.2c.3-.2.5-.6.5-.9V5.4c0-.5-.2-.8-.6-1zm-1.7 2.1v8.8c0 .5-.5.9-1 .7-1.7-.7-.3-3.5-1.5-5.1-1.2-1.4-2.7 0-4.1-2.2-1.3-2.2.5-3.8 2.1-4.6.3-.1.5-.1.7 0l3.4 1.7c.*******.4.7zm-9.3 12.8c-.3.2-.6.1-.8-.1-.5-.4-.9-1-.9-1.7 0-1.1-1.8-.7-1.8-2.9 0-1.8-2.1-2.3-3.9-2.1-.5.1-.8-.3-.8-.7V5c0-.5.5-.9 1-.6l4 2h.1l.1.1c1.7 1 1.3 1.8.6 3-.7 1.3-1.1 0-2.2-.4s-2.2.4-1.8 1.1 1.5 0 2.2.7.7 1.9 2.9 1.1 2.6-.3 3.4.4c.7.8 1.1 2.2 0 3.3-.7.7-1 2.1-1.2 3-.1.2-.2.4-.4.5l-.5.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="lock"><path d="M5.1 8.8h1.8c.3 0 .5-.2.5-.4v-.1c0-2.6 2.2-4.8 4.9-4.6 2.5.2 4.3 2.3 4.3 4.8v-.1c0 .2.2.4.5.4h1.8c.3 0 .5-.2.5-.4v-.1c0-4.2-3.5-7.6-7.8-7.4-3.9.2-6.9 3.5-7 7.5.*******.5.4zm-.5-.4v.1-.1zm16.6 4.1c0-1.1-.8-1.9-1.8-1.9H4.6c-1 0-1.8.8-1.8 1.9v8.7c0 1 .8 1.9 1.8 1.9h14.8c1 0 1.8-.9 1.8-1.9v-8.7zm-7.1 7.2c.1.3-.1.6-.4.6h-3.4c-.3 0-.5-.3-.5-.6l.9-2.8c-.7-.4-1.1-1.3-1-2.2.2-.9.9-1.5 1.8-1.7 1.5-.3 2.8.8 2.8 2.1 0 .8-.4 1.5-1 1.8l.8 2.8z" /></symbol>
		<symbol viewBox="0 0 24 24" id="log_a_call"><path  d="M19.8.9H6.2C5 .9 4 2 4 3.1v.8h-.7c-.8 0-1.5.6-1.5 1.5s.7 1.4 1.5 1.4H4v3.7h-.7c-.8 0-1.5.7-1.5 1.5s.7 1.5 1.5 1.5H4v3.7h-.7c-.8 0-1.5.6-1.5 1.4 0 .9.7 1.5 1.5 1.5H4v.8c0 1.1 1 2.2 2.2 2.2h13.6c1.2 0 2.4-1.1 2.4-2.3V3c0-1.2-1.2-2.1-2.4-2.1zm-1.2 14.9l-1.1 1c-.2.3-.5.4-.8.4-2.4-.2-4.6-1.3-6.2-2.9s-2.7-3.9-2.8-6.4c0-.3.1-.6.3-.8l1-1c.5-.5 1.2-.5 1.7.1l.9 1.2c.3.4.3 1 0 1.4l-.8 1.1c0 .2 0 .4.1.5l1.7 1.9 1.8 1.7c.1.1.3.1.5 0l1.1-.8c.4-.3.9-.3 1.4 0l1.1 1c.5.3.5 1.1.1 1.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="logout"><path d="M9.7 22.4V21c0-.4-.3-.7-.7-.7H4.4c-.4 0-.7-.3-.7-.7V4.4c0-.4.3-.7.7-.7H9c.4 0 .7-.3.7-.7V1.6c0-.4-.3-.7-.7-.7H2.8C1.8.9.9 1.8.9 2.8v18.4c0 1 .9 1.9 1.9 1.9H9c.4 0 .7-.3.7-.7zm13.2-9.9c.3-.3.3-.7 0-1l-6.2-6.2c-.3-.3-.7-.3-1 0l-1 .9c-.3.3-.3.7 0 1l2.6 2.6c.3.3.1.8-.3.8H7.2c-.4 0-.7.2-.7.6v1.4c0 .*******.7h9.7c.5 0 .6.5.4.8l-2.6 2.6c-.3.3-.3.7 0 1l.9.9c.3.3.7.3 1 0l6.3-6.1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="magicwand"><path d="M13 9.7c-.2-.2-.4-.2-.6 0l-11.1 11c-.5.6-.5 1.4 0 2 .6.5 1.4.5 2 0l11-11.1c.2-.2.2-.4 0-.6L13 9.7zm3.2 0l1.5-1.5c.3-.3.3-.7 0-1l-.9-.9c-.3-.3-.7-.3-1 0l-1.5 1.5c-.2.1-.2.4 0 .6l1.3 1.3c.2.2.5.2.6 0zM4.8 5.4c1.8.5 3.1 1.8 3.7 3.6.1.3.5.3.5 0 .6-1.7 1.9-3.1 3.7-3.6.3-.1.3-.5 0-.6C11 4.2 9.6 2.9 9 1.1c0-.3-.4-.3-.5 0-.6 1.8-1.9 3.1-3.7 3.7-.2.1-.2.5 0 .6zm18.1 8.7c-1.6-.5-2.8-1.7-3.3-3.3-.1-.2-.4-.2-.5 0-.5 1.6-1.7 2.8-3.3 3.3-.2.1-.2.4 0 .5 1.6.5 2.8 1.7 3.3 3.3.1.2.4.2.5 0 .5-1.6 1.7-2.8 3.3-3.3.2-.1.2-.5 0-.5zM17.7 3.9c1.2.3 2.1 1.2 2.4 2.4.1.2.3.2.4 0 .4-1.2 1.2-2.1 2.4-2.4.2-.1.2-.3 0-.4-1.2-.4-2-1.2-2.4-2.4-.1-.2-.3-.2-.4 0-.3 1.2-1.2 2-2.4 2.4-.2.1-.2.3 0 .4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="mark_all_as_read"><path  d="M11.1 3.2l-.8-.8c-.2-.2-.6-.2-.8 0L4.6 7.3l-2-1.9c-.2-.3-.5-.3-.8 0l-.7.7c-.3.3-.3.6 0 .8l2.7 2.7c.2.3.5.4.8.4.2 0 .5-.1.8-.4L11.1 4c.2-.2.2-.5 0-.8zm11.2 5.3h-9.6c-.4 0-.7-.3-.7-.7V6.3c0-.4.3-.8.7-.8h9.6c.5 0 .8.4.8.8v1.5c0 .4-.3.7-.8.7zm0 6.6H4.5c-.4 0-.7-.3-.7-.7v-1.5c0-.4.3-.8.7-.8h17.8c.5 0 .8.4.8.8v1.5c0 .4-.3.7-.8.7zm0 6.6H4.5c-.4 0-.7-.3-.7-.7v-1.5c0-.4.3-.8.7-.8h17.8c.5 0 .8.4.8.8V21c0 .4-.3.7-.8.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="matrix"><path  d="M22.2 1.6c0-.4-.4-.7-.7-.7H7.2c-.4 0-.7.3-.7.7v2.3c0 .*******.7h14.3c.3 0 .7-.3.7-.7V1.6zM4.6 7.2c0-.4-.3-.7-.7-.7H2.5c-.3 0-.7.3-.7.7v6c0 .3.4.6.7.6h1.4c.4 0 .7-.3.7-.6v-6zm0 9.2c0-.4-.3-.7-.7-.7H2.5c-.3 0-.7.3-.7.7v6c0 .*******.7h1.4c.4 0 .7-.3.7-.7v-6zm8.8-9.2c0-.4-.3-.7-.7-.7H7.2c-.4 0-.7.3-.7.7v1.3c0 .*******.7h5.5c.4 0 .7-.3.7-.7V7.2zm8.8 0c0-.4-.4-.7-.7-.7h-5.6c-.3 0-.7.3-.7.7v1.3c0 .*******.7h5.6c.3 0 .7-.3.7-.7V7.2zm-8.8 4.6c0-.4-.3-.7-.7-.7H7.2c-.4 0-.7.3-.7.7v1.4c0 .3.3.6.7.6h5.5c.4 0 .7-.3.7-.6v-1.4zm8.8 0c0-.4-.4-.7-.7-.7h-5.6c-.3 0-.7.3-.7.7v1.4c0 .3.4.6.7.6h5.6c.3 0 .7-.3.7-.6v-1.4zm-8.8 4.6c0-.4-.3-.7-.7-.7H7.2c-.4 0-.7.3-.7.7v1.4c0 .*******.7h5.5c.4 0 .7-.4.7-.7v-1.4zm8.8 0c0-.4-.4-.7-.7-.7h-5.6c-.3 0-.7.3-.7.7v1.4c0 .*******.7h5.6c.3 0 .7-.4.7-.7v-1.4zM13.4 21c0-.4-.3-.7-.7-.7H7.2c-.4 0-.7.3-.7.7v1.4c0 .*******.7h5.5c.4 0 .7-.3.7-.7V21zm8.8 0c0-.4-.4-.7-.7-.7h-5.6c-.3 0-.7.3-.7.7v1.4c0 .*******.7h5.6c.3 0 .7-.3.7-.7V21z"/></symbol>
		<symbol viewBox="0 0 24 24" id="merge"><path  d="M19.5 20.3c-2.6-1.2-4.4-3.5-5.3-6-.4-.9-.6-2-.7-2.9V9.8h5c.3 0 .6-.4.3-.8l-6.5-7.9c-.3-.3-.8-.3-.9 0L5 9c-.2.2 0 .8.4.8h5v1.6c-.2 1-.4 2-.7 2.9-.9 2.5-2.8 4.8-5.4 6-.3.1-.5.6-.3.9l.6 1.5c.2.3.6.5.9.2 2.8-1.3 5-3.4 6.4-6 1.3 2.6 3.5 4.7 6.3 6 .4.2.9.2 1-.2l.6-1.5c.3-.3.1-.7-.3-.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="metrics"><path  d="M20.7.9H3.3C2 .9.9 2 .9 3.3v17.4c0 1.3 1.1 2.4 2.4 2.4h17.4c1.3 0 2.3-1.1 2.3-2.4V3.3C23.1 2 22 .9 20.7.9zM7.2 18.3c0 .4-.3.8-.7.8h-.8c-.4 0-.8-.4-.8-.8V14c0-.4.4-.8.8-.8h.8c.4 0 .7.4.7.8v4.3zm4 0c0 .4-.4.8-.8.8h-.8c-.4 0-.7-.4-.7-.8V8c0-.4.3-.8.7-.8h.8c.4 0 .8.4.8.8v10.3zm4 0c0 .4-.4.8-.8.8h-.8c-.4 0-.8-.4-.8-.8V5.7c0-.4.4-.8.8-.8h.8c.4 0 .8.4.8.8v12.6zm3.9 0c0 .4-.4.8-.8.8h-.8c-.4 0-.7-.4-.7-.8v-7.5c0-.4.3-.8.7-.8h.8c.4 0 .8.4.8.8v7.5z"/></symbol>
		<symbol viewBox="0 0 24 24" id="minimize_window"><path  d="M23.1 22.4c0 .4-.3.7-.7.7H1.6c-.4 0-.7-.3-.7-.7V21c0-.4.3-.7.7-.7h20.8c.4 0 .7.3.7.7v1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="moneybag"><path  d="M9.5 4.2c.1.3.3.4.6.4h3.7c.3 0 .6-.1.7-.4L16 1.5c.1-.3-.1-.6-.4-.6H8.4c-.3 0-.5.3-.3.5l1.4 2.8zm4.7 2.6H9.8c-3.6 0-6.6 3-6.6 6.7v7.4c0 1.2 1 2.2 2.2 2.2h13.2c1.2 0 2.2-1 2.2-2.2v-7.4c0-3.7-3-6.7-6.6-6.7zm-1.1 12.4v1.3c0 .2-.2.4-.5.4h-1.4c-.3 0-.3-.2-.3-.4v-1.2c-1.1-.2-2-.7-2.3-.9-.2-.3-.3-.5-.1-.9l.5-.7c0-.2.3-.3.5-.3.1 0 .3.1.4.1.8.5 1.4.7 1.9.7s.9-.3.9-.6c0-.2-.1-.6-1.5-1.1-1.3-.4-2.8-1.2-2.8-2.9 0-1 .6-2.1 2.5-2.5V9.1c0-.2.1-.4.3-.4h1.4c.3 0 .5.2.5.4v1.1c.7.1 1.5.5 1.8.7.1.1.2.3.3.5 0 .1-.1.3-.2.4l-.6.7c-.1.1-.4.3-.6.3-.1 0-.2-.1-.3-.1-.8-.4-1.4-.7-1.8-.7-.6 0-.9.3-.9.5 0 .3.2.6 1.4 1 1.6.5 3.3 1.4 3.3 3.1 0 1.2-1 2.3-2.4 2.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="monthlyview"><path  d="M20.3 3.2H18v-.9c0-.7-.6-1.4-1.4-1.4-.7 0-1.4.6-1.4 1.4v.9H8.8v-.9c0-.7-.6-1.4-1.4-1.4C6.6.9 6 1.5 6 2.3v.9H3.7c-1 0-1.9.9-1.9 1.9v1.1c0 .*******.7h19c.3 0 .7-.3.7-.7V5.1c0-1-.9-1.9-1.9-1.9zm1.2 6h-19c-.3 0-.7.4-.7.7v11.3c0 1 .9 1.9 1.9 1.9h16.6c1 0 1.9-.9 1.9-1.9V9.9c0-.3-.4-.7-.7-.7zM9.7 20.3c-1.1 0-2.3-.4-2.7-1 0-.1-.1-.2-.1-.3 0-.4.4-.8.8-.8.1 0 .2.1.4.1.5.3 1.1.5 1.6.5.9 0 1.4-.4 1.4-1s-.4-.9-1.5-.9c-.6.1-1-.1-1-.7 0-.4.3-.7.7-.7 1 .1 1.7-.2 1.7-.8 0-.6-.6-.9-1.4-.9-.5 0-1 .1-1.5.4-.1.1-.2.1-.3.1-.4 0-.7-.3-.7-.7 0-.2.1-.4.2-.5.6-.5 1.4-.8 2.5-.8 1.7 0 2.8.8 2.8 2.1 0 .9-.8 1.5-1.6 1.7.8.1 1.7.7 1.7 1.8 0 1.5-1.2 2.4-3 2.4zm7.4-.9c0 .4-.3.9-.7.9-.4 0-.7-.4-.7-.9v-4.7l-1 .9c-.1.1-.3.1-.5.1-.4 0-.7-.2-.7-.7 0-.1.1-.3.2-.4l1.8-1.8c.1-.2.4-.3.7-.3.5 0 .9.5.9 1v5.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="move"><path  d="M22.9 11.7l-3.8-4.2c-.3-.3-.6 0-.6.4v2.7h-4.7c-.2 0-.4-.2-.4-.4V5.5h2.7c.5 0 .7-.4.4-.6l-4.1-3.8c-.2-.2-.5-.2-.7 0L7.6 4.9c-.3.3-.1.6.4.6h2.6v4.7c0 .2-.2.4-.4.4H5.5V7.9c0-.5-.4-.7-.6-.4l-3.8 4.1c-.2.2-.2.5 0 .7l3.8 4.1c.3.3.6.1.6-.4v-2.6h4.7c.2 0 .4.2.4.4v4.7H7.9c-.5 0-.7.4-.4.6l4.1 3.8c.2.2.5.2.7 0l4.1-3.8c.3-.3.1-.6-.4-.6h-2.6v-4.7c0-.2.2-.4.4-.4h4.7v2.7c0 .5.4.7.6.4l3.8-4.1c.2-.3.2-.5 0-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="muted"><path  d="M22.4 2.6l-1-1c-.3-.3-.8-.2-1.1.2l-4.6 4.6V4.6c0-2.1-1.6-3.7-3.7-3.7S8.3 2.5 8.3 4.6v6.7c0 .7.2 1.3.6 1.9l-1.7 1.6c-.7-1-1.2-2.2-1.2-3.5V9.4c0-.6-.5-1.1-1.2-1.1s-1.1.5-1.1 1.1v1.9c0 1.9.7 3.7 1.9 5.1l-3.8 3.9c-.4.3-.4.8-.2 1.1l1 1c.3.3.8.2 1.1-.2L22.2 3.7c.4-.3.5-.8.2-1.1zM18 10.7v.6c0 3.2-2.7 5.9-6 5.9h-.4l-1.8 1.9c.4.1.8.1 1.3.2v1.5H9c-.6 0-1.2.5-1.2 1.1s.6 1.2 1.2 1.2h6c.7 0 1.2-.5 1.2-1.2s-.6-1.1-1.2-1.1h-2.1v-1.5c4.2-.6 7.4-4 7.4-8V9.4c0-.3-.1-.5-.3-.7l-2 2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="new"><path  d="M19.8 4.2C15.5-.1 8.5-.1 4.2 4.2c-4.3 4.3-4.3 11.3 0 15.6 4.3 4.4 11.3 4.4 15.6 0 4.3-4.3 4.3-11.3 0-15.6zm-.4 8.7c0 .3-.2.5-.5.5h-5.1c-.2 0-.4.2-.4.4v5.1c0 .3-.2.5-.5.5h-1.8c-.3 0-.5-.2-.5-.5v-5.1c0-.2-.2-.4-.4-.4H5.1c-.3 0-.5-.2-.5-.5v-1.8c0-.3.2-.5.5-.5h5.1c.2 0 .4-.2.4-.4V5.1c0-.3.2-.5.5-.5h1.8c.3 0 .5.2.5.5v5.1c0 .2.2.4.4.4h5.1c.3 0 .5.2.5.5v1.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="new_window"><path d="M22.5.9h-8.8c-.4 0-.8.3-.8.6v1.4c0 .4.3.8.8.8h3.6c.4 0 .7.5.3.7l-7.8 7.9c-.3.3-.3.7 0 .9l1 1c.2.3.6.3.9 0l7.9-7.8c.2-.3.7-.1.7.3v3.6c0 .4.4.8.7.8h1.4c.4 0 .7-.4.7-.8V1.6c0-.4-.3-.7-.6-.7zm-5.7 10.9l-1.6 1.6c-.3.3-.4.6-.4 1v5.2c0 .4-.4.7-.7.7H4.4c-.4 0-.7-.3-.7-.7V9.9c0-.3.3-.7.7-.7h5.3c.4 0 .7-.1 1-.4l1.5-1.6c.3-.2.1-.7-.3-.7H2.8c-1 0-1.9.8-1.9 1.8v12.9c0 1 .9 1.9 1.9 1.9h12.9c1 0 1.8-.9 1.8-1.9v-9.1c0-.4-.5-.6-.7-.3z" /></symbol>
		<symbol viewBox="0 0 24 24" id="news"><path  d="M23.3 2.8H4.4c-.4 0-.7.3-.7.7v14c0 .6-.5 1.1-1.1 1-.4-.1-.8-.5-.8-1V7.4c0-.3-.1-.5-.4-.5H.7c-.4 0-.7.3-.7.7v11.8c0 1 .8 1.8 1.8 1.8h20.4c1 0 1.8-.8 1.8-1.8V3.5c0-.4-.3-.7-.7-.7zM12.9 16.2c0 .2-.2.4-.4.4H6.9c-.3 0-.4-.2-.4-.4v-1c0-.2.1-.4.4-.4h5.6c.2 0 .4.2.4.4v1zm0-3.7c0 .2-.2.4-.4.4H6.9c-.3 0-.4-.2-.4-.4v-1c0-.2.1-.4.4-.4h5.6c.2 0 .4.2.4.4v1zm8.3 3.7c0 .2-.2.4-.4.4h-5.6c-.2 0-.4-.2-.4-.4v-1c0-.2.2-.4.4-.4h5.6c.2 0 .4.2.4.4v1zm0-3.7c0 .2-.2.4-.4.4h-5.6c-.2 0-.4-.2-.4-.4v-1c0-.2.2-.4.4-.4h5.6c.2 0 .4.2.4.4v1zm0-3.7c0 .2-.2.4-.4.4H6.9c-.3 0-.4-.2-.4-.4V6c0-.3.1-.5.4-.5h13.9c.2 0 .4.2.4.5v2.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="note"><path  d="M19.1 18.3l-.4.4c-.5.5-1.1.7-1.7.7h-1.2c-1.1 0-2.3-.8-2.3-2.4v-1.1c0-.9.4-1.5.6-1.8l5-5.1c.1-.1.3-.5.3-.6V4.5c0-1.2-1-2.2-2.2-2.2H5.4c-1.2 0-2.3 1.1-2.3 2.2h-.7C1.6 4.5.9 5.2.9 6s.7 1.5 1.5 1.5h.7v3h-.7c-.8 0-1.5.7-1.5 1.5s.7 1.5 1.5 1.5h.7v3h-.7c-.8 0-1.5.7-1.5 1.5s.7 1.4 1.5 1.4h.7c0 1.5 1.1 2.2 2.3 2.2h11.8c1.2 0 2.2-1 2.2-2.2v-.9c0-.3-.1-.3-.3-.2zM15.3 7.9c0 .4-.3.7-.7.7H7.2c-.4 0-.7-.3-.7-.7v-.7c0-.5.3-.8.7-.8h7.4c.4 0 .7.3.7.8v.7zM12 16.8c0 .5-.3.8-.7.8H7.2c-.4 0-.7-.3-.7-.8v-.7c0-.4.3-.7.7-.7h4.1c.4 0 .7.3.7.7v.7zm1.1-4.4c0 .4-.3.7-.7.7H7.2c-.4 0-.7-.3-.7-.7v-.8c0-.4.3-.7.7-.7h5.2c.4 0 .7.3.7.7v.8zm9.7-2.1l-.4-.4c-.3-.3-.8-.3-1 0l-5.7 5.8V17c0 .1 0 .2.1.2h1.3l5.7-5.8c.4-.3.4-.7 0-1.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="notebook"><path  d="M20.3.9H6.5c-1.1 0-1.9.9-1.9 1.9v1.4H3.2c-.8 0-1.4.6-1.4 1.3s.6 1.4 1.4 1.4h1.4v3.7H3.2c-.8 0-1.4.6-1.4 1.4s.6 1.4 1.4 1.4h1.4v3.7H3.2c-.8 0-1.4.6-1.4 1.4s.6 1.3 1.4 1.3h1.4v1.4c0 1 .8 1.9 1.9 1.9h13.8c1 0 1.9-.9 1.9-1.9V2.8c0-1-.9-1.9-1.9-1.9zm-3.2 15.7c0 .3-.2.5-.5.5h-6.4c-.3 0-.5-.2-.5-.5v-.9c0-.3.2-.5.5-.5h6.4c.3 0 .5.2.5.5v.9zm.9-3.7c0 .3-.2.5-.5.5H9.2c-.2 0-.4-.2-.4-.5V12c0-.3.2-.5.4-.5h8.3c.3 0 .5.2.5.5v.9zm.9-4.6c0 .3-.2.5-.4.5H8.3c-.3 0-.5-.2-.5-.5V5.5c0-.2.2-.4.5-.4h10.2c.2 0 .4.2.4.4v2.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="notification"><path d="M21.2 15.2H21c-.9 0-1.6-.7-1.6-1.6V8.3c0-4.2-3.5-7.6-7.8-7.4-3.9.2-7 3.6-7 7.6v5.2c0 .8-.7 1.5-1.6 1.5h-.2c-1 0-1.9.9-1.9 1.9v.7c0 .*******.7h20.8c.4 0 .7-.4.7-.7v-.7c0-1-.9-1.9-1.9-1.9zm-6.9 5.1H9.7c-.2 0-.5.3-.4.6.2 1.3 1.4 2.2 2.7 2.2s2.5-1 2.7-2.2c.1-.3-.2-.6-.4-.6z" /></symbol>
		<symbol viewBox="0 0 24 24" id="office365"><g ><path d="M14.2 22.8c.3.1.6.1.9 0l5.5-1.8c.4-.1.6-.4.6-.8V3.6c0-.3-.2-.6-.4-.7L15.2 1c-.3-.1-.7-.1-.9 0L3.2 5.3c-.2.1-.4.3-.4.6v12.5c0 .3.2.6.4.7l11 3.7zm.6-3c0 .2-.3.5-.5.4L5.1 19c-.3-.1-.4-.3-.4-.5v-.2c0-.2.1-.3.3-.4l1.7-.8c.2-.1.3-.3.3-.4V6.8c0-.2.2-.4.4-.4l6.9-1.6c.3 0 .6.1.6.5v14.5z"/></g></symbol>
		<symbol viewBox="0 0 24 24" id="offline"><path d="M16 16.7c.2-.3.2-.6 0-.9l-.8-.8c-.2-.2-.6-.2-.8 0l-2.1 2c-.1.2-.4.2-.5 0l-2.1-2c-.2-.2-.6-.2-.8 0l-.8.8c-.3.3-.3.6 0 .9l2 2c.1.1.1.4 0 .5l-2 2.1c-.3.2-.3.6 0 .8l.8.8c.2.3.6.3.8 0l2.1-2c.1-.1.4-.1.5 0l2.1 2c.2.3.6.3.8 0l.8-.8c.2-.2.2-.6 0-.8l-2-2.1c-.2-.1-.2-.4 0-.5l2-2zm6-11.3C19.5 2.5 15.9 1 12 1S4.6 2.5 2.1 5.4c-.2.1-.2.5 0 .6l1.4 1.2c.2.2.5.1.7 0C6.2 5 9 3.7 12 3.7s5.9 1.3 7.9 3.5c.2.1.5.1.7 0L22 6c.2-.2.2-.5 0-.6zm-10 2c-1.9 0-3.7.9-5 2.3-.2.2-.2.5 0 .7l1.5 1.1c.2.2.5.2.6 0 .8-.8 1.8-1.3 2.9-1.3s2.2.5 3 1.2c.1.2.4.2.6.1l1.4-1.1c.3-.2.3-.5.1-.7C15.8 8.3 14 7.4 12 7.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="open"><path  d="M3.7 16.2v-.3.5-.2zM21.2.9H2.8C1.8.9.9 1.8.9 2.8v16.6c0 1 .9 1.8 1.9 1.8h5.5c.3 0 .5-.2.5-.4v-1.9c0-.3-.2-.4-.5-.4H4.4c-.4 0-.7-.4-.7-.7V6.2c0-.3.3-.7.7-.7h15.2c.4 0 .7.4.7.7v11.6c0 .3-.3.7-.7.7h-3.9c-.3 0-.5.1-.5.4v1.9c0 .2.2.4.5.4h5.5c1 0 1.9-.8 1.9-1.8V2.8c0-1-.9-1.9-1.9-1.9zM17.3 16l1-1c.3-.3.3-.7 0-1l-5.8-5.8c-.3-.3-.7-.3-1 0L5.7 14c-.3.3-.3.7 0 1l1 .9c.3.3.7.3 1 0l2.1-2.1c.3-.3.8-.1.8.3v8.3c0 .*******.7h1.3c.4 0 .8-.3.8-.7v-8.3c0-.4.4-.6.8-.3l2.1 2.2c.3.2.7.2 1 0z"/></symbol>
		<symbol viewBox="0 0 24 24" id="open_folder"><path d="M21.2 6.5H10.8c-.7 0-1.3-.4-1.7-1L7.5 2.8c-.3-.6-.9-1-1.6-1H2.8c-1 0-1.9.9-1.9 1.9v16.6c0 1 .9 1.9 1.9 1.9h18.4c1 0 1.9-.9 1.9-1.9v-12c0-1-.9-1.8-1.9-1.8zm0-3.7H10.1c-.2 0-.3.2-.2.3l.8 1.2c.1.2.2.3.4.3h10.1c.5 0 1 .1 1.5.3.1.1.4-.1.4-.3 0-1-.9-1.8-1.9-1.8z" /></symbol>
		<symbol viewBox="0 0 24 24" id="opened_folder"><path d="M20.3 6.9c0-1-.8-1.8-1.8-1.8h-6.8c-.9 0-1.6-.9-1.6-.9L8.9 2.8s-.5-1-1.6-1H5.5c-1 0-1.8.9-1.8 1.9v4.1h16.6v-.9zm1.3 2.8H2.4c-1 0-1.7.9-1.4 1.7l2.6 9.7c.2.6.7 1.1 1.4 1.1h14.1c.6 0 1.2-.5 1.3-1.1l2.7-9.7c.2-.8-.5-1.7-1.5-1.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="overflow"><path  d="M17.2 4.6H7.3c-1 0-1.7.8-1.7 1.7v.3c0 .1.1.2.3.2h9c1 0 1.7.8 1.7 1.7v10.2c0 .2.2.3.3.3h.3c.9 0 1.7-.8 1.7-1.7v-11c0-.9-.8-1.7-1.7-1.7zM20.9.9H11c-1 0-1.7.8-1.7 1.7v.3c0 .1.1.3.3.3h9c1 0 1.7.8 1.7 1.7v10.2c0 .1.1.3.3.3h.3c.9 0 1.7-.8 1.7-1.7V2.6c0-.9-.8-1.7-1.7-1.7zM15 10.1c0-1-.7-1.7-1.7-1.7H3.1c-1 0-1.7.7-1.7 1.7v11.3c0 .9.8 1.7 1.7 1.7h10.2c1 0 1.7-.8 1.7-1.7V10.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="package"><path  d="M20.5 11.1h-3.7l-1.5 1.8h5v2.8H3.7v-2.8h4.9l-1.5-1.8H3.5c-.9 0-1.7.7-1.7 1.6v9c0 .8.6 1.4 1.4 1.4h17.6c.8 0 1.4-.6 1.4-1.4v-9c0-.9-.8-1.6-1.7-1.6zm-9.9-9.5v5.8H7.4c-.4 0-.7.4-.4.6l4.6 5.7c.2.1.5.1.7 0L16.9 8c.3-.2 0-.6-.4-.6h-3.1V1.6c0-.4-.3-.7-.7-.7h-1.4c-.4 0-.7.3-.7.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="package_org"><path  d="M20.5 10.6h-5.8l-1.8 1.9h7.4v2.7H3.7v-2.7h3.7l-1.8-1.9H3.5c-.9 0-1.7.8-1.7 1.7v8.9c0 .8.6 1.4 1.4 1.4h17.6c.8 0 1.4-.6 1.4-1.4v-8.9c0-.9-.8-1.7-1.7-1.7zm-11 1.3c.4.4.9.4 1.3 0l8.8-8.8c.2-.1.2-.4 0-.6l-1.3-1.3c-.2-.2-.5-.2-.7 0l-7.4 7.5-3.1-3.1c-.2-.2-.5-.2-.7 0L5.1 6.9c-.2.2-.2.4 0 .6l4.4 4.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="package_org_beta"><path  d="M20.5 10.6h-2.7c-.2.7-.5 1.3-1 1.9h3.5v2.7H3.7v-2.7h2.8v-1.9h-3c-.9 0-1.7.8-1.7 1.7v8.9c0 .8.6 1.4 1.4 1.4h17.6c.8 0 1.4-.6 1.4-1.4v-8.9c0-.9-.8-1.7-1.7-1.7zm-4.3-6c0-1.8-1.6-3.2-3.4-3.2H9c-.4 0-.7.3-.7.7v9.7c0 .*******.7h3.9c1.8 0 3.3-1.5 3.2-3.3 0-.9-.4-1.7-1-2.2.7-.7 1.1-1.5 1.1-2.4zm-6-1.4h2.7c.8 0 1.4.6 1.4 1.4 0 .8-.6 1.4-1.4 1.4h-2.7V3.2zm4.1 6c0 .8-.6 1.4-1.4 1.4h-2.7V7.8h2.7c.8 0 1.4.6 1.4 1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="page"><path  d="M20.5 8.8h-5.2c-1.2 0-1.9-.8-1.9-2V1.7c0-.5-.3-.8-.8-.8H5c-1.2 0-2.2 1-2.2 2.2v17.8c0 1.2 1 2.2 2.2 2.2h14c1.2 0 2.2-1 2.2-2.2V9.5c0-.4-.3-.7-.7-.7zm.6-2.8l-4.9-4.9c-.1-.1-.3-.2-.4-.2-.3 0-.6.3-.6.5v4c0 .8.8 1.5 1.6 1.5h3.9c.3 0 .5-.3.5-.5s0-.4-.1-.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="palette"><path  d="M22.8 8C21.8 3.6 17.2.9 12.1.9 5.9.9.9 5.9.9 12s5 11.1 11.2 11.1c8.6 0 7.9-4.4 5.2-6.1-1.7-1-2.5-3.3-.9-5 3-3.1 7.8 1.8 6.4-4zM6 15.7c-1.3 0-2.3-1-2.3-2.3s1-2.3 2.3-2.3 2.3 1 2.3 2.3-1 2.3-2.3 2.3zm.5-8.8c0-1.3 1-2.3 2.3-2.3s2.3 1 2.3 2.3-1 2.3-2.3 2.3-2.3-1-2.3-2.3zm5 13.4c-1.3 0-2.3-1-2.3-2.3s1-2.3 2.3-2.3 2.3 1 2.3 2.3-1 2.3-2.3 2.3zm4.2-12c-1.3 0-2.3-1-2.3-2.3s1-2.3 2.3-2.3S18 4.7 18 6s-1 2.3-2.3 2.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="paste"><path d="M8.1 5.5h7.8c.4 0 .7-.3.7-.7v-2c0-1-.8-1.9-1.8-1.9H9.2c-1 0-1.8.9-1.8 1.9v2c0 .*******.7zm12.2-2.7h-1.1c-.4 0-.7.3-.7.7v2c0 1.1-.9 1.9-1.9 1.9H7.4c-1 0-1.9-.8-1.9-1.9v-2c0-.4-.3-.7-.7-.7H3.7c-1 0-1.9.8-1.9 1.8v16.6c0 1 .9 1.9 1.9 1.9h16.6c1 0 1.9-.9 1.9-1.9V4.6c0-1-.9-1.8-1.9-1.8zm-2.8 16.1c0 .3-.1.5-.4.5H6.9c-.3 0-.4-.2-.4-.5V18c0-.3.1-.5.4-.5h10.2c.3 0 .4.2.4.5v.9zm0-3.7c0 .3-.1.5-.4.5H6.9c-.3 0-.4-.2-.4-.5v-.9c0-.3.1-.5.4-.5h10.2c.3 0 .4.2.4.5v.9zm0-3.7c0 .3-.1.5-.4.5H6.9c-.3 0-.4-.2-.4-.5v-.9c0-.3.1-.4.4-.4h10.2c.3 0 .4.1.4.4v.9z" /></symbol>
		<symbol viewBox="0 0 24 24" id="people"><path d="M19.4 10.3c-1.3-.5-1.5-1-1.5-1.5s.4-1 .8-1.4c.8-.7 1.2-1.6 1.2-2.7 0-2-1.3-3.8-3.7-3.8-2.1 0-3.4 1.5-3.6 3.3 0 .2.1.3.2.4 1.8 1.1 2.8 3.1 2.8 5.4 0 1.8-.6 3.3-1.9 4.4-.1.1-.1.3 0 .4.3.2 1.1.6 1.5.8.2 0 .3.1.4.1h5.6c1 0 1.9-.9 1.9-1.9v-.2c0-1.6-1.8-2.5-3.7-3.3zm-6.2 6.4c-1.6-.6-1.8-1.2-1.8-1.8 0-.6.5-1.2 1-1.7.9-.7 1.4-1.8 1.4-3.1 0-2.4-1.6-4.5-4.4-4.5-2.8 0-4.5 2.1-4.5 4.5 0 1.3.5 2.4 1.5 3.1.5.5.9 1.1.9 1.7 0 .6-.2 1.2-1.8 1.8-2.3.9-4.6 2-4.6 3.9v.6c0 1 .9 1.9 1.9 1.9h12.8c1.1 0 1.9-.9 1.9-1.9v-.6c0-1.9-2-3-4.3-3.9z" /></symbol>
		<symbol viewBox="0 0 24 24" id="phone_landscape"><path  d="M24 6c0-1-.8-1.8-1.8-1.8H1.8C.8 4.2 0 5 0 6v12c0 1 .8 1.8 1.8 1.8h20.4c1 0 1.8-.8 1.8-1.8V6zM2.3 13.4c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.4.6 1.4 1.4-.6 1.4-1.4 1.4zm18 3c0 .4-.3.7-.7.7H5.3c-.4 0-.7-.3-.7-.7V7.6c0-.4.3-.7.7-.7h14.3c.4 0 .7.3.7.7v8.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="phone_portrait"><path  d="M19.8 1.8C19.8.8 19 0 18 0H6C5 0 4.2.8 4.2 1.8v20.4c0 1 .8 1.8 1.8 1.8h12c1 0 1.8-.8 1.8-1.8V1.8zM12 23.1c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.4.6 1.4 1.4-.6 1.4-1.4 1.4zm5.1-4.4c0 .4-.3.7-.7.7H7.6c-.4 0-.7-.3-.7-.7V4.4c0-.4.3-.7.7-.7h8.8c.4 0 .7.3.7.7v14.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="photo"><path d="M12 9.2c-2 0-3.7 1.7-3.7 3.7s1.7 3.7 3.7 3.7 3.7-1.6 3.7-3.7S14 9.2 12 9.2zm9.2-2.7h-2.4c-.6 0-1.2-.4-1.5-.9L16.2 4c-.3-.8-1.1-1.2-1.9-1.2H9.7c-.8 0-1.6.4-1.9 1.2L6.7 5.6c-.3.5-.9.9-1.6.9H2.8c-1 0-1.9.8-1.9 1.8v11.1c0 1 .9 1.8 1.9 1.8h18.4c1 0 1.9-.8 1.9-1.8V8.3c0-1-.9-1.8-1.9-1.8zm-9.2 12c-3 0-5.5-2.5-5.5-5.6S9 7.4 12 7.4s5.5 2.5 5.5 5.5-2.5 5.6-5.5 5.6z" /></symbol>
		<symbol viewBox="0 0 24 24" id="picklist"><path  d="M22.2 18.9c.5 0 .9-.4.9-.9V6c0-.5-.4-.9-.9-.9H1.8c-.5 0-.9.4-.9.9v12c0 .*******.9h20.4zM2.8 17.1V6.9h18.4v10.2H2.8zM15 10.6h4.3c.1 0 .2.2.1.2l-2.1 2.5c-.1.1-.3.1-.4 0l-2.1-2.5s.1-.2.2-.2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="power"><path d="M15.9 3.6c-.3-.2-.7 0-.7.4v1.7c0 .3.2.7.5.8 2.4 1.4 4 4.2 3.6 7.3-.3 3.3-3.1 6.1-6.5 6.5-4.4.5-8.2-3-8.2-7.4 0-2.7 1.5-5.1 3.7-6.4.3-.1.5-.5.5-.8V4c0-.4-.4-.6-.7-.4-3.9 1.6-6.6 5.6-6.2 10.2.4 4.8 4.2 8.7 8.9 9.2 6.1.7 11.4-4.1 11.4-10.1 0-4.2-2.6-7.8-6.3-9.3zm-2.5-2c0-.4-.3-.7-.7-.7h-1.4c-.4 0-.7.3-.7.7v7.9c0 .*******.7h1.4c.4 0 .7-.4.7-.7V1.6z" /></symbol>
		<symbol viewBox="0 0 24 24" id="preview"><path d="M23.9 11.6C21.7 7.2 17.2 4.2 12 4.2S2.3 7.2.1 11.6c-.1.3-.1.6 0 .8 2.2 4.4 6.7 7.4 11.9 7.4s9.7-3 11.9-7.4c.1-.3.1-.5 0-.8zM12 17.1c-2.8 0-5.1-2.3-5.1-5.1S9.2 6.9 12 6.9s5.1 2.3 5.1 5.1-2.3 5.1-5.1 5.1zm0-8.3c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2z" /></symbol>
		<symbol viewBox="0 0 24 24" id="priority"><path d="M4.2 1.6c0-.4-.4-.7-.7-.7H2.1c-.4 0-.7.3-.7.7v20.8c0 .*******.7h1.4c.3 0 .7-.3.7-.7V1.6zm17.7 2c-7.4 3.8-6.5-4.1-15.4-1-.3.1-.5.4-.5.6V14c0 .3.3.5.6.4 8.9-3 7.9 5.2 15.6.8.3-.1.4-.3.4-.6V3.9c0-.3-.4-.5-.7-.3z" /></symbol>
		<symbol viewBox="0 0 24 24" id="process"><path  d="M7.5 10.7l3.9-4.9c.3-.4.8-.4 1.1 0l3.9 5c.2.1.4.3.6.3h4.4c.4 0 .8-.3.8-.7V3.7c0-1-.9-1.9-1.9-1.9H3.7c-1 0-1.9.9-1.9 1.9v6.7c0 .*******.7h4.4c.3 0 .4-.2.6-.4zm9 2.6l-3.9 4.9c-.3.4-.9.4-1.2 0l-3.9-5c-.2-.1-.3-.3-.6-.3H2.5c-.3 0-.7.3-.7.7v6.7c0 1 .9 1.9 1.9 1.9h16.6c1 0 1.9-.9 1.9-1.9v-6.7c0-.4-.4-.7-.7-.7H17c-.2 0-.4.2-.5.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="push"><path d="M20.3.9H9.2c-1 0-1.8.9-1.8 1.9 0 .3.2.7.4.8.2.1 1.9 1.9 1.9 1.9.2.1.4 0 .4-.2 0-.4.3-.7.7-.7h7.8c.4 0 .8.3.8.7v12.5c0 .3-.4.6-.8.6h-7.8c-.4 0-.6-.3-.6-.6v-.1c0-.2-.3-.3-.4-.1 0 0-1.8 1.7-2 1.8-.2.2-.4.5-.4.9v.9c0 1 .8 1.8 1.8 1.8h11.1c1 0 1.9-.8 1.9-1.8V2.8c0-1-.9-1.9-1.9-1.9zm-5.5 21.3c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.4.6 1.4 1.4-.6 1.4-1.4 1.4zM12.7 11L7 5.3c-.3-.3-.7-.3-1 0l-1 .9c-.2.3-.2.7 0 1l2.2 2.1c.2.3 0 .8-.4.8H.7c-.4.1-.7.4-.7.7v1.4c0 .*******.7h6.1c.4 0 .6.5.3.8L5 15.8c-.3.3-.3.7 0 1l1 1c.2.2.6.2.9 0l5.8-5.8c.3-.2.3-.7 0-1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="puzzle"><path  d="M20.8 17.7c-.1 1.3-.3 2.6-.5 3.9 0 .4-.5.8-.8.8-2.5.3-5 .5-7.5.5-2.4 0-4.9-.1-7.3-.5-.4 0-.8-.4-.9-.8-.3-2-.5-4.1-.5-6.2s.2-4.1.5-6.2c.1-.3.5-.7.9-.8 1.5-.2 3-.3 4.4-.4 0 0 1.2 0 1.1-1.2 0-1-1.8-1.7-1.8-3.4C8.4 2 9.8.9 12 .9c2.3 0 3.6 1.1 3.6 2.5 0 1.8-1.7 2.4-1.8 3.4C13.8 7.9 15 8 15 8c1.5.1 3 .2 4.5.4.3 0 .8.4.8.8.2 1.5.4 2.8.5 4.2 0 .4-.4.9-.8.9h-.4c-.4 0-1-.4-1.3-.7 0 0-1-1-2.1-1-1.7-.1-3 1.4-3 3s1.3 3.1 3 3.1c1-.1 2-1.1 2-1.1.4-.2 1-.5 1.4-.5h.4c.5 0 .8.3.8.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="question"><path  d="M13.1 17.5h-2.3c-.4 0-.6-.2-.6-.6v-.7c0-1.9 1.2-3.7 3-4.3.6-.2 1.1-.5 1.5-1 2.3-2.8.2-6.1-2.6-6.2-1 0-1.9.3-2.7 1-.6.6-1 1.3-1 2.1-.1.2-.4.5-.7.5H5.4c-.5 0-.8-.4-.7-.8.1-1.7.9-3.3 2.2-4.5C8.4 1.6 10.2.8 12.3.9c3.8.1 6.9 3.3 7.1 7.1.1 3.2-1.9 6.1-4.9 7.2-.4.2-.7.5-.7 1v.6c0 .5-.3.7-.7.7zm.7 4.9c0 .4-.3.7-.6.7h-2.4c-.3 0-.6-.3-.6-.7v-2.3c0-.4.3-.7.6-.7h2.4c.3 0 .6.3.6.7v2.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="questions_and_answers"><path  d="M23.1 12.9c0-1.8-1.2-3.3-2.8-3.9C20.2 4.5 16.5.9 12 .9S3.8 4.5 3.7 9C2.1 9.6.9 11.1.9 12.9c0 2 1.4 3.6 3.1 4 1 3.6 4.2 6.2 8 6.2s7-2.6 8-6.2c1.7-.4 3.1-2 3.1-4zm-4.6-4.1l-.1-.1.2.1h-.1zM12 21.2c-3.6 0-6.5-3-6.5-6.6 0-.9.2-2.3.6-3.2 0-.1.1-.2.2-.4 1.4-.5 2.6-1.5 3.3-2.7 1.7 2 4.2 3.4 7 3.4H18c.1.6.3 1.3.4 2.1-.3 1.1-2.1 2.2-4.6 2.4-.1-.3-.4-.5-.7-.5h-2.3c-.4 0-.6.4-.6.7v1.4c0 .4.2.7.6.7h2.3c.3 0 .6-.2.7-.5 1.6 0 3.1-.5 4.2-1.2-.8 2.6-3.2 4.4-6 4.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="record"><path  d="M12 3.7c4.6 0 8.3 3.7 8.3 8.3s-3.7 8.3-8.3 8.3-8.3-3.7-8.3-8.3S7.4 3.7 12 3.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="record_create"><path  d="M8 5.4h8c.4 0 .8-.4.8-.8V3.1c0-1.2-1-2.2-2.2-2.2H9.5c-1.2 0-2.2 1-2.2 2.2v1.5c0 .4.3.8.7.8zm12-2.6h-.8c-.2 0-.3.1-.3.3v1.5c0 1.6-1.3 3-2.9 3H8c-1.6 0-2.9-1.4-2.9-3V3.1c0-.2-.1-.3-.3-.3H4c-1.2 0-2.2 1-2.2 2.2v15.9c0 1.2 1 2.2 2.2 2.2h16c1.2 0 2.2-1 2.2-2.2V5c0-1.2-1-2.2-2.2-2.2zM8 18.6c0 .5-.3.8-.7.8h-.7c-.5 0-.8-.3-.8-.8v-.7c0-.4.3-.7.8-.7h.7c.4 0 .7.3.7.7v.7zM8 15c0 .4-.3.7-.7.7h-.7c-.5 0-.8-.3-.8-.7v-.8c0-.4.3-.7.8-.7h.7c.4 0 .7.3.7.7v.8zm0-3.7c0 .4-.3.7-.7.7h-.7c-.5 0-.8-.3-.8-.7v-.8c0-.4.3-.7.8-.7h.7c.4 0 .7.3.7.7v.8zm10.2 7.3c0 .5-.3.8-.8.8h-7.2c-.4 0-.7-.3-.7-.8v-.7c0-.4.3-.7.7-.7h7.2c.5 0 .8.3.8.7v.7zm0-3.6c0 .4-.3.7-.8.7h-7.2c-.4 0-.7-.3-.7-.7v-.8c0-.4.3-.7.7-.7h7.2c.5 0 .8.3.8.7v.8zm0-3.7c0 .4-.3.7-.8.7h-7.2c-.4 0-.7-.3-.7-.7v-.8c0-.4.3-.7.7-.7h7.2c.5 0 .8.3.8.7v.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="redo"><path  d="M16.4 5.8h.3c.5 0 .5-.4.2-.6l-2.3-2.3c-.2-.2-.2-.7 0-1l1-.9c.2-.3.7-.3 1 0l5.9 5.9c.3.3.3.6 0 .9l-6 6c-.3.2-.6.2-.9-.1l-1-1c-.2-.3-.3-.7 0-1l2.2-2.2c.4-.4.5-.6.1-.8-.1-.1-.5-.1-.5-.1H10c-3.3 0-5.9 2.7-5.9 5.9s2.6 5.9 5.9 5.9h3.7c.4 0 .7.4.7.7v1.5c0 .4-.3.7-.6.7H10c-4.8 0-8.8-3.9-8.8-8.8s4-8.7 8.8-8.7h6.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="refresh"><path  d="M21.5 1.8h-1.4c-.4 0-.7.4-.7.7v3.3c0 .4-.2.6-.6.3-.1-.2-.2-.3-.4-.5-2.3-2.3-5.6-3.2-8.9-2.6-1.1.2-2.3.7-3.2 1.3-2.8 1.9-4.5 4.9-4.5 8.1 0 2.5.9 5 2.7 6.8 1.8 1.9 4.3 3 7 3 2.3 0 4.6-.8 6.3-2.3.3-.3.3-.7.1-1l-1-1c-.2-.2-.7-.3-.9 0-1.7 1.3-4 1.9-6.2 1.3-.6-.1-1.2-.4-1.8-.7-2.6-1.6-3.8-4.7-3.1-7.7.1-.6.4-1.2.7-1.8 1.3-2.2 3.6-3.5 6-3.5 1.8 0 3.6.8 4.9 2.*******.4.5.6.2.4-.2.6-.6.6h-3.2c-.4 0-.7.3-.7.7v1.4c0 .4.3.6.7.6h8.4c.3 0 .6-.2.6-.6V2.5c0-.3-.4-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="relate"><path  d="M16.6 9.2c0-1-.8-1.8-1.8-1.8h-12c-1 0-1.9.8-1.9 1.8v12c0 1 .9 1.9 1.9 1.9h12c1 0 1.8-.9 1.8-1.9v-12zm-3.7 6.5c0 .2-.2.5-.4.5H9.7v2.7c0 .3-.2.5-.5.5h-.9c-.2 0-.5-.2-.5-.5v-2.7H5.1c-.3 0-.5-.3-.5-.5v-.9c0-.3.2-.5.5-.5h2.7v-2.8c0-.2.3-.4.5-.4h.9c.3 0 .5.2.5.4v2.8h2.8c.2 0 .4.2.4.5v.9zm6.9 3.7h-1.3v-2.8h1.3c.3 0 .5-.2.5-.4v-12c0-.3-.2-.5-.5-.5h-12c-.2 0-.4.2-.4.5v1.3H4.6V4.2C4.6 2.4 6 .9 7.8.9h12c1.8 0 3.3 1.5 3.3 3.3v12c0 1.8-1.5 3.2-3.3 3.2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="remove_formatting"><path  d="M20.8 18.9l2.1-2.1c.2-.2.2-.5 0-.7l-1.3-1.3c-.1-.2-.4-.2-.6 0L18.9 17l-2-2c-.1-.2-.4-.2-.6 0L15 16.3c-.2.2-.2.5 0 .6l2 2-2 2c-.1.1-.1.4 0 .6l1.3 1.3c.2.2.5.2.7 0l1.9-1.9 2.1 2c.2.2.5.2.6 0l1.3-1.3c.2-.1.2-.4 0-.6l-2.1-2.1zM2.2 3.7h5L5.3 14.4c-.1.5.2.8.7.8h2.3c.3 0 .7-.2.7-.5l1.9-11H16c.3 0 .7-.2.7-.6l.2-1.4c.1-.4-.2-.8-.7-.8H2.4c-.3 0-.6.3-.6.6l-.3 1.4c-.1.4.3.8.7.8zm10.7 14.1c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v1.4c0 .3.3.6.7.6h10.6c.4 0 .7-.3.7-.6v-1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="remove_link"><path  d="M11.1 16.9c-.3 0-.6-.1-.9-.1-.2-.1-.6-.2-.8-.3-.2 0-.4 0-.5.1l-.2.2c-1.1 1.2-3 1.3-4.3.2-1.3-1.2-1.4-3.2-.1-4.5l3-3c.5-.5.9-.7 1.4-.8.7-.2 1.4-.2 2 .1.3.2.7.4 1 .7.1.1.3.3.4.6.1.2.5.3.7.1L14 9c.2-.2.2-.4 0-.6-.2-.2-.3-.4-.5-.6-.3-.3-.7-.6-1-.8-.6-.4-1.2-.6-1.9-.8-1.2-.2-2.6 0-3.8.6-.4.3-.8.6-1.2 1l-3 2.9c-2.1 2.1-2.3 5.6-.2 7.8 2.2 2.3 5.8 2.4 8 .1l1-1c.3-.2.1-.7-.3-.7zm7.6-6.5c2.2-2.2 2.2-5.9-.1-8-2.3-2.1-5.7-1.9-7.8.2l-1 .9c-.2.3-.1.7.3.8.6 0 1.2.1 1.7.3.2.1.4 0 .5-.1l.2-.2c1.1-1.1 3-1.3 4.3-.2 1.3 1.2 1.4 3.3.1 4.5l-3.1 3.1c-.4.4-.8.6-1.3.8-.7.1-1.4.1-2-.2-.3-.1-.7-.3-1-.7-.1-.1-.3-.3-.4-.5-.1-.3-.5-.3-.7-.1l-1.1 1.1c-.2.2-.2.5-.1.6.2.3.4.5.6.7.3.3.6.5 1 .8.6.3 1.2.6 1.9.7 1.2.2 2.5.1 3.7-.6.5-.2.9-.5 1.3-.9l3-3zm2.1 8.5l2.1-2.1c.2-.2.2-.5 0-.6l-1.3-1.3c-.1-.2-.4-.2-.6 0L18.9 17l-2-2c-.1-.1-.4-.1-.6 0L15 16.3c-.2.2-.2.5 0 .7l2 2-2 1.9c-.1.2-.1.5 0 .7l1.3 1.2c.2.2.5.2.7 0l1.9-1.9L21 23c.2.2.5.2.6 0l1.3-1.3c.2-.2.2-.5 0-.7l-2.1-2.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="replace"><path  d="M9.2 17.3c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v5.1c0 .*******.7h6.9c.4 0 .7-.3.7-.7v-5.1zm-5.5-7.1H1.5c-.5 0-.7.4-.4.6l3.7 3.8c.1.2.4.2.6 0l3.7-3.8c.3-.3 0-.6-.4-.6H6.5c0-2.4 2.3-4.7 4.6-4.7V2.8c-4.2 0-7.4 3.2-7.4 7.4zm15.6-.8c-.2-.2-.5-.2-.7 0L15 13.2c-.3.3-.1.6.4.6h2.2c0 2.8-1.9 4.7-4.7 4.7v2.7c4.2 0 7.5-3.2 7.5-7.4h2.2c.5 0 .7-.4.4-.6l-3.7-3.8zm3.8-7.8c0-.4-.3-.7-.7-.7h-6.9c-.4 0-.7.3-.7.7v5.1c0 .*******.7h6.9c.4 0 .7-.3.7-.7V1.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="reply"><path  d="M8.9 8.4s-.5-.6-.3-.8L11.2 5c.3-.3.3-.7 0-1l-1-1c-.2-.3-.6-.3-.9 0L3 9.2c-.2.3-.2.7 0 1l6.3 6.2c.3.3.7.3.9 0l1-.9c.3-.3.3-.7 0-1l-2.5-2.6c-.3-.3-.1-.7.2-.8 5.1.2 9.3 4.3 9.6 9.5 0 .*******.7h1.4c.4 0 .6-.3.6-.8-.3-6.7-5.4-11.9-12.3-12.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="reply_all"><path  d="M11.3 8.4s-.5-.6-.3-.8L13.6 5c.3-.3.3-.7 0-1l-1-1c-.2-.3-.6-.3-.9 0L5.4 9.2c-.2.3-.2.7 0 1l6.3 6.2c.3.3.7.3.9 0l1-.9c.3-.3.3-.7 0-1l-2.5-2.6c-.3-.3-.1-.7.2-.8 5.1.2 9.3 4.3 9.6 9.5 0 .*******.7H23c.4 0 .6-.3.6-.8-.3-6.7-5.4-11.9-12.3-12.1zm-8.8 1l5.3-5.3c.3-.3.3-.7 0-1V3c-.3-.3-.7-.3-1 0L.6 9.2c-.3.3-.3.7 0 1l6.2 6.2c.3.3.7.3 1 0 .3-.3.3-.7 0-1l-5.3-5.3s-.3-.3 0-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="reset_password"><path d="M19.4 10.6H4.6c-1 0-1.8.8-1.8 1.9v8.7c0 1 .8 1.9 1.8 1.9h14.8c1 0 1.8-.9 1.8-1.9v-8.7c0-1.1-.8-1.9-1.8-1.9zm-5.1 9.9c-.7.5-1.5.7-2.3.7-.3 0-.6 0-.8-.1-1.1-.2-2.1-.8-2.7-1.7l1.6-1c.3.5.8.8 1.4.9.6.2 1.2 0 1.8-.3 1.1-.7 1.3-2.2.6-3.2-.3-.5-.8-.9-1.4-1-.6-.1-1.2 0-1.8.4l-.3.3 1.6 1.6H7.8v-4.2L9 14.1c.2-.2.5-.3.6-.5 1-.6 2.1-.8 3.2-.6 1.1.2 2 .8 2.6 1.7 1.3 2 .8 4.5-1.1 5.8zM4.6 8.4v.1-.1zm.5.4h1.8c.3 0 .5-.2.5-.4v-.1c0-2.6 2.2-4.8 4.9-4.6 2.5.2 4.3 2.3 4.3 4.8v-.1c0 .2.2.4.5.4h1.8c.3 0 .5-.2.5-.4v-.1c0-4.2-3.5-7.6-7.8-7.4-3.9.2-6.9 3.5-7 7.5.*******.5.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="resource_absence"><path  d="M10.1 17.1c0-1.3.4-2.7 1.1-3.8.8-1.4 1.6-1.9 2.3-3 1.2-1.7 1.4-4.1.7-6-.8-1.9-2.5-3-4.6-2.9S6 2.7 5.3 4.6c-.7 2-.4 4.5 1.3 6.1.6.7 1.3 1.7.9 2.6-.3 1-1.4 1.4-2.2 1.7-1.8.8-4 1.9-4.3 4.1-.4 1.7.8 3.5 2.7 3.5h7.8c.4 0 .6-.4.4-.7-1.1-1.4-1.8-3.1-1.8-4.8zm11.3-3.9c-2.2-2.2-5.7-2.2-7.8 0-2.2 2.1-2.2 5.6 0 7.8 2.1 2.2 5.6 2.2 7.8 0s2.2-5.7 0-7.8zM19.8 18c.2.2.2.6 0 .7l-.7.7c-.2.2-.4.2-.6-.1l-1-.9-1 1c-.2.2-.4.2-.6-.1l-.7-.6c-.2-.2-.2-.4 0-.6l1-1-1-1c-.2-.2-.2-.5 0-.6l.6-.7c.2-.2.5-.2.7 0l1 .9 1-.9c.1-.2.5-.3.7-.1l.6.7c.2.2.2.5 0 .7l-1 .9 1 1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="resource_capacity"><path  d="M17.5 11.5c-3.1 0-5.6 2.6-5.6 5.7s2.5 5.6 5.6 5.6 5.6-2.6 5.6-5.6-2.5-5.7-5.6-5.7zm3.9 6.2h-4v-4.3s1.5-.1 2.9 1.4 1.1 2.9 1.1 2.9zm-11.2-.5c0-1.2.5-2.7 1.1-3.8.8-1.4 1.7-2 2.4-3 1.1-1.7 1.3-4.2.6-6.1-.7-1.9-2.5-3-4.6-2.9-2 0-3.7 1.3-4.3 3.2-.8 2.1-.5 4.6 1.2 6.2.7.6 1.3 1.7 1 2.7-.4.9-1.5 1.3-2.2 1.7-1.8.8-4.1 1.9-4.4 4-.4 1.8.8 3.6 2.7 3.6h7.9c.4 0 .6-.4.4-.7-1.1-1.4-1.8-3.1-1.8-4.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="resource_territory"><path  d="M12.3 13.4c.9-1.4 1.9-2 2.6-3 1.1-1.7 1.3-4.2.6-6-.7-1.9-2.5-3-4.5-3S7.3 2.7 6.6 4.6c-.7 2.1-.4 4.6 1.3 6.2.7.6 1.3 1.6 1 2.6-.4.9-1.5 1.3-2.3 1.7-1.8.8-3.9 1.9-4.3 4-.4 1.8.8 3.6 2.7 3.6h8.3c.4 0 .7-.5.5-.7-1.2-1.4-2.4-3.1-2.4-4.9 0-1.2.3-2.6.9-3.7zm5.1 4.8c-1 0-1.8-.8-1.8-1.8s.8-1.7 1.8-1.7 1.8.8 1.8 1.7c0 1-.8 1.8-1.8 1.8zm0-6c-2.4 0-4.2 1.9-4.2 4.2 0 2.9 3 5.7 3.9 6.4.2.1.4.1.6 0 .9-.8 3.9-3.5 3.9-6.4 0-2.3-1.8-4.2-4.2-4.2z"/></symbol>
		<symbol viewBox="0 0 24 24" id="retweet"><path d="M23.8 13.3l-1-1c-.2-.3-.6-.3-.9 0l-1.3 1.3c-.3.3-.8.1-.8-.4V5.5c0-1-.8-1.8-1.8-1.8h-6.7c-.4 0-.7.3-.7.7v1.4c0 .*******.7h5.1c.4 0 .7.3.7.7v6c0 .5-.5.6-.9.4L15 12.4c-.2-.3-.7-.3-.9 0l-1 1c-.3.3-.3.7 0 1l4.9 4.8c.2.3.6.3.9 0l4.9-4.9c.2-.3.2-.7 0-1zm-11.1 4.2H7.6c-.4 0-.7-.3-.7-.7v-6c0-.5.5-.6.9-.4L9 11.6c.2.3.7.3.9 0l1-.9c.3-.3.3-.7 0-1L6.1 4.8c-.3-.3-.7-.3-1 0L.2 9.7c-.3.3-.3.7 0 1l1 .9c.2.3.6.3.9 0l1.3-1.2c.2-.3.8-.1.8.3v7.8c0 1 .8 1.8 1.8 1.8h6.7c.4 0 .7-.3.7-.7v-1.4c0-.3-.3-.7-.7-.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="richtextbulletedlist"><path  d="M3.7 6.2c0 .4-.3.7-.7.7H1.6c-.4 0-.7-.3-.7-.7V4.8c0-.3.3-.6.7-.6H3c.4 0 .7.3.7.6v1.4zm19.4-1.4c0-.3-.3-.6-.7-.6H6.2c-.3 0-.7.3-.7.6v1.4c0 .*******.7h16.2c.4 0 .7-.3.7-.7V4.8zM3.7 11.3c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v1.4c0 .*******.7H3c.4 0 .7-.3.7-.7v-1.4zm17.5 0c0-.4-.3-.7-.7-.7H6.2c-.3 0-.7.3-.7.7v1.4c0 .*******.7h14.3c.4 0 .7-.3.7-.7v-1.4zM3.7 17.8c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v1.4c0 .3.3.6.7.6H3c.4 0 .7-.3.7-.6v-1.4zm19.4 0c0-.4-.3-.7-.7-.7H6.2c-.3 0-.7.3-.7.7v1.4c0 .3.4.6.7.6h16.2c.4 0 .7-.3.7-.6v-1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="richtextindent"><path  d="M24 5.3c0-.4-.3-.7-.7-.7h-7.8c-.4 0-.7.3-.7.7v1.4c0 .*******.7h7.8c.4 0 .7-.3.7-.7V5.3zm-1.8 11.1c0-.4-.4-.7-.7-.7h-6c-.4 0-.7.3-.7.7v1.4c0 .*******.7h6c.3 0 .7-.4.7-.7v-1.4zm1.8-5.6c0-.3-.3-.6-.7-.6h-7.8c-.4 0-.7.3-.7.6v1.4c0 .*******.7h7.8c.4 0 .7-.3.7-.7v-1.4zM12.9 2.5c0-.3-.3-.7-.7-.7h-1.4c-.3 0-.6.4-.6.7v19c0 .3.3.7.6.7h1.4c.4 0 .7-.4.7-.7v-19zM4.3 7.1c-.2-.3-.6-.1-.6.4v2.7h-3c-.4 0-.7.3-.7.6v1.4c0 .*******.7h3v2.7c0 .5.4.7.6.5l3.9-4.2c.1-.2.1-.5 0-.6L4.3 7.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="richtextnumberedlist"><path  d="M23.1 3v1.4c0 .4-.3.7-.7.7H9.9c-.3 0-.7-.3-.7-.7V3c0-.4.4-.7.7-.7h12.5c.4 0 .7.3.7.7zM9.9 9.7h8.3c.4 0 .7-.3.7-.7V7.6c0-.4-.3-.7-.7-.7H9.9c-.3 0-.7.3-.7.7V9c0 .*******.7zm12.5 4.1H9.9c-.3 0-.7.4-.7.7v1.4c0 .*******.7h12.5c.4 0 .7-.3.7-.7v-1.4c0-.3-.3-.7-.7-.7zm-4.2 4.7H9.9c-.3 0-.7.3-.7.7v1.3c0 .*******.7h8.3c.4 0 .7-.3.7-.7v-1.3c0-.4-.3-.7-.7-.7zM1.6 3.7h1.2v5.8c0 .*******.7h.4c.4 0 .7-.4.7-.7V2.8c0-.5-.4-1-.9-1H1.6c-.4 0-.7.4-.7.7V3c0 .*******.7zm3.9 9.2H1.6c-.4 0-.7.3-.7.7v.5c0 .*******.7h3v1.8H1.8c-.5 0-.9.4-.9.9v3.7c0 .5.4 1 .9 1h4c.3 0 .7-.4.7-.7V21c0-.4-.4-.7-.7-.7h-3v-1.8h2.7c.5 0 1-.5 1-1v-3.7c0-.5-.5-.9-1-.9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="richtextoutdent"><path  d="M7.6 10.2h-3V7.4c0-.4-.4-.7-.6-.4L.1 11.2c-.1.2-.1.4 0 .6L4 16c.2.2.6 0 .6-.4v-2.7h3c.3 0 .7-.3.7-.7v-1.4c0-.3-.4-.6-.7-.6zM24 5.3c0-.4-.3-.7-.7-.7h-7.8c-.4 0-.7.3-.7.7v1.4c0 .*******.7h7.8c.4 0 .7-.3.7-.7V5.3zm-1.8 11.1c0-.4-.4-.7-.7-.7h-6c-.4 0-.7.3-.7.7v1.4c0 .*******.7h6c.3 0 .7-.4.7-.7v-1.4zm1.8-5.6c0-.3-.3-.6-.7-.6h-7.8c-.4 0-.7.3-.7.6v1.4c0 .*******.7h7.8c.4 0 .7-.3.7-.7v-1.4zM12.9 2.5c0-.3-.3-.7-.7-.7h-1.4c-.3 0-.6.4-.6.7v19c0 .3.3.7.6.7h1.4c.4 0 .7-.4.7-.7v-19z"/></symbol>
		<symbol viewBox="0 0 24 24" id="right"><path  d="M6.5 20.2V3.8c0-.4.6-.8 1-.4l9.8 8c.3.3.3.9 0 1.2l-9.8 8c-.4.4-1 .1-1-.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="right_align_text"><path  d="M21.5 2.3h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.3.7-.7V3c0-.4-.4-.7-.7-.7zm0 5.5H6.2c-.3 0-.7.4-.7.7v1.4c0 .*******.7h15.3c.3 0 .7-.3.7-.7V8.5c0-.3-.4-.7-.7-.7zm0 11.1H6.2c-.3 0-.7.3-.7.7V21c0 .*******.7h15.3c.3 0 .7-.3.7-.7v-1.4c0-.4-.4-.7-.7-.7zm0-5.5h-19c-.3 0-.7.3-.7.7v1.4c0 .*******.7h19c.3 0 .7-.4.7-.7v-1.4c0-.4-.4-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="rotate"><path d="M22.4.9H21c-.4 0-.7.3-.7.7v3.2c0 .5-.5.7-.7.4-2.2-2.4-5.3-3.6-8.7-3.3-1.2.1-2.3.5-3.4 1-.5.3-1.1.6-1.5 1-.4.2-.4.7-.1 1l.9 1c.3.2.6.3.9.1.6-.4 1.2-.7 1.8-1 .3-.1.6-.2.9-.2 2.9-.6 5.7.6 7.3 2.4.5.7.1 1.1-.3 1.1h-3.3c-.3 0-.7.3-.7.7v1.4c0 .*******.7h8.4c.3 0 .6-.3.6-.6V1.6c0-.4-.3-.7-.7-.7zm-4.2 16.4c-.3-.3-.7-.3-1 0-.7.7-1.6 1.3-2.7 1.7-.2.1-.6.2-.9.2-2.9.6-5.7-.6-7.2-2.4-.6-.7-.2-1.1.3-1.1h3.2c.4 0 .7-.3.7-.7v-1.4c0-.4-.3-.7-.7-.7H1.5c-.3 0-.6.3-.6.6v8.9c0 .*******.7H3c.4 0 .7-.3.7-.7v-3.2c0-.5.5-.7.7-.4 2.2 2.4 5.3 3.6 8.7 3.3 1.2-.1 2.3-.5 3.4-1 1-.5 1.9-1.2 2.6-1.9.3-.3.3-.7 0-1l-.9-.9z" /></symbol>
		<symbol viewBox="0 0 24 24" id="rows"><path  d="M21.5 6.5h-19c-.3 0-.7-.4-.7-.7V4.4c0-.4.4-.7.7-.7h19c.3 0 .7.3.7.7v1.4c0 .3-.4.7-.7.7zm0 6.8h-19c-.3 0-.7-.3-.7-.7v-1.3c0-.4.4-.7.7-.7h19c.3 0 .7.3.7.7v1.4c0 .3-.4.6-.7.6zm0 7h-19c-.3 0-.7-.3-.7-.7v-1.4c0-.3.4-.7.7-.7h19c.3 0 .7.4.7.7v1.4c0 .4-.4.7-.7.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="salesforce1"><path  d="M10 5.5c.8-.8 1.9-1.3 3.1-1.3 1.5 0 2.9.9 3.7 2.2.6-.3 1.3-.5 2-.5 2.9 0 5.2 2.3 5.2 5.2s-2.3 5.1-5.2 5.1h-1c-.6 1.1-1.9 1.9-3.3 1.9-.6 0-1.2-.1-1.7-.4-.6 1.5-2.1 2.6-3.9 2.6-1.9 0-3.5-1.1-4.1-2.8-.3 0-.6.1-.8.1-2.2 0-4-1.8-4-4 0-1.5.7-2.8 1.9-3.5-.2-.5-.3-1.2-.3-1.8 0-2.6 2-4.7 4.6-4.7 1.6.1 3 .8 3.8 1.9"/></symbol>
		<symbol viewBox="0 0 24 24" id="search"><path  d="M22.9 20.9l-6.2-6.1c1.3-1.8 1.9-4 1.6-6.4-.6-3.9-3.8-7.1-7.8-7.4C5 .4.4 5 1 10.5c.3 4 3.5 7.3 7.4 7.8 2.4.3 4.6-.3 6.4-1.5l6.1 6.1c.3.3.7.3 1 0l.9-1c.3-.3.3-.7.1-1zM3.7 9.6c0-3.2 2.7-5.9 5.9-5.9s6 2.7 6 5.9-2.7 6-6 6-5.9-2.6-5.9-6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="settings"><path d="M12 8.8c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.3 3.2 3.3 3.3-1.5 3.3-3.3-1.5-3.2-3.3-3.2zm9.7 6.2L20 13.5c.1-.5.2-1 .2-1.5s-.1-1.1-.2-1.6L21.7 9c.6-.5.8-1.3.4-2l-.7-1.3c-.3-.4-.8-.7-1.4-.7-.2 0-.3 0-.5.1l-2.1.8c-.8-.8-1.8-1.3-2.7-1.6l-.4-2.2c-.1-.7-.8-1.1-1.5-1.1h-1.5c-.7 0-1.4.4-1.5 1.1l-.4 2.1c-1 .4-1.9.9-2.8 1.6L4.5 5c-.2 0-.3-.1-.5-.1-.5 0-1 .3-1.3.8L1.9 7c-.3.6-.2 1.4.4 1.9L4 10.3c-.1.5-.1 1.1-.1 1.6 0 .6 0 1.1.1 1.6l-1.7 1.4c-.5.5-.7 1.3-.4 1.9l.8 1.3c.3.5.8.8 1.3.8.2 0 .4-.1.5-.1l2.1-.8c.9.7 1.8 1.2 2.8 1.6l.3 2.2c.2.7.8 1.2 1.6 1.2h1.4c.8 0 1.4-.5 1.6-1.3l.3-2.2c1.1-.3 2.1-.9 2.9-1.7l2 .8c.2 0 .*******.6 0 1.1-.3 1.4-.7l.7-1.2c.4-.6.2-1.4-.4-1.8zM12 17.1c-2.8 0-5-2.2-5-5.1s2.2-5 5-5 5.1 2.2 5.1 5-2.2 5.1-5.1 5.1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="setup"><path  d="M21.6 15l-1.7-1.5c.1-.5.1-1 .1-1.5s0-1.1-.1-1.6L21.6 9c.6-.5.7-1.3.4-2l-.8-1.3c-.2-.5-.8-.8-1.3-.8-.2 0-.4.1-.5.1l-2.1.8c-.8-.7-1.7-1.2-2.7-1.6l-.3-2.1c-.2-.8-.8-1.2-1.6-1.2h-1.4c-.8 0-1.4.4-1.6 1.2l-.3 2.1c-1 .3-1.9.9-2.8 1.6l-2-.8c-.2-.1-.3-.1-.5-.1-.5 0-1.1.3-1.3.7L2 6.9c-.3.7-.2 1.5.4 2l1.7 1.4c-.1.5-.1 1.1-.1 1.6s0 1 .1 1.5l-1.7 1.5c-.6.4-.7 1.3-.4 1.9l.8 1.4c.2.4.8.7 1.3.7.2 0 .4 0 .5-.1l2.1-.8c.8.8 1.7 1.3 2.7 1.6l.3 2.2c.2.8.8 1.3 1.6 1.3h1.4c.8 0 1.4-.6 1.6-1.3l.3-2.2c1.1-.4 2-1 2.8-1.7l2 .7c.2.1.4.1.5.1.6 0 1.1-.2 1.4-.7l.7-1.2c.3-.6.2-1.4-.4-1.8zM12 17.1c-2.7 0-5-2.2-5-5.1s2.2-5 5-5 5.1 2.2 5.1 5-2.3 5.1-5.1 5.1zm1.4-8.8h-2.1c-.4 0-.6.2-.7.5l-1.3 3.3c-.1.2.1.5.3.5h2.2l-.8 2.8c-.1.2.3.4.4.2l3.3-3.8c.3-.3.1-.6-.3-.6h-1.6l1.5-2.3c.1-.2-.1-.5-.4-.5h-.5z"/></symbol>
		<symbol viewBox="0 0 24 24" id="setup_assistant_guide"><path d="M5.3 11.5c0-.2-.2-.3-.4-.2l-2 1.7c-.1.1-.1.2-.1.4v7.3c0 .5.6.7 1 .4l3.4-2.6c.1-.1.1-.2 0-.4-.8-1.2-1.6-3.3-1.9-6.6zm4.5 6.9c.1 0 .2.1.3.1h3.8c.1 0 .2-.1.3-.1.5-.4 2.7-2.2 2.7-8.5 0-2.9-.8-5-1.8-6.3C13.7 1.5 12 .9 12 .9s-1.8.6-3.2 2.7C7.8 5 7.1 7 7.1 9.9c0 6.3 2.1 8.1 2.7 8.5zM12 6c1.5 0 2.7 1.2 2.7 2.8s-1.2 2.7-2.7 2.7-2.8-1.2-2.8-2.7S10.4 6 12 6zm9.1 7l-2-1.7c-.2-.1-.4 0-.4.2-.3 3.3-1.1 5.4-1.9 6.7v.3l3.4 2.6c.4.4 1 .1 1-.3v-7.4c0-.2 0-.3-.1-.4zm-6.2 7.5c-.1-.1-.3-.2-.4-.2H9.4c-.1 0-.3.1-.4.2-.1.3-.4.8-.6 1.5-.1.5.3 1.1.9 1.1h5.3c.6 0 1-.6.9-1.1-.2-.7-.5-1.2-.6-1.5z" /></symbol>
		<symbol viewBox="0 0 24 24" id="share"><path d="M22.4 13.8H21c-.4 0-.7.4-.7.7v5.1c0 .4-.3.7-.7.7H4.4c-.4 0-.7-.3-.7-.7V9.9c0-.3.3-.7.7-.7h1.8c.4 0 .7-.3.7-.7V7.2c0-.4-.3-.7-.7-.7H2.8c-1 0-1.9.8-1.9 1.8v12.9c0 1 .9 1.9 1.9 1.9h18.4c1 0 1.9-.9 1.9-1.9v-6.7c0-.3-.3-.7-.7-.7zm-6.7-7.3c-4.6 0-8.8 4.1-9.2 8.9 0 .4.3.8.7.8h1.4c.4 0 .6-.3.7-.6.3-3.5 3.3-6.4 6.9-6.4h.7c.4 0 .6.5.3.8l-2.5 2.6c-.3.3-.3.7 0 1l.9.9c.3.3.7.3 1 0l6.3-6.2c.3-.3.3-.7 0-1l-6.2-6.2c-.3-.3-.7-.3-1 0l-1 1c-.3.3-.3.7 0 .9l2.6 2.6c.2.3.1.8-.4.8l-1.2.1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="share_mobile"><path d="M19.4 23.3c1 0 1.8-.9 1.8-1.9V9c0-1-.8-1.8-1.8-1.8h-3.5c-.4 0-.7.3-.7.7v1.3c0 .*******.7h1.8c.4 0 .7.4.7.7v9.2c0 .4-.3.7-.7.7H6.2c-.4 0-.7-.3-.7-.7v-9.2c0-.3.3-.7.7-.7h1.9c.3 0 .7-.3.7-.7V7.9c0-.4-.4-.7-.7-.7H4.6c-1 0-1.8.8-1.8 1.8v12.4c0 1 .8 1.9 1.8 1.9h14.8zm-6-9v-9h2.7c.4 0 .7-.4.4-.6L12.3.8c-.1-.1-.4-.1-.6 0L7.5 4.7c-.2.2 0 .6.5.6h2.6v9c0 .*******.7h1.4c.3 0 .7-.4.7-.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="share_post"><path  d="M12 1.8C5.9 1.8.9 6.4.9 12c0 1.8.5 3.5 1.4 5 .1.2.1.4.1.6l-1 3.2c-.2.6.4 1.1 1 .9l3.2-1.1c.2-.1.4-.1.6.1 1.7.9 3.7 1.5 5.8 1.5 6.2 0 11.1-4.5 11.1-10.2C23 6.4 18 1.8 12 1.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="shield"><path d="M2.2 6.5h19.6c.4 0 .8-.5.7-1-.5-1.5-1.1-2.9-2-4.1-.3-.4-.8-.4-1.1-.1-.8.8-2.1 1.3-3.4 1.3-1.4 0-2.6-.6-3.5-1.5-.3-.3-.8-.3-1.1 0-.9.9-2.1 1.5-3.5 1.5-1.3 0-2.5-.5-3.4-1.3-.3-.3-.9-.2-1.1.1-.9 1.2-1.6 2.6-2 4.1 0 .5.4 1 .8 1zm20.9 2.9c0-.4-.3-.6-.8-.6H1.7c-.5 0-.8.2-.8.6v.2c0 6.9 4.8 12.6 11.1 13.5 6.3-.9 11.1-6.6 11.1-13.5v-.2z" /></symbol>
		<symbol viewBox="0 0 24 24" id="side_list"><path  d="M22.4 1.8H9.9c-.3 0-.7.4-.7.7v19c0 .*******.7h12.5c.4 0 .7-.4.7-.7v-19c0-.3-.3-.7-.7-.7zm-15.7 0H1.6c-.4 0-.7.4-.7.7v2.3c0 .*******.7h5.1c.4 0 .7-.3.7-.7V2.5c0-.3-.3-.7-.7-.7zm0 5.6H1.6c-.4 0-.7.3-.7.7v2.3c0 .*******.7h5.1c.4 0 .7-.3.7-.7V8.1c0-.4-.3-.7-.7-.7zm0 5.5H1.6c-.4 0-.7.3-.7.7v2.3c0 .*******.7h5.1c.4 0 .7-.3.7-.7v-2.3c0-.4-.3-.7-.7-.7zm0 5.6H1.6c-.4 0-.7.3-.7.7v2.3c0 .*******.7h5.1c.4 0 .7-.4.7-.7v-2.3c0-.4-.3-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="signpost"><path d="M22.8 4.2l-1.9-1.5c-.3-.2-.5-.3-.9-.3h-6.5v-.7c0-.5-.3-.8-.8-.8h-1.4c-.5 0-.8.3-.8.8v.7H3.1c-.4 0-.7.3-.7.7v3c0 .*******.7H20c.4 0 .7-.1.9-.2l1.9-1.5c.4-.3.4-.7 0-.9zm-1.9 6.3h-7.4V9.4c0-.2-.2-.4-.4-.4h-2.2c-.2 0-.4.2-.4.4v1.1H4c-.4 0-.7.1-.9.3l-1.9 1.4c-.4.3-.4.7 0 1l1.9 1.4c.3.2.5.3.9.3h16.9c.4 0 .7-.3.7-.7v-3c0-.4-.3-.7-.7-.7zM13.5 20v-2.5c0-.2-.2-.3-.4-.3h-2.2c-.2 0-.4.1-.4.3V20c-1.5.4-2.3 1.3-2.5 2.4-.1.3.2.7.5.7h7c.4 0 .7-.3.6-.7-.3-1.1-1.1-2-2.6-2.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="sms"><path  d="M12 1.8C5.9 1.8 1 6.4 1 12c0 1.7.5 3.4 1.3 4.8.1.3.2.6.1.8l-1.4 4c-.2.3.2.6.6.6l3.9-1.6c.3-.1.5 0 .8.1 1.7.9 3.7 1.5 5.8 1.5 6 0 11-4.6 11-10.2C23 6.4 18.1 1.8 12 1.8zM7.6 13.7c-.2.2-.3.4-.5.6s-.4.2-.7.3c-.2.1-.5.1-.8.1-.3 0-.7 0-1-.2-.3-.1-.6-.3-.9-.6l-.1-.1s0-.1.1-.2l.8-.7c.1-.1.2-.1.2 0s.1.1.1.1c.1.2.2.2.4.3.2.2.4.2.7.1.1 0 .1 0 .2-.1l.2-.1V13c0-.2 0-.2-.1-.3-.1-.1-.2-.1-.4-.2s-.4-.1-.6-.2l-.6-.3c-.3-.1-.4-.3-.5-.5-.2-.2-.3-.5-.3-.8 0-.4.1-.6.2-.9.2-.2.3-.4.5-.5.2-.2.4-.3.7-.3.6-.2 1.1-.2 1.7 0 .*******.7.4l.1.1c.1 0 .1.1 0 .2l-.7.7c-.1.1-.3.1-.4 0l-.1-.1c-.3-.1-.6-.2-.9-.1h-.2l-.1.2v.2c0 .1 0 .2.1.2 0 .1.2.1.4.2s.3.2.6.2l.6.3c.2.1.4.3.5.5.2.2.3.5.3.9 0 .3-.1.5-.2.8zm7.6.6c0 .3-.2.5-.5.5h-.4c-.3 0-.5-.2-.5-.5v-2.7c0-.3-.3-.3-.4-.1l-.8 2.1c0 .2-.2.2-.4.2h-.3c-.2 0-.4-.1-.5-.2l-.8-2.1c-.1-.2-.4-.2-.4.1v2.7c0 .3-.3.5-.6.5h-.4c-.3 0-.4-.2-.4-.5V9.2c0-.2.2-.4.4-.4h1.2c.2 0 .4.1.4.2l.9 2.4c.1.2.4.2.4 0l1-2.4c0-.1.2-.2.4-.2h1.2c.3 0 .5.2.5.4v5.1zm4.9-.6c-.2.2-.3.5-.5.6-.2.1-.4.3-.7.4s-.5.1-.8.1c-.4 0-.7-.1-1-.2-.3-.2-.7-.3-.9-.6l-.1-.1c0-.1 0-.1.1-.2l.7-.7c.1-.1.2-.1.3-.1s.1.2.1.2l.3.3c.*******.8.1.1-.1.2-.1.2-.1l.1-.2.1-.1c0-.2-.1-.3-.1-.3-.1-.1-.2-.2-.4-.2s-.4-.2-.6-.2c-.3-.1-.5-.2-.7-.3-.2-.1-.4-.3-.5-.5-.2-.2-.3-.5-.3-.9 0-.3.1-.6.2-.8.2-.3.3-.4.5-.6.2-.1.5-.3.7-.3.6-.1 1.1-.1 1.7 0 .*******.7.5l.1.1c.1 0 .1.2 0 .3l-.7.7c-.1.1-.2.1-.3 0l-.2-.2c-.3-.1-.6-.2-.9-.1 0 0-.1 0-.1.1l-.2.1v.2c0 .1 0 .*******.*******.3.2 0 .*******.2 0 .*******.*******.*******.*******.1.2 0 .5-.1.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="snippet"><path  d="M6.7 2.8H1.6c-.4 0-.7.3-.7.7v6c0 .*******.7h5.1c.4 0 .7-.4.7-.7v-6c0-.4-.3-.7-.7-.7zm15.7 0H9.9c-.3 0-.7.3-.7.7v1.3c0 .*******.7h12.5c.4 0 .7-.3.7-.7V3.5c0-.4-.3-.7-.7-.7zM9.9 10.2h7.9c.3 0 .7-.4.7-.7V8.1c0-.4-.4-.7-.7-.7H9.9c-.3 0-.7.3-.7.7v1.4c0 .*******.7zm-3.2 3.6H1.6c-.4 0-.7.4-.7.7v6c0 .*******.7h5.1c.4 0 .7-.3.7-.7v-6c0-.3-.3-.7-.7-.7zm15.7 0H9.9c-.3 0-.7.4-.7.7v1.4c0 .*******.7h12.5c.4 0 .7-.3.7-.7v-1.4c0-.3-.3-.7-.7-.7zm-4.6 4.7H9.9c-.3 0-.7.3-.7.7v1.3c0 .*******.7h7.9c.3 0 .7-.3.7-.7v-1.3c0-.4-.4-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="socialshare"><path  d="M18.9 14.8c-1.2 0-2.3.5-3 1.3l-6.8-3.4c.1-.2.1-.5.1-.7 0-.3-.1-.6-.1-.8l6.8-3.4c.7.9 1.8 1.4 3 1.4 2.3 0 4.2-1.9 4.2-4.2S21.2.9 18.9.9 14.8 2.7 14.8 5v.3l-7 3.5c-.8-.6-1.7-1-2.8-1C2.7 7.8.9 9.7.9 12s1.8 4.2 4.1 4.2c1.1 0 2-.4 2.8-1.1l6.9 3.5v.3c0 2.3 1.9 4.2 4.2 4.2s4.1-1.9 4.1-4.2-1.8-4.1-4.1-4.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="sort"><path d="M12.7 7.4c.3-.3.3-.7 0-1L7.4 1.1c-.2-.3-.7-.3-.9 0L1.2 6.4c-.3.3-.3.7 0 1l.9 1c.3.2.7.2 1 0l1.7-1.7c.2-.3.7-.1.7.3v9.8c0 .*******.7h1.4c.4 0 .7-.4.7-.7V7c0-.4.5-.6.8-.3l1.7 1.7c.2.2.6.2.9 0l1-1zm10.1 9.2l-.9-.9c-.3-.3-.7-.3-1 0l-1.7 1.7c-.2.2-.7 0-.7-.4V7.2c0-.4-.4-.7-.7-.7h-1.4c-.4 0-.7.3-.7.7v9.7c0 .5-.5.6-.8.4l-1.7-1.7c-.2-.3-.6-.3-.9 0l-1 1c-.3.3-.3.7 0 1l5.3 5.3c.3.3.7.3 1 0l5.3-5.3c.2-.3.2-.8-.1-1z" /></symbol>
		<symbol viewBox="0 0 24 24" id="spinner"><path d="M7.4 12.7v-1.4c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v1.4c0 .*******.7h5.1c.4 0 .7-.3.7-.7zm.9 2.1c-.3-.3-.7-.3-1 0l-3.6 3.6c-.3.2-.3.7 0 .9l1 1c.2.3.7.3.9 0l3.6-3.6c.3-.3.3-.7 0-1l-.9-.9zm7.4-5.6c.3.3.7.3 1 0l3.6-3.6c.3-.2.3-.7 0-.9l-1-1c-.2-.3-.7-.3-.9 0l-3.6 3.5c-.3.3-.3.7 0 1l.9 1zM5.6 3.7c-.2-.3-.7-.3-.9 0l-1 1c-.3.2-.3.7 0 .9l3.6 3.6c.3.3.7.3 1 0l.9-.9c.3-.3.3-.7 0-1L5.6 3.7zm11.2 11.1c-.3-.3-.7-.3-1 0l-1 .9c-.3.3-.3.7 0 1l3.6 3.6c.2.3.7.3.9 0l1-1c.3-.2.3-.7 0-.9l-3.5-3.6zm-4.1 1.8h-1.4c-.4 0-.7.3-.7.7v5.1c0 .*******.7h1.4c.4 0 .7-.3.7-.7v-5.1c0-.4-.3-.7-.7-.7zm9.7-6h-5.1c-.4 0-.7.3-.7.7v1.4c0 .*******.7h5.1c.4 0 .7-.3.7-.7v-1.4c0-.4-.3-.7-.7-.7zM12.7.9h-1.4c-.4 0-.7.3-.7.7v5.1c0 .*******.7h1.4c.4 0 .7-.3.7-.7V1.6c0-.4-.3-.7-.7-.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="standard_objects"><path  d="M21.3 18l-8.7 4.9c-.4.3-1 .3-1.5 0L2.5 18c-.4-.2-.4-.7 0-.9l2-1.1c.2-.1.3-.1.5 0l5.2 3c.6.2 1.1.4 1.7.4s1.2-.2 1.7-.4l5.2-3c.2-.1.4-.1.5 0l2 1.1c.4.2.4.7 0 .9zm0-5.6l-8.7 5c-.4.2-1 .2-1.5 0l-8.6-5c-.4-.2-.4-.6 0-.8l2-1.2c.2-.1.3-.1.5 0l5.2 3c.6.3 1.1.4 1.7.4s1.2-.1 1.7-.4l5.2-3c.2-.1.4-.1.5 0l2 1.2c.4.2.4.6 0 .8zm-10.1-.6L2.5 6.9c-.3-.2-.3-.7 0-.9l8.7-4.9c.5-.3 1.1-.3 1.5 0L21.4 6c.4.2.4.7 0 .9l-8.7 4.9c-.4.2-1 .2-1.5 0z"/></symbol>
		<symbol viewBox="0 0 24 24" id="stop"><path  d="M3.7 3.7h16.6v16.6H3.7V3.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="strikethrough"><path  d="M5.6 8.4c-.1-.5-.2-1.1-.2-1.6 0-.6.2-1.3.5-2 .2-.6.7-1.3 1.3-1.8.5-.6 1.3-1.1 2.2-1.5.9-.3 2-.6 3.2-.6 1.2 0 2.3.2 3.4.5.8.3 1.6.7 2.3 1.4.3.2.3.7-.1 1L17 4.9c-.3.3-.7.3-1 0-.3-.3-.7-.6-1.1-.8-.6-.3-1.4-.5-2.3-.5-.7 0-1.4.1-1.9.3s-1 .5-1.3.9-.6.6-.7 1-.2.8-.2 1c0 .5.1 1 .2 1.3.2.3-.1.7-.4.7H6c-.2 0-.4-.3-.4-.4zm12.8 6.8h-2.3c-.3 0-.5.4-.4.6.1.3.2.7.2 1 0 .6-.2 1.1-.4 1.6-.3.4-.6.8-1 1.1-.4.3-.9.5-1.4.6-.5.2-1 .3-1.5.3-.8 0-1.7-.2-2.5-.6-.6-.3-1.1-.6-1.5-1.2-.3-.2-.7-.3-1 0l-1.3 1.1c-.3.2-.3.7 0 .9.6.8 1.4 1.3 2.4 1.7 1.2.5 2.5.7 3.9.7 1 0 2-.2 2.8-.5.9-.3 1.7-.7 2.4-1.3.6-.5 1.2-1.2 1.6-2 .3-.9.6-1.8.6-2.8 0-.3 0-.6-.1-.9-.1-.1-.3-.3-.5-.3zM23 11c-.1-.2-.3-.4-.6-.4H1.6c-.3 0-.5.2-.6.4-.1.1-.1.2-.1.3v1.3c0 .4.3.8.7.8h20.8c.4 0 .7-.4.7-.8v-1.3c0-.1 0-.2-.1-.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="success"><path  d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zm6.2 8.3l-7.1 7.2c-.3.3-.7.3-1 0l-3.9-3.9c-.2-.3-.2-.8 0-1.1l1-1c.3-.2.8-.2 1.1 0l2 2.1c.2.2.5.2.7 0l5.2-5.3c.2-.3.7-.3 1 0l1 1c.3.2.3.7 0 1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="summary"><path  d="M22.4.9H1.6c-.4 0-.7.3-.7.7v2.3c0 .*******.7h20.8c.4 0 .7-.3.7-.7V1.6c0-.4-.3-.7-.7-.7zm0 5.6H6.2c-.3 0-.7.3-.7.7v1.3c0 .*******.7h16.2c.4 0 .7-.3.7-.7V7.2c0-.4-.3-.7-.7-.7zm0 9.2H6.2c-.3 0-.7.3-.7.7v1.4c0 .*******.7h16.2c.4 0 .7-.4.7-.7v-1.4c0-.4-.3-.7-.7-.7zm0 4.6h-18c-.4 0-.7-.3-.7-.7v-3.2c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v6c0 .*******.7h20.8c.4 0 .7-.3.7-.7V21c0-.4-.3-.7-.7-.7zm0-9.2h-18c-.4 0-.7-.3-.7-.7V7.2c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v6c0 .3.3.6.7.6h20.8c.4 0 .7-.3.7-.6v-1.4c0-.4-.3-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="summarydetail"><path  d="M22.4.9H1.6c-.4 0-.7.3-.7.7v2.3c0 .*******.7h20.8c.4 0 .7-.3.7-.7V1.6c0-.4-.3-.7-.7-.7zM9.5 6.5H6.2c-.3 0-.7.3-.7.7v1.3c0 .*******.7h3.3c.3 0 .7-.3.7-.7V7.2c0-.4-.4-.7-.7-.7zm6.4 0h-3.2c-.4 0-.7.3-.7.7v1.3c0 .*******.7h3.2c.4 0 .7-.3.7-.7V7.2c0-.4-.3-.7-.7-.7zm6.5 0h-3.2c-.4 0-.7.3-.7.7v1.3c0 .*******.7h3.2c.4 0 .7-.3.7-.7V7.2c0-.4-.3-.7-.7-.7zM9.5 15.7H6.2c-.3 0-.7.3-.7.7v1.4c0 .*******.7h3.3c.3 0 .7-.4.7-.7v-1.4c0-.4-.4-.7-.7-.7zm6.4 0h-3.2c-.4 0-.7.3-.7.7v1.4c0 .*******.7h3.2c.4 0 .7-.4.7-.7v-1.4c0-.4-.3-.7-.7-.7zm6.5 0h-3.2c-.4 0-.7.3-.7.7v1.4c0 .*******.7h3.2c.4 0 .7-.4.7-.7v-1.4c0-.4-.3-.7-.7-.7zm0 4.6h-18c-.4 0-.7-.3-.7-.7v-3.2c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v6c0 .*******.7h20.8c.4 0 .7-.3.7-.7V21c0-.4-.3-.7-.7-.7zm0-9.2h-18c-.4 0-.7-.3-.7-.7V7.2c0-.4-.3-.7-.7-.7H1.6c-.4 0-.7.3-.7.7v6c0 .3.3.6.7.6h20.8c.4 0 .7-.3.7-.6v-1.4c0-.4-.3-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="switch"><path  d="M22 8.2l-9.5 9.6c-.3.2-.7.2-1 0L2 8.2c-.2-.3-.2-.7 0-1l1-1c.3-.3.8-.3 1.1 0l7.4 7.5c.3.3.7.3 1 0l7.4-7.5c.3-.3.8-.3 1.1 0l1 1c.2.3.2.7 0 1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="sync"><path  d="M22.1 13.5c0-.3-.3-.7-.7-.7h-1.6c-.4 0-.6.3-.6.7 0 .1 0 .2-.1.3-.1.6-.4 1.2-.7 1.7-1.3 2.3-3.5 3.5-5.9 3.5-1.9 0-3.6-.7-4.9-2-.3-.2-.4-.4-.6-.7-.1-.3.2-.5.6-.5h3.2c.4 0 .7-.3.7-.7v-1.4c0-.4-.2-.7-.6-.7H2.4c-.3 0-.6.3-.6.6V22c.1.4.4.7.8.7H4c.3 0 .7-.3.7-.7v-3.2c0-.4.2-.6.5-.3.2.1.3.3.5.4 2.3 2.3 5.5 3.3 8.8 2.7 1.2-.3 2.3-.7 3.3-1.4 2.4-1.6 3.9-4 4.3-6.6v-.1zm-20.2-3c0 .*******.7h1.6c.4 0 .6-.3.6-.7 0-.1 0-.2.1-.3.1-.6.4-1.2.7-1.7C6.9 6.2 9.1 5 11.5 5c1.9 0 3.6.7 4.9 2 .*******.6.7.1.3-.2.5-.6.5h-3.2c-.4 0-.7.3-.7.7v1.4c0 .4.2.7.6.7h8.5c.3 0 .6-.3.6-.6V2c-.1-.4-.4-.7-.8-.7H20c-.3 0-.7.3-.7.7v3.2c0 .4-.2.6-.5.3-.2-.1-.3-.3-.5-.4-2.3-2.3-5.5-3.3-8.8-2.7-1.2.3-2.3.7-3.3 1.4-2.4 1.6-3.9 4-4.3 6.6v.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="table"><path  d="M21.5.9h-19c-.3 0-.7.3-.7.7v2.3c0 .*******.7h19c.3 0 .7-.3.7-.7V1.6c0-.4-.4-.7-.7-.7zM6.7 6.5H2.5c-.3 0-.7.3-.7.7v1.3c0 .*******.7h4.2c.4 0 .7-.3.7-.7V7.2c0-.4-.3-.7-.7-.7zm7.4 0H9.9c-.3 0-.7.3-.7.7v1.3c0 .*******.7h4.2c.3 0 .7-.3.7-.7V7.2c0-.4-.4-.7-.7-.7zm7.4 0h-4.2c-.4 0-.7.3-.7.7v1.3c0 .*******.7h4.2c.3 0 .7-.3.7-.7V7.2c0-.4-.4-.7-.7-.7zM6.7 11.1H2.5c-.3 0-.7.3-.7.7v1.4c0 .3.4.6.7.6h4.2c.4 0 .7-.3.7-.6v-1.4c0-.4-.3-.7-.7-.7zm7.4 0H9.9c-.3 0-.7.3-.7.7v1.4c0 .3.4.6.7.6h4.2c.3 0 .7-.3.7-.6v-1.4c0-.4-.4-.7-.7-.7zm7.4 0h-4.2c-.4 0-.7.3-.7.7v1.4c0 .3.3.6.7.6h4.2c.3 0 .7-.3.7-.6v-1.4c0-.4-.4-.7-.7-.7zM6.7 15.7H2.5c-.3 0-.7.3-.7.7v1.4c0 .*******.7h4.2c.4 0 .7-.4.7-.7v-1.4c0-.4-.3-.7-.7-.7zm7.4 0H9.9c-.3 0-.7.3-.7.7v1.4c0 .*******.7h4.2c.3 0 .7-.4.7-.7v-1.4c0-.4-.4-.7-.7-.7zm7.4 0h-4.2c-.4 0-.7.3-.7.7v1.4c0 .*******.7h4.2c.3 0 .7-.4.7-.7v-1.4c0-.4-.4-.7-.7-.7zM6.7 20.3H2.5c-.3 0-.7.3-.7.7v1.4c0 .*******.7h4.2c.4 0 .7-.3.7-.7V21c0-.4-.3-.7-.7-.7zm7.4 0H9.9c-.3 0-.7.3-.7.7v1.4c0 .*******.7h4.2c.3 0 .7-.3.7-.7V21c0-.4-.4-.7-.7-.7zm7.4 0h-4.2c-.4 0-.7.3-.7.7v1.4c0 .*******.7h4.2c.3 0 .7-.3.7-.7V21c0-.4-.4-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="tablet_landscape"><path  d="M23.1 4.6c0-1-.9-1.8-1.9-1.8H2.8c-1 0-1.9.8-1.9 1.8v14.8c0 1 .9 1.8 1.9 1.8h18.4c1 0 1.9-.8 1.9-1.8V4.6zM3.2 13.4c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.4.6 1.4 1.4-.7 1.4-1.4 1.4zm17.1 4.4c0 .3-.3.7-.7.7H6.2c-.3 0-.7-.4-.7-.7V6.2c0-.3.4-.7.7-.7h13.4c.4 0 .7.4.7.7v11.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="tablet_portrait"><path  d="M21.2 2.8c0-1-.8-1.9-1.8-1.9H4.6c-1 0-1.8.9-1.8 1.9v18.4c0 1 .8 1.9 1.8 1.9h14.8c1 0 1.8-.9 1.8-1.9V2.8zM12 22.2c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.3.6 1.3 1.4-.6 1.4-1.3 1.4zm6.5-4.4c0 .3-.4.7-.7.7H6.2c-.3 0-.7-.4-.7-.7V4.4c0-.4.4-.7.7-.7h11.6c.3 0 .7.3.7.7v13.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="tabset"><path  d="M14.6.9H9.4c-.3 0-.5.3-.5.6v1.2c0 .3.2.6.5.6h5.2c.3 0 .5-.3.5-.6V1.5c.1-.3-.2-.6-.5-.6zm7.9 0h-5.2c-.3 0-.6.3-.6.6v1.2c0 .3.3.6.6.6h5.2c.3-.1.6-.3.6-.6V1.5c0-.3-.3-.6-.6-.6zm0 3.9H7.8c-.3 0-.6-.3-.6-.6V1.5c0-.3-.2-.6-.6-.6H1.5c-.3 0-.6.3-.6.6v21c0 .3.3.6.6.6h21c.3 0 .6-.3.6-.6V5.4c0-.3-.3-.6-.6-.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="task"><path  d="M11.1 3.2l-.8-.8c-.2-.2-.6-.2-.8 0L4.6 7.3l-2-1.9c-.2-.3-.5-.3-.8 0l-.7.7c-.3.3-.3.6 0 .8l2.7 2.7c.2.3.5.4.8.4.2 0 .5-.1.8-.4L11.1 4c.2-.2.2-.5 0-.8zm11.2 5.3h-9.6c-.4 0-.7-.3-.7-.7V6.3c0-.4.3-.8.7-.8h9.6c.5 0 .8.4.8.8v1.5c0 .4-.3.7-.8.7zm0 6.6H10.5c-.4 0-.8-.3-.8-.7v-1.5c0-.4.4-.8.8-.8h11.8c.5 0 .8.4.8.8v1.5c0 .4-.3.7-.8.7zM6 15.1H4.5c-.4 0-.7-.3-.7-.7v-1.5c0-.4.3-.8.7-.8H6c.4 0 .7.4.7.8v1.5c.1.4-.3.7-.7.7zm0 6.6H4.5c-.4 0-.7-.3-.7-.7v-1.5c0-.4.3-.8.7-.8H6c.4 0 .7.4.7.8V21c.1.4-.3.7-.7.7zm16.3 0H10.5c-.4 0-.8-.3-.8-.7v-1.5c0-.4.4-.8.8-.8h11.8c.5 0 .8.4.8.8V21c0 .4-.3.7-.8.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="text_background_color"><path  d="M12 7.4l1.9 4.6H9.8l1.8-4.6h.4zm10.2-3.7v16.6c0 1-.9 1.9-1.9 1.9H3.7c-1 0-1.9-.9-1.9-1.9V3.7c0-1 .9-1.9 1.9-1.9h16.6c1 0 1.9.9 1.9 1.9zm-2.6 15.1L14.1 5.1c-.2-.3-.4-.5-.7-.5h-3.3c-.3 0-.5.2-.6.5L4.4 18.8c-.1.3.1.6.4.6h1.9c.3 0 .5-.2.6-.5l1.5-4.1H15l1.6 4.1c.1.3.4.5.7.5h1.9c.3 0 .5-.3.4-.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="text_color"><path  d="M4.8 16.6h1.9c.3 0 .5-.2.6-.5L8.8 12H15l1.6 4.1c.1.3.4.5.7.5h1.9c.3 0 .5-.3.4-.6L14 2.3c-.1-.3-.3-.5-.6-.5h-3.2c-.3 0-.6.2-.7.5L4.4 16c-.1.3.1.6.4.6zm6.8-12h.4l2 4.6H9.8l1.8-4.6zm10.8 14.8H1.6c-.4 0-.7.3-.7.7v1.4c0 .*******.7h20.8c.4 0 .7-.4.7-.7v-1.4c0-.4-.3-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="threedots"><path  d="M3.7 9.2c1.5 0 2.8 1.3 2.8 2.8s-1.3 2.8-2.8 2.8S.9 13.5.9 12s1.3-2.8 2.8-2.8zm8.3 0c1.5 0 2.8 1.3 2.8 2.8s-1.3 2.8-2.8 2.8-2.8-1.3-2.8-2.8 1.3-2.8 2.8-2.8zm8.3 0c1.5 0 2.8 1.3 2.8 2.8s-1.3 2.8-2.8 2.8-2.8-1.3-2.8-2.8 1.3-2.8 2.8-2.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="threedots_vertical"><path  d="M9.2 20.3c0-1.5 1.3-2.8 2.8-2.8s2.8 1.3 2.8 2.8-1.3 2.8-2.8 2.8-2.8-1.3-2.8-2.8zm0-8.3c0-1.5 1.3-2.8 2.8-2.8s2.8 1.3 2.8 2.8-1.3 2.8-2.8 2.8-2.8-1.3-2.8-2.8zm0-8.3C9.2 2.2 10.5.9 12 .9s2.8 1.3 2.8 2.8-1.3 2.8-2.8 2.8-2.8-1.3-2.8-2.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="thunder"><path d="M14 1.4c-.7-.6-1.7-.6-2.4 0L1.4 11.6c-.6.7-.6 1.7 0 2.4l5.4 5.4c.7.7 1.8.7 2.5 0L19.4 9.3c.7-.7.7-1.8 0-2.5L14 1.4zm-8.4.9c-.2-.2-.6-.2-.9 0L2.3 4.7c-.2.3-.2.7 0 .9l.9.9c.3.3.7.3.9 0l2.4-2.4c.3-.2.3-.6 0-.9l-.9-.9zm8.7 14.4c-.2.3-.2.7 0 .9l5.3 5.3c.2.2.6.2.9 0l2.4-2.4c.2-.3.2-.7 0-.9l-5.2-5.3c-.3-.2-.7-.2-1 0l-2.4 2.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="tile_card_list"><path  d="M6.7 1.8H2.5c-.3 0-.7.4-.7.7v7.9c0 .*******.7h4.2c.4 0 .7-.3.7-.7V2.5c0-.3-.3-.7-.7-.7zm7.4 0H9.9c-.3 0-.7.4-.7.7v7.9c0 .*******.7h4.2c.3 0 .7-.3.7-.7V2.5c0-.3-.4-.7-.7-.7zm7.4 0h-4.2c-.4 0-.7.4-.7.7v7.9c0 .*******.7h4.2c.3 0 .7-.3.7-.7V2.5c0-.3-.4-.7-.7-.7zM6.7 12.9H2.5c-.3 0-.7.3-.7.7v7.9c0 .*******.7h4.2c.4 0 .7-.4.7-.7v-7.9c0-.4-.3-.7-.7-.7zm7.4 0H9.9c-.3 0-.7.3-.7.7v7.9c0 .*******.7h4.2c.3 0 .7-.4.7-.7v-7.9c0-.4-.4-.7-.7-.7zm7.4 0h-4.2c-.4 0-.7.3-.7.7v7.9c0 .*******.7h4.2c.3 0 .7-.4.7-.7v-7.9c0-.4-.4-.7-.7-.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="topic"><path d="M8 16.3c0-.1-.2-.3-.3-.3l-1-.3c-.2-.1-.4 0-.5.2l-1.8 3c-.4.9-.2 1.1.7.7l3-1.8c.2-.1.3-.3.3-.5l-.4-1zm8-8.6c0 .1.2.3.3.3l1 .3c.2.1.4 0 .5-.2L19.6 5c.4-.8.2-1.1-.7-.6l-3 1.7c-.2.1-.3.4-.3.5l.4 1.1zm-9.8.4c.1.2.3.3.5.3l1-.3c.1-.1.3-.2.3-.3l.3-1.1c.1-.1 0-.4-.2-.5l-3-1.8c-.9-.4-1.1-.2-.7.7l1.8 3zm11.6 7.8c-.1-.2-.3-.3-.5-.3l-1 .3c-.1.1-.3.2-.3.3l-.3 1.1c-.1.2 0 .4.2.5l3.1 1.8c.8.4 1.1.2.6-.7l-1.8-3zm4.7-4.3l-7.6-2c-.3 0-.5-.3-.5-.5l-2-7.6c-.3-.8-.6-.8-.8 0l-2 7.6c-.1.3-.3.5-.6.5l-7.5 2c-.8.3-.8.6 0 .8l7.6 2c.*******.5.6l2 7.5c.3.8.6.8.8 0l2-7.5c.1-.3.3-.5.6-.6l7.5-2c.8-.2.8-.6 0-.8zM12 13.8c-1 0-1.8-.8-1.8-1.8s.8-1.8 1.8-1.8 1.8.8 1.8 1.8-.8 1.8-1.8 1.8z" /></symbol>
		<symbol viewBox="0 0 24 24" id="touch_action"><path  d="M17.4 13.8l-3.7-1.3c-.3-.1-.5-.4-.5-.7V6.7c0-.7-.7-1.3-1.5-1.3h-.1c-.8 0-1.4.6-1.4 1.3v10c0 .8-1.1 1.2-1.6.4l-.9-2c-.5-.9-1.7-1.1-2.5-.5l-.6.4 3.2 7.6c.1.3.4.5.8.5h8.3c.5 0 .8-.3.9-.7l1.4-5.2c.4-1.5-.4-2.9-1.8-3.4zm-9.5-3.2v-4c.1-1.8 1.7-3.4 3.5-3.5h.4c1.9.1 3.4 1.7 3.5 3.5v4c0 .3.4.5.7.3 1-1.1 1.6-2.5 1.6-4 0-3.4-3-6.2-6.5-5.9-2.7.3-4.9 2.3-5.3 5-.3 1.8.3 3.6 1.5 4.9.2.2.6 0 .6-.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="trail"><path  d="M12.8.9c1.6 0 2.8 1.2 2.8 2.7s-1.3 2.8-2.8 2.8-2.7-1.2-2.7-2.8S11.3.9 12.8.9zm7 7.5c-.5-.1-1 .3-1.1.8l-.2 2.7c-.1 0-.2.1-.3.1h-2.5l-1.8-3.1c-.1-.3-.4-.5-.7-.5L10.5 8c-.4-.1-.9.2-1.1.6l-2 5.2c-.2.5 0 .9.4 1.1l5 3.4.4 3.9c0 .5.5.9 1 .9.6 0 1.1-.5 1-1l-.4-4.8c0-.2-.2-.5-.4-.6l-2.7-3.1 1-2.5 1.2 2.1c.2.3.5.6.9.6h3.5l-1 8.3c-.1.5.3.9.8 1 .1 0 .1-.1.1-.1.5 0 1-.3 1-.8l1.6-12.9c0-.4-.4-.9-1-.9zM5.6 12.8l1.7-4.4c.1-.3.3-.6.5-.8l-.3-.1c-1.5-.2-2.8.7-3.3 2.1L3.3 12c-.2.5.1 1.1.6 1.2l.4.1c.6.2 1.1-.1 1.3-.5zm.7 3.4l-2.1 6.2c-.1.4.1.6.5.6h1.1c.4 0 .8-.2 1-.6l2-4.5-2.3-1.4c0-.1-.1-.2-.2-.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="turn_off_notifications"><path  d="M10.8 12.3c.3.3.6.3.9 0l9.9-9.1c.2-.3.1-.9-.6-.9H1.6c-.5 0-1 .5-.6.9l9.8 9.1zm3.1 5.1c.2-2.6 2.3-4.8 4.9-5 .3 0 .5-.1.7-.1.8 0 1.5.2 2.2.5V7.1c0-.5-.5-.7-.8-.4l-7.7 7.1c-.5.5-1.2.7-1.9.7s-1.4-.2-1.9-.7L1.8 6.7c-.4-.3-.9-.1-.9.4v9c0 1.2.9 2.1 2.1 2.1h10.9v-.8zm5.5-3.1c-2 0-3.7 1.7-3.7 3.7s1.7 3.7 3.7 3.7 3.7-1.7 3.7-3.7-1.6-3.7-3.7-3.7zm2.8 4.4c0 .1-.1.2-.2.2h-5.1c-.1 0-.2-.1-.2-.2v-1.4c0-.1.1-.2.2-.2H22c.1 0 .2.1.2.2v1.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="undelete"><path d="M19.2 9.2H4.8c-.3 0-.6.4-.6.7v10.9c0 1.3 1 2.3 2.3 2.3h11c1.3 0 2.3-1 2.3-2.3V9.9c0-.3-.3-.7-.6-.7zm-7.2 12v-1.8c1.5 0 2.8-1.3 2.8-2.8s-1.3-2.8-2.8-2.8c-.7 0-1.4.4-1.9.9l1.1 1.1c.1.1 0 .4-.2.4H7.6c-.1 0-.2-.1-.2-.2v-3.4c0-.2.2-.3.4-.2l1 1c.8-.8 2-1.4 3.2-1.4 2.6 0 4.7 2.1 4.7 4.7s-2.2 4.5-4.7 4.5zm9-16.6h-5.8V2.8c0-1-.8-1.9-1.8-1.9h-2.8c-1 0-1.8.9-1.8 1.9v1.8H3c-.4 0-.7.3-.7.7v1.4c0 .*******.7h18c.4 0 .7-.3.7-.7V5.3c0-.4-.3-.7-.7-.7zm-7.6 0h-2.8V3.2c0-.2.2-.4.5-.4h1.8c.3 0 .5.2.5.4v1.4z" /></symbol>
		<symbol viewBox="0 0 24 24" id="undeprecate"><path  d="M22.2 3.2H1.8c-.5 0-.9.4-.9 1v12c0 .*******.9h7.5c.5 2.6 2.7 4.6 5.5 4.6s5-2 5.4-4.6h2c.5 0 .9-.4.9-.9v-12c0-.6-.4-1-.9-1zm-8.1 15.9l-2.7-2.8 1.2-1.3 1.5 1.5 3.3-3.3 1.2 1.3-4.5 4.6zm7.1-3.9h-1c-.4-2.6-2.7-4.6-5.4-4.6s-5.1 2-5.5 4.6H2.8V5.1h18.4v10.1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="underline"><path  d="M20.5 19.4h-17c-.4 0-.7.3-.7.7v1.4c0 .*******.7h17c.4 0 .7-.4.7-.7v-1.4c0-.4-.3-.7-.7-.7zm-8.8-1.9c-3.5-.1-6.2-3.1-6.2-6.6V4.6c0-.5.5-.9 1-.9h.9c.5 0 .9.4.9.9v6.3c0 2 1.5 3.7 3.5 3.9 2.1.1 3.9-1.6 3.9-3.7V4.6c0-.5.4-.9.9-.9h.9c.5 0 1 .4 1 .9v6.5c0 3.7-3.1 6.6-6.8 6.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="undo"><path  d="M14 5.8c4.8 0 8.7 3.9 8.7 8.7s-3.9 8.8-8.7 8.8h-3.8c-.4 0-.6-.3-.6-.7v-1.5c0-.3.3-.7.6-.7H14c3.3 0 5.9-2.6 5.9-5.9S17.3 8.6 14 8.6H7.6s-.4 0-.5.1c-.4.2-.3.4 0 .8l2.3 2.2c.2.3.2.7-.1 1l-1 1c-.3.3-.6.3-.9.1l-6-6c-.2-.3-.2-.6 0-.9l6-5.9c.3-.3.7-.3 1 0l.9.9c.3.3.3.8 0 1L7.1 5.2c-.3.2-.3.6.1.6H14z"/></symbol>
		<symbol viewBox="0 0 24 24" id="unlock"><path d="M4.6 8.4v.1-.1zm14.8 2.2h-12V8.4c0-2.4 1.8-4.6 4.3-4.7 2.2-.1 4.1 1.3 4.7 3.3.1.2.3.4.5.4h1.9c.3 0 .5-.3.4-.6-.7-3.5-3.8-6.1-7.6-5.9-3.9.2-6.9 3.6-7 7.5v2.2c-1 0-1.8.8-1.8 1.9v8.7c0 1 .8 1.9 1.8 1.9h14.8c1 0 1.8-.9 1.8-1.9v-8.7c0-1.1-.8-1.9-1.8-1.9zm-5.3 9.1c.1.3-.1.6-.4.6h-3.4c-.3 0-.6-.3-.5-.6l.9-2.8c-.7-.4-1.1-1.3-1-2.2.2-.9.9-1.5 1.8-1.7 1.5-.3 2.8.8 2.8 2.1 0 .8-.4 1.5-1 1.8l.8 2.8z" /></symbol>
		<symbol viewBox="0 0 24 24" id="unmuted"><path d="M19.2 8.3c-.7 0-1.2.5-1.2 1.1v1.9c0 3.2-2.7 5.9-6 5.9s-6.1-2.7-6.1-5.9V9.4c0-.6-.5-1.1-1.1-1.1s-1.1.5-1.1 1.1v1.9c0 4.1 3.1 7.4 7.1 8v1.6H9c-.7 0-1.2.4-1.2 1.1s.5 1.1 1.2 1.1h6c.6 0 1.2-.5 1.2-1.1s-.6-1.1-1.2-1.1h-1.9v-1.6c4.1-.6 7.2-3.9 7.2-8V9.4c0-.6-.5-1.1-1.1-1.1zM12 15c2 0 3.7-1.7 3.7-3.7V4.6c0-2.1-1.6-3.7-3.7-3.7S8.3 2.5 8.3 4.6v6.7c0 2 1.7 3.7 3.7 3.7z" /></symbol>
		<symbol viewBox="0 0 24 24" id="up"><path  d="M20.2 17.5H3.8c-.4 0-.8-.6-.4-1l8-9.8c.3-.3.9-.3 1.2 0l8 9.8c.4.4.1 1-.4 1z"/></symbol>
		<symbol viewBox="0 0 24 24" id="upload"><path d="M22.4 14.3H21c-.4 0-.7.4-.7.7v4.6c0 .4-.3.7-.7.7H4.4c-.4 0-.7-.3-.7-.7V15c0-.3-.3-.7-.7-.7H1.6c-.4 0-.7.4-.7.7v6.2c0 1 .9 1.9 1.9 1.9h18.4c1 0 1.9-.9 1.9-1.9V15c0-.3-.3-.7-.7-.7zM12.5 1.1c-.3-.3-.7-.3-1 0L5.3 7.3c-.3.3-.3.7 0 1l.9 1c.3.3.7.3 1 0l2.6-2.6c.3-.3.8-.1.8.3v9.8c0 .*******.7h1.3c.4 0 .8-.4.8-.7V7.1c0-.5.4-.6.8-.4l2.6 2.6c.2.3.6.3.9 0l1-.9c.3-.3.3-.7 0-1l-6.2-6.3z" /></symbol>
		<symbol viewBox="0 0 24 24" id="user"><path  d="M23.1 19.8v1.1c0 1.2-1 2.2-2.2 2.2H3.1c-1.2 0-2.2-1-2.2-2.2v-1.1c0-2.6 3.2-4.3 6.1-5.6l.3-.1c.2-.1.5-.1.7 0 1.2.8 2.5 1.2 4 1.2s2.8-.4 3.9-1.2c.3-.1.5-.1.7 0l.3.1c3 1.3 6.2 2.9 6.2 5.6zM12 .9c3 0 5.5 2.7 5.5 6.1S15 13.1 12 13.1 6.5 10.4 6.5 7 9 .9 12 .9z"/></symbol>
		<symbol viewBox="0 0 24 24" id="user_role"><path  d="M10.1 16.6c0-1.2.5-2.7 1.1-3.8.8-1.4 1.7-1.9 2.4-2.9 1.1-1.7 1.3-4.2.6-6-.7-1.9-2.5-3-4.5-3S6 2.2 5.3 4.1c-.7 2.1-.4 4.6 1.3 6.1.7.7 1.3 1.7 1 2.7-.4.9-1.5 1.3-2.3 1.7-1.8.8-3.9 1.9-4.3 4-.4 1.8.8 3.6 2.7 3.6h7.9c.3 0 .6-.5.3-.8-1.1-1.3-1.8-3-1.8-4.8zm7.4-5.5c-3 0-5.5 2.5-5.5 5.5s2.5 5.6 5.5 5.6 5.6-2.5 5.6-5.6-2.5-5.5-5.6-5.5zm1 6c-.1 0-.3 0-.4-.1l-2.2 2.2c-.1.1-.3.2-.4.2-.2 0-.4-.1-.5-.2-.3-.3-.3-.7 0-1l2.1-2.1c0-.1 0-.3-.1-.4-.1-1 .7-1.9 1.7-1.9.1 0 .3 0 .5.1 0 0 .1.1 0 .1l-.9 1c-.1.1-.1.2 0 .2l.6.7c.1.1.2.1.3 0l.9-.9c.1-.1.2-.1.2 0v.5c0 1-.8 1.7-1.8 1.6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="volume_high"><path d="M11.4 1.2L5.5 8.3H2.8c-1 0-1.9.8-1.9 1.9v3.6c0 1.1.9 1.9 1.9 1.9h2.7l5.9 7.1c.6.6 1.5.2 1.5-.6V1.8c0-.8-1-1.2-1.5-.6zM19.7 4c-.2-.2-.5-.2-.7 0l-.6.7c-.2.1-.2.5 0 .6 1.7 1.7 2.8 4.1 2.8 6.7 0 2.6-1.1 5-2.8 6.7-.2.2-.2.5 0 .6l.6.7c.2.2.5.2.7 0 2-2 3.4-4.9 3.4-8 0-3.1-1.3-6-3.4-8zm-2.9 3c-.2-.2-.5-.2-.7 0l-.6.6c-.2.2-.2.5 0 .7 1 .9 1.6 2.2 1.6 3.7s-.7 2.8-1.7 3.7c-.2.2-.2.5 0 .7l.7.6c.1.2.4.2.6 0 1.3-1.2 2.2-3 2.2-5s-.8-3.8-2.1-5z" /></symbol>
		<symbol viewBox="0 0 24 24" id="volume_low"><path d="M11.4 1.2L5.5 8.3H2.8c-1 0-1.9.8-1.9 1.9v3.6c0 1.1.9 1.9 1.9 1.9h2.7l5.9 7.1c.6.6 1.5.2 1.5-.6V1.8c0-.8-1-1.2-1.5-.6zM16.8 7c-.2-.2-.5-.2-.7 0l-.6.6c-.2.2-.2.5 0 .7 1 .9 1.6 2.2 1.6 3.7s-.7 2.8-1.7 3.7c-.2.2-.2.5 0 .7l.7.6c.1.2.4.2.6 0 1.3-1.2 2.2-3 2.2-5s-.8-3.8-2.1-5z" /></symbol>
		<symbol viewBox="0 0 24 24" id="volume_off"><path d="M11.4 1.2L5.5 8.3H2.8c-1 0-1.9.8-1.9 1.9v3.6c0 1.1.9 1.9 1.9 1.9h2.7l5.9 7.1c.6.6 1.5.2 1.5-.6V1.8c0-.8-1-1.2-1.5-.6zM20.7 12l2.2-2.3c.2-.1.2-.4 0-.6l-.6-.7c-.2-.1-.5-.1-.7 0l-2.2 2.3-2.3-2.3c-.2-.1-.4-.1-.6 0l-.7.7c-.2.2-.2.5 0 .6l2.3 2.3-2.3 2.3c-.2.1-.2.4 0 .6l.7.7c.2.1.4.1.6 0l2.3-2.3 2.2 2.3c.2.1.5.1.7 0l.6-.7c.2-.2.2-.5 0-.6L20.7 12z" /></symbol>
		<symbol viewBox="0 0 24 24" id="warning"><path  d="M23.7 19.6L13.2 2.5c-.6-.9-1.8-.9-2.4 0L.3 19.6c-.7 1.1 0 2.6 1.1 2.6h21.2c1.1 0 1.8-1.5 1.1-2.6zM12 18.5c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4 1.4.6 1.4 1.4-.6 1.4-1.4 1.4zm1.4-4.2c0 .3-.2.5-.5.5h-1.8c-.3 0-.5-.2-.5-.5v-6c0-.3.2-.5.5-.5h1.8c.3 0 .5.2.5.5v6z"/></symbol>
		<symbol viewBox="0 0 24 24" id="weeklyview"><path  d="M20.3 3.2H18v-.9c0-.7-.6-1.4-1.4-1.4-.7 0-1.4.6-1.4 1.4v.9H8.8v-.9c0-.7-.6-1.4-1.4-1.4C6.6.9 6 1.5 6 2.3v.9H3.7c-1 0-1.9.9-1.9 1.9v1.1c0 .*******.7h19c.3 0 .7-.3.7-.7V5.1c0-1-.9-1.9-1.9-1.9zm1.2 6h-19c-.3 0-.7.4-.7.7v11.3c0 1 .9 1.9 1.9 1.9h16.6c1 0 1.9-.9 1.9-1.9V9.9c0-.3-.4-.7-.7-.7zm-6.4 4.4l-2.9 6.2c-.1.3-.4.5-.8.5-.5 0-.9-.4-.9-.8 0-.1.1-.3.1-.4l2.5-5.3H9.6c-.5 0-.8-.2-.8-.6 0-.4.3-.7.8-.7h4.8c.4 0 .8.3.8.8 0 .1 0 .2-.1.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="wifi"><path  d="M22 7.3c-2.5-2.8-6.1-4.4-10-4.4S4.6 4.5 2.1 7.3c-.2.2-.2.5 0 .6l1.4 1.2c.2.2.5.2.7 0C6.2 6.9 9 5.7 12 5.7s5.9 1.2 7.9 3.4c.2.2.5.2.7 0L22 7.9c.2-.1.2-.4 0-.6zM12 9.4c-1.9 0-3.7.8-5 2.3-.2.2-.2.5 0 .7l1.5 1.1c.2.1.5.1.6-.1.8-.8 1.8-1.3 2.9-1.3s2.2.5 3 1.3c.1.2.4.2.6 0l1.4-1.1c.3-.2.3-.4.1-.7-1.3-1.4-3.1-2.2-5.1-2.2zm.1 6.4c1.2 0 2.3 1 2.3 2.3s-1 2.3-2.3 2.3-2.3-1-2.3-2.3 1-2.3 2.3-2.3z"/></symbol>
		<symbol viewBox="0 0 24 24" id="work_order_type"><path  d="M18.1 12.4c0 .5-.4.9-.8.9H6.7c-.4 0-.8-.4-.8-.9v-.9c0-.4.4-.8.8-.8h10.6c.5 0 .9.4.9.8v.9h-.1zm-1.7 5.3c0 .4-.4.9-.9.9H6.7c-.4 0-.8-.5-.8-.9v-.9c0-.5.4-.9.8-.9h8.9c.4 0 .8.4.8.9v.9zM5.9 6.2c0-.4.4-.8.8-.8h8.9c.4 0 .8.4.8.8v.9c0 .5-.4.9-.8.9H6.7c-.4 0-.8-.4-.8-.9v-.9zM19 1.8H5C3.5 1.8 2.3 3 2.3 4.5v14.9c0 1.5 1.2 2.7 2.6 2.7H19c1.4 0 2.6-1.2 2.6-2.7V4.5c.1-1.5-1.1-2.7-2.6-2.7z"/></symbol>
		<symbol viewBox="0 0 24 24" id="world"><path  d="M12 .9C5.9.9.9 5.9.9 12s5 11.1 11.1 11.1 11.1-5 11.1-11.1S18.1.9 12 .9zm0 2.3zm.9.1h-.1.1zM12 20.8c-4.8 0-8.8-4-8.8-8.8 0-.5.1-1 .2-1.4.6.1 1.3.3 1.7.7.8.8 1.6 1.8 2.5 2 0 0-.1 0-.2.2-.1.1-.2.4-.2.9 0 2.1 2 .8 2 3s2.5 3 2.5 1.3 1.6-2.6 1.6-3.9-1.3-1.3-2-1.8c-.9-.4-1.3-1.1-2.9-.9-.8-.7-1.2-1.4-.9-2.1.4-.8 2.1-1 2.1-2.2S8.5 6.4 7.7 6.4c-.4 0-1.2-.3-1.8-.6.7-.8 1.7-1.4 2.7-1.9.8.3 2 .9 3.1.9 1.2 0 1.9-.9 1.7-1.5 2.1.3 3.9 1.4 5.2 2.9-.6.4-1.6.9-3.2.9-2.1 0-2.1 2.1-.9 2.5 1.3.5 2.6-.8 3 0 .5.9-3 .9-2.1 3 .9 2.1 1.7 0 2.6 2.1.9 2.1 2.6-.3 1.3-2-.6-.7-.4-3 .9-3h.4c.2.7.3 1.5.3 2.3-.1 4.8-4.1 8.8-8.9 8.8z"/></symbol>
		<symbol viewBox="0 0 24 24" id="yubi_key"><g ><path d="M17.8 6.5H.7c-.4 0-.7.3-.7.7v9.7c0 .*******.7h17.1c.3 0 .7-.3.7-.7V7.2c0-.4-.4-.7-.7-.7zm-8.1 8.4c-1.6 0-2.8-1.3-2.8-2.9s1.2-2.8 2.8-2.8 2.8 1.3 2.8 2.8-1.2 2.9-2.8 2.9z"/><circle cx="9.692" cy="12.046" r=".554"/><path d="M23.3 8.3h-6.7c-.4 0-.7.3-.7.7v6c0 .*******.7h6.7c.4 0 .7-.3.7-.7V9c0-.4-.3-.7-.7-.7zm-1.6 1.9c.3 0 .5.1.5.4v.9h-3.7v-1.3h3.2zm0 3.6h-3.2v-1.2h3.7v.8c0 .3-.2.4-.5.4z"/></g></symbol>
		<symbol viewBox="0 0 24 24" id="zoomin"><path  d="M14.3 8.8h-2.8V6c0-.3-.1-.5-.4-.5H9.2c-.2 0-.4.2-.4.5v2.8H6c-.3 0-.5.2-.5.4v1.9c0 .3.2.4.5.4h2.8v2.8c0 .3.2.5.4.5h1.9c.3 0 .4-.2.4-.5v-2.8h2.8c.3 0 .5-.1.5-.4V9.2c0-.2-.2-.4-.5-.4zm8.6 12.1l-5.3-5.3c1.1-1.5 1.8-3.4 1.8-5.4 0-5.1-4.2-9.3-9.2-9.3S.9 5.1.9 10.2s4.2 9.2 9.3 9.2c2 0 3.9-.7 5.4-1.8l5.3 5.3c.3.3.7.3 1 0l.9-1c.3-.3.3-.7.1-1zm-12.7-4.3c-3.6 0-6.5-2.9-6.5-6.4s2.9-6.5 6.5-6.5 6.4 2.9 6.4 6.5-2.9 6.4-6.4 6.4z"/></symbol>
		<symbol viewBox="0 0 24 24" id="zoomout"><path  d="M8.8 11.5h5.5c.3 0 .5-.1.5-.4V9.2c0-.2-.2-.4-.5-.4H8.8m0 0H6c-.3 0-.5.2-.5.4v1.9c0 .3.2.4.5.4h2.8"/><path  d="M22.9 20.9l-5.3-5.3c1.1-1.5 1.8-3.4 1.8-5.4 0-5.1-4.2-9.3-9.2-9.3S.9 5.1.9 10.2s4.2 9.2 9.3 9.2c2 0 3.9-.7 5.4-1.8l5.3 5.3c.3.3.7.3 1 0l.9-1c.3-.3.3-.7.1-1zm-12.7-4.3c-3.6 0-6.5-2.9-6.5-6.4s2.9-6.5 6.5-6.5 6.4 2.9 6.4 6.5-2.9 6.4-6.4 6.4z"/></symbol>
</svg>

<!--
====================================================================================================
-->
		
		<header>
			<h1>SVG <code>&lt;symbol&gt;</code> sprite preview</h1>
			<p>This preview features two methods of using the generated sprite in conjunction with inline SVG. Please have a look at the HTML source for further details and be aware of the following constraints:</p>
			<ul>
				<li>Your browser has to <a href="http://caniuse.com/#feat=svg-html5" target="_blank">support inline SVG</a> for these techniques to work.</li>
				<li>The embedded sprite (A) slightly differs from the generated external one. Please <a href="https://github.com/jkphl/svg-sprite/blob/master/docs/configuration.md#defs--symbol-mode" target="_blank">see the documentation</a> for details on how to create such an embeddable sprite.</li>
				<li>Internet Explorer up to version 11 doesn't support external sprites for use with inline SVG. For IE 9-11, you may polyfill this functionality with <a href="https://github.com/jonathantneal/svg4everybody" target="_blank">SVG for Everybody</a>.</li>
			</ul>
		</header>
		<section>

<!--
	
A) Inline SVG with embedded sprite
====================================================================================================
These SVG images make use of fragment identifiers (IDs) and are extracted out of the inline sprite
embedded above. They may be styled via CSS.

-->

			<h3>A) Inline SVG with embedded sprite</h3>
			<ul>

				<li title="add">
					<div class="icon-box">
						
						<!-- add -->
						<svg class="svg-add-dims">
							<use xlink:href="#add"></use>
						</svg>
						
					</div>
					<h2>add</h2>
				</li>
				<li title="adduser">
					<div class="icon-box">
						
						<!-- adduser -->
						<svg class="svg-adduser-dims">
							<use xlink:href="#adduser"></use>
						</svg>
						
					</div>
					<h2>adduser</h2>
				</li>
				<li title="announcement">
					<div class="icon-box">
						
						<!-- announcement -->
						<svg class="svg-announcement-dims">
							<use xlink:href="#announcement"></use>
						</svg>
						
					</div>
					<h2>announcement</h2>
				</li>
				<li title="answer">
					<div class="icon-box">
						
						<!-- answer -->
						<svg class="svg-answer-dims">
							<use xlink:href="#answer"></use>
						</svg>
						
					</div>
					<h2>answer</h2>
				</li>
				<li title="apex">
					<div class="icon-box">
						
						<!-- apex -->
						<svg class="svg-apex-dims">
							<use xlink:href="#apex"></use>
						</svg>
						
					</div>
					<h2>apex</h2>
				</li>
				<li title="approval">
					<div class="icon-box">
						
						<!-- approval -->
						<svg class="svg-approval-dims">
							<use xlink:href="#approval"></use>
						</svg>
						
					</div>
					<h2>approval</h2>
				</li>
				<li title="apps">
					<div class="icon-box">
						
						<!-- apps -->
						<svg class="svg-apps-dims">
							<use xlink:href="#apps"></use>
						</svg>
						
					</div>
					<h2>apps</h2>
				</li>
				<li title="arrowdown">
					<div class="icon-box">
						
						<!-- arrowdown -->
						<svg class="svg-arrowdown-dims">
							<use xlink:href="#arrowdown"></use>
						</svg>
						
					</div>
					<h2>arrowdown</h2>
				</li>
				<li title="arrowup">
					<div class="icon-box">
						
						<!-- arrowup -->
						<svg class="svg-arrowup-dims">
							<use xlink:href="#arrowup"></use>
						</svg>
						
					</div>
					<h2>arrowup</h2>
				</li>
				<li title="attach">
					<div class="icon-box">
						
						<!-- attach -->
						<svg class="svg-attach-dims">
							<use xlink:href="#attach"></use>
						</svg>
						
					</div>
					<h2>attach</h2>
				</li>
				<li title="back">
					<div class="icon-box">
						
						<!-- back -->
						<svg class="svg-back-dims">
							<use xlink:href="#back"></use>
						</svg>
						
					</div>
					<h2>back</h2>
				</li>
				<li title="ban">
					<div class="icon-box">
						
						<!-- ban -->
						<svg class="svg-ban-dims">
							<use xlink:href="#ban"></use>
						</svg>
						
					</div>
					<h2>ban</h2>
				</li>
				<li title="bold">
					<div class="icon-box">
						
						<!-- bold -->
						<svg class="svg-bold-dims">
							<use xlink:href="#bold"></use>
						</svg>
						
					</div>
					<h2>bold</h2>
				</li>
				<li title="bookmark">
					<div class="icon-box">
						
						<!-- bookmark -->
						<svg class="svg-bookmark-dims">
							<use xlink:href="#bookmark"></use>
						</svg>
						
					</div>
					<h2>bookmark</h2>
				</li>
				<li title="breadcrumbs">
					<div class="icon-box">
						
						<!-- breadcrumbs -->
						<svg class="svg-breadcrumbs-dims">
							<use xlink:href="#breadcrumbs"></use>
						</svg>
						
					</div>
					<h2>breadcrumbs</h2>
				</li>
				<li title="broadcast">
					<div class="icon-box">
						
						<!-- broadcast -->
						<svg class="svg-broadcast-dims">
							<use xlink:href="#broadcast"></use>
						</svg>
						
					</div>
					<h2>broadcast</h2>
				</li>
				<li title="brush">
					<div class="icon-box">
						
						<!-- brush -->
						<svg class="svg-brush-dims">
							<use xlink:href="#brush"></use>
						</svg>
						
					</div>
					<h2>brush</h2>
				</li>
				<li title="bucket">
					<div class="icon-box">
						
						<!-- bucket -->
						<svg class="svg-bucket-dims">
							<use xlink:href="#bucket"></use>
						</svg>
						
					</div>
					<h2>bucket</h2>
				</li>
				<li title="builder">
					<div class="icon-box">
						
						<!-- builder -->
						<svg class="svg-builder-dims">
							<use xlink:href="#builder"></use>
						</svg>
						
					</div>
					<h2>builder</h2>
				</li>
				<li title="call">
					<div class="icon-box">
						
						<!-- call -->
						<svg class="svg-call-dims">
							<use xlink:href="#call"></use>
						</svg>
						
					</div>
					<h2>call</h2>
				</li>
				<li title="capslock">
					<div class="icon-box">
						
						<!-- capslock -->
						<svg class="svg-capslock-dims">
							<use xlink:href="#capslock"></use>
						</svg>
						
					</div>
					<h2>capslock</h2>
				</li>
				<li title="cases">
					<div class="icon-box">
						
						<!-- cases -->
						<svg class="svg-cases-dims">
							<use xlink:href="#cases"></use>
						</svg>
						
					</div>
					<h2>cases</h2>
				</li>
				<li title="center_align_text">
					<div class="icon-box">
						
						<!-- center_align_text -->
						<svg class="svg-center_align_text-dims">
							<use xlink:href="#center_align_text"></use>
						</svg>
						
					</div>
					<h2>center_align_text</h2>
				</li>
				<li title="change_owner">
					<div class="icon-box">
						
						<!-- change_owner -->
						<svg class="svg-change_owner-dims">
							<use xlink:href="#change_owner"></use>
						</svg>
						
					</div>
					<h2>change_owner</h2>
				</li>
				<li title="change_record_type">
					<div class="icon-box">
						
						<!-- change_record_type -->
						<svg class="svg-change_record_type-dims">
							<use xlink:href="#change_record_type"></use>
						</svg>
						
					</div>
					<h2>change_record_type</h2>
				</li>
				<li title="chart">
					<div class="icon-box">
						
						<!-- chart -->
						<svg class="svg-chart-dims">
							<use xlink:href="#chart"></use>
						</svg>
						
					</div>
					<h2>chart</h2>
				</li>
				<li title="chat">
					<div class="icon-box">
						
						<!-- chat -->
						<svg class="svg-chat-dims">
							<use xlink:href="#chat"></use>
						</svg>
						
					</div>
					<h2>chat</h2>
				</li>
				<li title="check">
					<div class="icon-box">
						
						<!-- check -->
						<svg class="svg-check-dims">
							<use xlink:href="#check"></use>
						</svg>
						
					</div>
					<h2>check</h2>
				</li>
				<li title="checkin">
					<div class="icon-box">
						
						<!-- checkin -->
						<svg class="svg-checkin-dims">
							<use xlink:href="#checkin"></use>
						</svg>
						
					</div>
					<h2>checkin</h2>
				</li>
				<li title="chevrondown">
					<div class="icon-box">
						
						<!-- chevrondown -->
						<svg class="svg-chevrondown-dims">
							<use xlink:href="#chevrondown"></use>
						</svg>
						
					</div>
					<h2>chevrondown</h2>
				</li>
				<li title="chevronleft">
					<div class="icon-box">
						
						<!-- chevronleft -->
						<svg class="svg-chevronleft-dims">
							<use xlink:href="#chevronleft"></use>
						</svg>
						
					</div>
					<h2>chevronleft</h2>
				</li>
				<li title="chevronright">
					<div class="icon-box">
						
						<!-- chevronright -->
						<svg class="svg-chevronright-dims">
							<use xlink:href="#chevronright"></use>
						</svg>
						
					</div>
					<h2>chevronright</h2>
				</li>
				<li title="chevronup">
					<div class="icon-box">
						
						<!-- chevronup -->
						<svg class="svg-chevronup-dims">
							<use xlink:href="#chevronup"></use>
						</svg>
						
					</div>
					<h2>chevronup</h2>
				</li>
				<li title="clear">
					<div class="icon-box">
						
						<!-- clear -->
						<svg class="svg-clear-dims">
							<use xlink:href="#clear"></use>
						</svg>
						
					</div>
					<h2>clear</h2>
				</li>
				<li title="clock">
					<div class="icon-box">
						
						<!-- clock -->
						<svg class="svg-clock-dims">
							<use xlink:href="#clock"></use>
						</svg>
						
					</div>
					<h2>clock</h2>
				</li>
				<li title="close">
					<div class="icon-box">
						
						<!-- close -->
						<svg class="svg-close-dims">
							<use xlink:href="#close"></use>
						</svg>
						
					</div>
					<h2>close</h2>
				</li>
				<li title="comments">
					<div class="icon-box">
						
						<!-- comments -->
						<svg class="svg-comments-dims">
							<use xlink:href="#comments"></use>
						</svg>
						
					</div>
					<h2>comments</h2>
				</li>
				<li title="company">
					<div class="icon-box">
						
						<!-- company -->
						<svg class="svg-company-dims">
							<use xlink:href="#company"></use>
						</svg>
						
					</div>
					<h2>company</h2>
				</li>
				<li title="connected_apps">
					<div class="icon-box">
						
						<!-- connected_apps -->
						<svg class="svg-connected_apps-dims">
							<use xlink:href="#connected_apps"></use>
						</svg>
						
					</div>
					<h2>connected_apps</h2>
				</li>
				<li title="contract">
					<div class="icon-box">
						
						<!-- contract -->
						<svg class="svg-contract-dims">
							<use xlink:href="#contract"></use>
						</svg>
						
					</div>
					<h2>contract</h2>
				</li>
				<li title="contract_alt">
					<div class="icon-box">
						
						<!-- contract_alt -->
						<svg class="svg-contract_alt-dims">
							<use xlink:href="#contract_alt"></use>
						</svg>
						
					</div>
					<h2>contract_alt</h2>
				</li>
				<li title="copy">
					<div class="icon-box">
						
						<!-- copy -->
						<svg class="svg-copy-dims">
							<use xlink:href="#copy"></use>
						</svg>
						
					</div>
					<h2>copy</h2>
				</li>
				<li title="crossfilter">
					<div class="icon-box">
						
						<!-- crossfilter -->
						<svg class="svg-crossfilter-dims">
							<use xlink:href="#crossfilter"></use>
						</svg>
						
					</div>
					<h2>crossfilter</h2>
				</li>
				<li title="custom_apps">
					<div class="icon-box">
						
						<!-- custom_apps -->
						<svg class="svg-custom_apps-dims">
							<use xlink:href="#custom_apps"></use>
						</svg>
						
					</div>
					<h2>custom_apps</h2>
				</li>
				<li title="cut">
					<div class="icon-box">
						
						<!-- cut -->
						<svg class="svg-cut-dims">
							<use xlink:href="#cut"></use>
						</svg>
						
					</div>
					<h2>cut</h2>
				</li>
				<li title="dash">
					<div class="icon-box">
						
						<!-- dash -->
						<svg class="svg-dash-dims">
							<use xlink:href="#dash"></use>
						</svg>
						
					</div>
					<h2>dash</h2>
				</li>
				<li title="database">
					<div class="icon-box">
						
						<!-- database -->
						<svg class="svg-database-dims">
							<use xlink:href="#database"></use>
						</svg>
						
					</div>
					<h2>database</h2>
				</li>
				<li title="datadotcom">
					<div class="icon-box">
						
						<!-- datadotcom -->
						<svg class="svg-datadotcom-dims">
							<use xlink:href="#datadotcom"></use>
						</svg>
						
					</div>
					<h2>datadotcom</h2>
				</li>
				<li title="dayview">
					<div class="icon-box">
						
						<!-- dayview -->
						<svg class="svg-dayview-dims">
							<use xlink:href="#dayview"></use>
						</svg>
						
					</div>
					<h2>dayview</h2>
				</li>
				<li title="delete">
					<div class="icon-box">
						
						<!-- delete -->
						<svg class="svg-delete-dims">
							<use xlink:href="#delete"></use>
						</svg>
						
					</div>
					<h2>delete</h2>
				</li>
				<li title="deprecate">
					<div class="icon-box">
						
						<!-- deprecate -->
						<svg class="svg-deprecate-dims">
							<use xlink:href="#deprecate"></use>
						</svg>
						
					</div>
					<h2>deprecate</h2>
				</li>
				<li title="description">
					<div class="icon-box">
						
						<!-- description -->
						<svg class="svg-description-dims">
							<use xlink:href="#description"></use>
						</svg>
						
					</div>
					<h2>description</h2>
				</li>
				<li title="desktop">
					<div class="icon-box">
						
						<!-- desktop -->
						<svg class="svg-desktop-dims">
							<use xlink:href="#desktop"></use>
						</svg>
						
					</div>
					<h2>desktop</h2>
				</li>
				<li title="dislike">
					<div class="icon-box">
						
						<!-- dislike -->
						<svg class="svg-dislike-dims">
							<use xlink:href="#dislike"></use>
						</svg>
						
					</div>
					<h2>dislike</h2>
				</li>
				<li title="dock_panel">
					<div class="icon-box">
						
						<!-- dock_panel -->
						<svg class="svg-dock_panel-dims">
							<use xlink:href="#dock_panel"></use>
						</svg>
						
					</div>
					<h2>dock_panel</h2>
				</li>
				<li title="down">
					<div class="icon-box">
						
						<!-- down -->
						<svg class="svg-down-dims">
							<use xlink:href="#down"></use>
						</svg>
						
					</div>
					<h2>down</h2>
				</li>
				<li title="download">
					<div class="icon-box">
						
						<!-- download -->
						<svg class="svg-download-dims">
							<use xlink:href="#download"></use>
						</svg>
						
					</div>
					<h2>download</h2>
				</li>
				<li title="edit">
					<div class="icon-box">
						
						<!-- edit -->
						<svg class="svg-edit-dims">
							<use xlink:href="#edit"></use>
						</svg>
						
					</div>
					<h2>edit</h2>
				</li>
				<li title="edit_form">
					<div class="icon-box">
						
						<!-- edit_form -->
						<svg class="svg-edit_form-dims">
							<use xlink:href="#edit_form"></use>
						</svg>
						
					</div>
					<h2>edit_form</h2>
				</li>
				<li title="email">
					<div class="icon-box">
						
						<!-- email -->
						<svg class="svg-email-dims">
							<use xlink:href="#email"></use>
						</svg>
						
					</div>
					<h2>email</h2>
				</li>
				<li title="end_call">
					<div class="icon-box">
						
						<!-- end_call -->
						<svg class="svg-end_call-dims">
							<use xlink:href="#end_call"></use>
						</svg>
						
					</div>
					<h2>end_call</h2>
				</li>
				<li title="erect_window">
					<div class="icon-box">
						
						<!-- erect_window -->
						<svg class="svg-erect_window-dims">
							<use xlink:href="#erect_window"></use>
						</svg>
						
					</div>
					<h2>erect_window</h2>
				</li>
				<li title="error">
					<div class="icon-box">
						
						<!-- error -->
						<svg class="svg-error-dims">
							<use xlink:href="#error"></use>
						</svg>
						
					</div>
					<h2>error</h2>
				</li>
				<li title="event">
					<div class="icon-box">
						
						<!-- event -->
						<svg class="svg-event-dims">
							<use xlink:href="#event"></use>
						</svg>
						
					</div>
					<h2>event</h2>
				</li>
				<li title="expand">
					<div class="icon-box">
						
						<!-- expand -->
						<svg class="svg-expand-dims">
							<use xlink:href="#expand"></use>
						</svg>
						
					</div>
					<h2>expand</h2>
				</li>
				<li title="expand_alt">
					<div class="icon-box">
						
						<!-- expand_alt -->
						<svg class="svg-expand_alt-dims">
							<use xlink:href="#expand_alt"></use>
						</svg>
						
					</div>
					<h2>expand_alt</h2>
				</li>
				<li title="fallback">
					<div class="icon-box">
						
						<!-- fallback -->
						<svg class="svg-fallback-dims">
							<use xlink:href="#fallback"></use>
						</svg>
						
					</div>
					<h2>fallback</h2>
				</li>
				<li title="favorite">
					<div class="icon-box">
						
						<!-- favorite -->
						<svg class="svg-favorite-dims">
							<use xlink:href="#favorite"></use>
						</svg>
						
					</div>
					<h2>favorite</h2>
				</li>
				<li title="feed">
					<div class="icon-box">
						
						<!-- feed -->
						<svg class="svg-feed-dims">
							<use xlink:href="#feed"></use>
						</svg>
						
					</div>
					<h2>feed</h2>
				</li>
				<li title="file">
					<div class="icon-box">
						
						<!-- file -->
						<svg class="svg-file-dims">
							<use xlink:href="#file"></use>
						</svg>
						
					</div>
					<h2>file</h2>
				</li>
				<li title="filter">
					<div class="icon-box">
						
						<!-- filter -->
						<svg class="svg-filter-dims">
							<use xlink:href="#filter"></use>
						</svg>
						
					</div>
					<h2>filter</h2>
				</li>
				<li title="filterList">
					<div class="icon-box">
						
						<!-- filterList -->
						<svg class="svg-filterList-dims">
							<use xlink:href="#filterList"></use>
						</svg>
						
					</div>
					<h2>filterList</h2>
				</li>
				<li title="flow">
					<div class="icon-box">
						
						<!-- flow -->
						<svg class="svg-flow-dims">
							<use xlink:href="#flow"></use>
						</svg>
						
					</div>
					<h2>flow</h2>
				</li>
				<li title="forward">
					<div class="icon-box">
						
						<!-- forward -->
						<svg class="svg-forward-dims">
							<use xlink:href="#forward"></use>
						</svg>
						
					</div>
					<h2>forward</h2>
				</li>
				<li title="frozen">
					<div class="icon-box">
						
						<!-- frozen -->
						<svg class="svg-frozen-dims">
							<use xlink:href="#frozen"></use>
						</svg>
						
					</div>
					<h2>frozen</h2>
				</li>
				<li title="full_width_view">
					<div class="icon-box">
						
						<!-- full_width_view -->
						<svg class="svg-full_width_view-dims">
							<use xlink:href="#full_width_view"></use>
						</svg>
						
					</div>
					<h2>full_width_view</h2>
				</li>
				<li title="groups">
					<div class="icon-box">
						
						<!-- groups -->
						<svg class="svg-groups-dims">
							<use xlink:href="#groups"></use>
						</svg>
						
					</div>
					<h2>groups</h2>
				</li>
				<li title="help">
					<div class="icon-box">
						
						<!-- help -->
						<svg class="svg-help-dims">
							<use xlink:href="#help"></use>
						</svg>
						
					</div>
					<h2>help</h2>
				</li>
				<li title="home">
					<div class="icon-box">
						
						<!-- home -->
						<svg class="svg-home-dims">
							<use xlink:href="#home"></use>
						</svg>
						
					</div>
					<h2>home</h2>
				</li>
				<li title="identity">
					<div class="icon-box">
						
						<!-- identity -->
						<svg class="svg-identity-dims">
							<use xlink:href="#identity"></use>
						</svg>
						
					</div>
					<h2>identity</h2>
				</li>
				<li title="image">
					<div class="icon-box">
						
						<!-- image -->
						<svg class="svg-image-dims">
							<use xlink:href="#image"></use>
						</svg>
						
					</div>
					<h2>image</h2>
				</li>
				<li title="inbox">
					<div class="icon-box">
						
						<!-- inbox -->
						<svg class="svg-inbox-dims">
							<use xlink:href="#inbox"></use>
						</svg>
						
					</div>
					<h2>inbox</h2>
				</li>
				<li title="info">
					<div class="icon-box">
						
						<!-- info -->
						<svg class="svg-info-dims">
							<use xlink:href="#info"></use>
						</svg>
						
					</div>
					<h2>info</h2>
				</li>
				<li title="info_alt">
					<div class="icon-box">
						
						<!-- info_alt -->
						<svg class="svg-info_alt-dims">
							<use xlink:href="#info_alt"></use>
						</svg>
						
					</div>
					<h2>info_alt</h2>
				</li>
				<li title="insert_tag_field">
					<div class="icon-box">
						
						<!-- insert_tag_field -->
						<svg class="svg-insert_tag_field-dims">
							<use xlink:href="#insert_tag_field"></use>
						</svg>
						
					</div>
					<h2>insert_tag_field</h2>
				</li>
				<li title="insert_template">
					<div class="icon-box">
						
						<!-- insert_template -->
						<svg class="svg-insert_template-dims">
							<use xlink:href="#insert_template"></use>
						</svg>
						
					</div>
					<h2>insert_template</h2>
				</li>
				<li title="italic">
					<div class="icon-box">
						
						<!-- italic -->
						<svg class="svg-italic-dims">
							<use xlink:href="#italic"></use>
						</svg>
						
					</div>
					<h2>italic</h2>
				</li>
				<li title="jump_to_bottom">
					<div class="icon-box">
						
						<!-- jump_to_bottom -->
						<svg class="svg-jump_to_bottom-dims">
							<use xlink:href="#jump_to_bottom"></use>
						</svg>
						
					</div>
					<h2>jump_to_bottom</h2>
				</li>
				<li title="jump_to_top">
					<div class="icon-box">
						
						<!-- jump_to_top -->
						<svg class="svg-jump_to_top-dims">
							<use xlink:href="#jump_to_top"></use>
						</svg>
						
					</div>
					<h2>jump_to_top</h2>
				</li>
				<li title="justify_text">
					<div class="icon-box">
						
						<!-- justify_text -->
						<svg class="svg-justify_text-dims">
							<use xlink:href="#justify_text"></use>
						</svg>
						
					</div>
					<h2>justify_text</h2>
				</li>
				<li title="kanban">
					<div class="icon-box">
						
						<!-- kanban -->
						<svg class="svg-kanban-dims">
							<use xlink:href="#kanban"></use>
						</svg>
						
					</div>
					<h2>kanban</h2>
				</li>
				<li title="keyboard_dismiss">
					<div class="icon-box">
						
						<!-- keyboard_dismiss -->
						<svg class="svg-keyboard_dismiss-dims">
							<use xlink:href="#keyboard_dismiss"></use>
						</svg>
						
					</div>
					<h2>keyboard_dismiss</h2>
				</li>
				<li title="knowledge_base">
					<div class="icon-box">
						
						<!-- knowledge_base -->
						<svg class="svg-knowledge_base-dims">
							<use xlink:href="#knowledge_base"></use>
						</svg>
						
					</div>
					<h2>knowledge_base</h2>
				</li>
				<li title="layers">
					<div class="icon-box">
						
						<!-- layers -->
						<svg class="svg-layers-dims">
							<use xlink:href="#layers"></use>
						</svg>
						
					</div>
					<h2>layers</h2>
				</li>
				<li title="layout">
					<div class="icon-box">
						
						<!-- layout -->
						<svg class="svg-layout-dims">
							<use xlink:href="#layout"></use>
						</svg>
						
					</div>
					<h2>layout</h2>
				</li>
				<li title="left">
					<div class="icon-box">
						
						<!-- left -->
						<svg class="svg-left-dims">
							<use xlink:href="#left"></use>
						</svg>
						
					</div>
					<h2>left</h2>
				</li>
				<li title="left_align_text">
					<div class="icon-box">
						
						<!-- left_align_text -->
						<svg class="svg-left_align_text-dims">
							<use xlink:href="#left_align_text"></use>
						</svg>
						
					</div>
					<h2>left_align_text</h2>
				</li>
				<li title="level_up">
					<div class="icon-box">
						
						<!-- level_up -->
						<svg class="svg-level_up-dims">
							<use xlink:href="#level_up"></use>
						</svg>
						
					</div>
					<h2>level_up</h2>
				</li>
				<li title="like">
					<div class="icon-box">
						
						<!-- like -->
						<svg class="svg-like-dims">
							<use xlink:href="#like"></use>
						</svg>
						
					</div>
					<h2>like</h2>
				</li>
				<li title="link">
					<div class="icon-box">
						
						<!-- link -->
						<svg class="svg-link-dims">
							<use xlink:href="#link"></use>
						</svg>
						
					</div>
					<h2>link</h2>
				</li>
				<li title="list">
					<div class="icon-box">
						
						<!-- list -->
						<svg class="svg-list-dims">
							<use xlink:href="#list"></use>
						</svg>
						
					</div>
					<h2>list</h2>
				</li>
				<li title="location">
					<div class="icon-box">
						
						<!-- location -->
						<svg class="svg-location-dims">
							<use xlink:href="#location"></use>
						</svg>
						
					</div>
					<h2>location</h2>
				</li>
				<li title="lock">
					<div class="icon-box">
						
						<!-- lock -->
						<svg class="svg-lock-dims">
							<use xlink:href="#lock"></use>
						</svg>
						
					</div>
					<h2>lock</h2>
				</li>
				<li title="log_a_call">
					<div class="icon-box">
						
						<!-- log_a_call -->
						<svg class="svg-log_a_call-dims">
							<use xlink:href="#log_a_call"></use>
						</svg>
						
					</div>
					<h2>log_a_call</h2>
				</li>
				<li title="logout">
					<div class="icon-box">
						
						<!-- logout -->
						<svg class="svg-logout-dims">
							<use xlink:href="#logout"></use>
						</svg>
						
					</div>
					<h2>logout</h2>
				</li>
				<li title="magicwand">
					<div class="icon-box">
						
						<!-- magicwand -->
						<svg class="svg-magicwand-dims">
							<use xlink:href="#magicwand"></use>
						</svg>
						
					</div>
					<h2>magicwand</h2>
				</li>
				<li title="mark_all_as_read">
					<div class="icon-box">
						
						<!-- mark_all_as_read -->
						<svg class="svg-mark_all_as_read-dims">
							<use xlink:href="#mark_all_as_read"></use>
						</svg>
						
					</div>
					<h2>mark_all_as_read</h2>
				</li>
				<li title="matrix">
					<div class="icon-box">
						
						<!-- matrix -->
						<svg class="svg-matrix-dims">
							<use xlink:href="#matrix"></use>
						</svg>
						
					</div>
					<h2>matrix</h2>
				</li>
				<li title="merge">
					<div class="icon-box">
						
						<!-- merge -->
						<svg class="svg-merge-dims">
							<use xlink:href="#merge"></use>
						</svg>
						
					</div>
					<h2>merge</h2>
				</li>
				<li title="metrics">
					<div class="icon-box">
						
						<!-- metrics -->
						<svg class="svg-metrics-dims">
							<use xlink:href="#metrics"></use>
						</svg>
						
					</div>
					<h2>metrics</h2>
				</li>
				<li title="minimize_window">
					<div class="icon-box">
						
						<!-- minimize_window -->
						<svg class="svg-minimize_window-dims">
							<use xlink:href="#minimize_window"></use>
						</svg>
						
					</div>
					<h2>minimize_window</h2>
				</li>
				<li title="moneybag">
					<div class="icon-box">
						
						<!-- moneybag -->
						<svg class="svg-moneybag-dims">
							<use xlink:href="#moneybag"></use>
						</svg>
						
					</div>
					<h2>moneybag</h2>
				</li>
				<li title="monthlyview">
					<div class="icon-box">
						
						<!-- monthlyview -->
						<svg class="svg-monthlyview-dims">
							<use xlink:href="#monthlyview"></use>
						</svg>
						
					</div>
					<h2>monthlyview</h2>
				</li>
				<li title="move">
					<div class="icon-box">
						
						<!-- move -->
						<svg class="svg-move-dims">
							<use xlink:href="#move"></use>
						</svg>
						
					</div>
					<h2>move</h2>
				</li>
				<li title="muted">
					<div class="icon-box">
						
						<!-- muted -->
						<svg class="svg-muted-dims">
							<use xlink:href="#muted"></use>
						</svg>
						
					</div>
					<h2>muted</h2>
				</li>
				<li title="new">
					<div class="icon-box">
						
						<!-- new -->
						<svg class="svg-new-dims">
							<use xlink:href="#new"></use>
						</svg>
						
					</div>
					<h2>new</h2>
				</li>
				<li title="new_window">
					<div class="icon-box">
						
						<!-- new_window -->
						<svg class="svg-new_window-dims">
							<use xlink:href="#new_window"></use>
						</svg>
						
					</div>
					<h2>new_window</h2>
				</li>
				<li title="news">
					<div class="icon-box">
						
						<!-- news -->
						<svg class="svg-news-dims">
							<use xlink:href="#news"></use>
						</svg>
						
					</div>
					<h2>news</h2>
				</li>
				<li title="note">
					<div class="icon-box">
						
						<!-- note -->
						<svg class="svg-note-dims">
							<use xlink:href="#note"></use>
						</svg>
						
					</div>
					<h2>note</h2>
				</li>
				<li title="notebook">
					<div class="icon-box">
						
						<!-- notebook -->
						<svg class="svg-notebook-dims">
							<use xlink:href="#notebook"></use>
						</svg>
						
					</div>
					<h2>notebook</h2>
				</li>
				<li title="notification">
					<div class="icon-box">
						
						<!-- notification -->
						<svg class="svg-notification-dims">
							<use xlink:href="#notification"></use>
						</svg>
						
					</div>
					<h2>notification</h2>
				</li>
				<li title="office365">
					<div class="icon-box">
						
						<!-- office365 -->
						<svg class="svg-office365-dims">
							<use xlink:href="#office365"></use>
						</svg>
						
					</div>
					<h2>office365</h2>
				</li>
				<li title="offline">
					<div class="icon-box">
						
						<!-- offline -->
						<svg class="svg-offline-dims">
							<use xlink:href="#offline"></use>
						</svg>
						
					</div>
					<h2>offline</h2>
				</li>
				<li title="open">
					<div class="icon-box">
						
						<!-- open -->
						<svg class="svg-open-dims">
							<use xlink:href="#open"></use>
						</svg>
						
					</div>
					<h2>open</h2>
				</li>
				<li title="open_folder">
					<div class="icon-box">
						
						<!-- open_folder -->
						<svg class="svg-open_folder-dims">
							<use xlink:href="#open_folder"></use>
						</svg>
						
					</div>
					<h2>open_folder</h2>
				</li>
				<li title="opened_folder">
					<div class="icon-box">
						
						<!-- opened_folder -->
						<svg class="svg-opened_folder-dims">
							<use xlink:href="#opened_folder"></use>
						</svg>
						
					</div>
					<h2>opened_folder</h2>
				</li>
				<li title="overflow">
					<div class="icon-box">
						
						<!-- overflow -->
						<svg class="svg-overflow-dims">
							<use xlink:href="#overflow"></use>
						</svg>
						
					</div>
					<h2>overflow</h2>
				</li>
				<li title="package">
					<div class="icon-box">
						
						<!-- package -->
						<svg class="svg-package-dims">
							<use xlink:href="#package"></use>
						</svg>
						
					</div>
					<h2>package</h2>
				</li>
				<li title="package_org">
					<div class="icon-box">
						
						<!-- package_org -->
						<svg class="svg-package_org-dims">
							<use xlink:href="#package_org"></use>
						</svg>
						
					</div>
					<h2>package_org</h2>
				</li>
				<li title="package_org_beta">
					<div class="icon-box">
						
						<!-- package_org_beta -->
						<svg class="svg-package_org_beta-dims">
							<use xlink:href="#package_org_beta"></use>
						</svg>
						
					</div>
					<h2>package_org_beta</h2>
				</li>
				<li title="page">
					<div class="icon-box">
						
						<!-- page -->
						<svg class="svg-page-dims">
							<use xlink:href="#page"></use>
						</svg>
						
					</div>
					<h2>page</h2>
				</li>
				<li title="palette">
					<div class="icon-box">
						
						<!-- palette -->
						<svg class="svg-palette-dims">
							<use xlink:href="#palette"></use>
						</svg>
						
					</div>
					<h2>palette</h2>
				</li>
				<li title="paste">
					<div class="icon-box">
						
						<!-- paste -->
						<svg class="svg-paste-dims">
							<use xlink:href="#paste"></use>
						</svg>
						
					</div>
					<h2>paste</h2>
				</li>
				<li title="people">
					<div class="icon-box">
						
						<!-- people -->
						<svg class="svg-people-dims">
							<use xlink:href="#people"></use>
						</svg>
						
					</div>
					<h2>people</h2>
				</li>
				<li title="phone_landscape">
					<div class="icon-box">
						
						<!-- phone_landscape -->
						<svg class="svg-phone_landscape-dims">
							<use xlink:href="#phone_landscape"></use>
						</svg>
						
					</div>
					<h2>phone_landscape</h2>
				</li>
				<li title="phone_portrait">
					<div class="icon-box">
						
						<!-- phone_portrait -->
						<svg class="svg-phone_portrait-dims">
							<use xlink:href="#phone_portrait"></use>
						</svg>
						
					</div>
					<h2>phone_portrait</h2>
				</li>
				<li title="photo">
					<div class="icon-box">
						
						<!-- photo -->
						<svg class="svg-photo-dims">
							<use xlink:href="#photo"></use>
						</svg>
						
					</div>
					<h2>photo</h2>
				</li>
				<li title="picklist">
					<div class="icon-box">
						
						<!-- picklist -->
						<svg class="svg-picklist-dims">
							<use xlink:href="#picklist"></use>
						</svg>
						
					</div>
					<h2>picklist</h2>
				</li>
				<li title="power">
					<div class="icon-box">
						
						<!-- power -->
						<svg class="svg-power-dims">
							<use xlink:href="#power"></use>
						</svg>
						
					</div>
					<h2>power</h2>
				</li>
				<li title="preview">
					<div class="icon-box">
						
						<!-- preview -->
						<svg class="svg-preview-dims">
							<use xlink:href="#preview"></use>
						</svg>
						
					</div>
					<h2>preview</h2>
				</li>
				<li title="priority">
					<div class="icon-box">
						
						<!-- priority -->
						<svg class="svg-priority-dims">
							<use xlink:href="#priority"></use>
						</svg>
						
					</div>
					<h2>priority</h2>
				</li>
				<li title="process">
					<div class="icon-box">
						
						<!-- process -->
						<svg class="svg-process-dims">
							<use xlink:href="#process"></use>
						</svg>
						
					</div>
					<h2>process</h2>
				</li>
				<li title="push">
					<div class="icon-box">
						
						<!-- push -->
						<svg class="svg-push-dims">
							<use xlink:href="#push"></use>
						</svg>
						
					</div>
					<h2>push</h2>
				</li>
				<li title="puzzle">
					<div class="icon-box">
						
						<!-- puzzle -->
						<svg class="svg-puzzle-dims">
							<use xlink:href="#puzzle"></use>
						</svg>
						
					</div>
					<h2>puzzle</h2>
				</li>
				<li title="question">
					<div class="icon-box">
						
						<!-- question -->
						<svg class="svg-question-dims">
							<use xlink:href="#question"></use>
						</svg>
						
					</div>
					<h2>question</h2>
				</li>
				<li title="questions_and_answers">
					<div class="icon-box">
						
						<!-- questions_and_answers -->
						<svg class="svg-questions_and_answers-dims">
							<use xlink:href="#questions_and_answers"></use>
						</svg>
						
					</div>
					<h2>questions_and_answers</h2>
				</li>
				<li title="record">
					<div class="icon-box">
						
						<!-- record -->
						<svg class="svg-record-dims">
							<use xlink:href="#record"></use>
						</svg>
						
					</div>
					<h2>record</h2>
				</li>
				<li title="record_create">
					<div class="icon-box">
						
						<!-- record_create -->
						<svg class="svg-record_create-dims">
							<use xlink:href="#record_create"></use>
						</svg>
						
					</div>
					<h2>record_create</h2>
				</li>
				<li title="redo">
					<div class="icon-box">
						
						<!-- redo -->
						<svg class="svg-redo-dims">
							<use xlink:href="#redo"></use>
						</svg>
						
					</div>
					<h2>redo</h2>
				</li>
				<li title="refresh">
					<div class="icon-box">
						
						<!-- refresh -->
						<svg class="svg-refresh-dims">
							<use xlink:href="#refresh"></use>
						</svg>
						
					</div>
					<h2>refresh</h2>
				</li>
				<li title="relate">
					<div class="icon-box">
						
						<!-- relate -->
						<svg class="svg-relate-dims">
							<use xlink:href="#relate"></use>
						</svg>
						
					</div>
					<h2>relate</h2>
				</li>
				<li title="remove_formatting">
					<div class="icon-box">
						
						<!-- remove_formatting -->
						<svg class="svg-remove_formatting-dims">
							<use xlink:href="#remove_formatting"></use>
						</svg>
						
					</div>
					<h2>remove_formatting</h2>
				</li>
				<li title="remove_link">
					<div class="icon-box">
						
						<!-- remove_link -->
						<svg class="svg-remove_link-dims">
							<use xlink:href="#remove_link"></use>
						</svg>
						
					</div>
					<h2>remove_link</h2>
				</li>
				<li title="replace">
					<div class="icon-box">
						
						<!-- replace -->
						<svg class="svg-replace-dims">
							<use xlink:href="#replace"></use>
						</svg>
						
					</div>
					<h2>replace</h2>
				</li>
				<li title="reply">
					<div class="icon-box">
						
						<!-- reply -->
						<svg class="svg-reply-dims">
							<use xlink:href="#reply"></use>
						</svg>
						
					</div>
					<h2>reply</h2>
				</li>
				<li title="reply_all">
					<div class="icon-box">
						
						<!-- reply_all -->
						<svg class="svg-reply_all-dims">
							<use xlink:href="#reply_all"></use>
						</svg>
						
					</div>
					<h2>reply_all</h2>
				</li>
				<li title="reset_password">
					<div class="icon-box">
						
						<!-- reset_password -->
						<svg class="svg-reset_password-dims">
							<use xlink:href="#reset_password"></use>
						</svg>
						
					</div>
					<h2>reset_password</h2>
				</li>
				<li title="resource_absence">
					<div class="icon-box">
						
						<!-- resource_absence -->
						<svg class="svg-resource_absence-dims">
							<use xlink:href="#resource_absence"></use>
						</svg>
						
					</div>
					<h2>resource_absence</h2>
				</li>
				<li title="resource_capacity">
					<div class="icon-box">
						
						<!-- resource_capacity -->
						<svg class="svg-resource_capacity-dims">
							<use xlink:href="#resource_capacity"></use>
						</svg>
						
					</div>
					<h2>resource_capacity</h2>
				</li>
				<li title="resource_territory">
					<div class="icon-box">
						
						<!-- resource_territory -->
						<svg class="svg-resource_territory-dims">
							<use xlink:href="#resource_territory"></use>
						</svg>
						
					</div>
					<h2>resource_territory</h2>
				</li>
				<li title="retweet">
					<div class="icon-box">
						
						<!-- retweet -->
						<svg class="svg-retweet-dims">
							<use xlink:href="#retweet"></use>
						</svg>
						
					</div>
					<h2>retweet</h2>
				</li>
				<li title="richtextbulletedlist">
					<div class="icon-box">
						
						<!-- richtextbulletedlist -->
						<svg class="svg-richtextbulletedlist-dims">
							<use xlink:href="#richtextbulletedlist"></use>
						</svg>
						
					</div>
					<h2>richtextbulletedlist</h2>
				</li>
				<li title="richtextindent">
					<div class="icon-box">
						
						<!-- richtextindent -->
						<svg class="svg-richtextindent-dims">
							<use xlink:href="#richtextindent"></use>
						</svg>
						
					</div>
					<h2>richtextindent</h2>
				</li>
				<li title="richtextnumberedlist">
					<div class="icon-box">
						
						<!-- richtextnumberedlist -->
						<svg class="svg-richtextnumberedlist-dims">
							<use xlink:href="#richtextnumberedlist"></use>
						</svg>
						
					</div>
					<h2>richtextnumberedlist</h2>
				</li>
				<li title="richtextoutdent">
					<div class="icon-box">
						
						<!-- richtextoutdent -->
						<svg class="svg-richtextoutdent-dims">
							<use xlink:href="#richtextoutdent"></use>
						</svg>
						
					</div>
					<h2>richtextoutdent</h2>
				</li>
				<li title="right">
					<div class="icon-box">
						
						<!-- right -->
						<svg class="svg-right-dims">
							<use xlink:href="#right"></use>
						</svg>
						
					</div>
					<h2>right</h2>
				</li>
				<li title="right_align_text">
					<div class="icon-box">
						
						<!-- right_align_text -->
						<svg class="svg-right_align_text-dims">
							<use xlink:href="#right_align_text"></use>
						</svg>
						
					</div>
					<h2>right_align_text</h2>
				</li>
				<li title="rotate">
					<div class="icon-box">
						
						<!-- rotate -->
						<svg class="svg-rotate-dims">
							<use xlink:href="#rotate"></use>
						</svg>
						
					</div>
					<h2>rotate</h2>
				</li>
				<li title="rows">
					<div class="icon-box">
						
						<!-- rows -->
						<svg class="svg-rows-dims">
							<use xlink:href="#rows"></use>
						</svg>
						
					</div>
					<h2>rows</h2>
				</li>
				<li title="salesforce1">
					<div class="icon-box">
						
						<!-- salesforce1 -->
						<svg class="svg-salesforce1-dims">
							<use xlink:href="#salesforce1"></use>
						</svg>
						
					</div>
					<h2>salesforce1</h2>
				</li>
				<li title="search">
					<div class="icon-box">
						
						<!-- search -->
						<svg class="svg-search-dims">
							<use xlink:href="#search"></use>
						</svg>
						
					</div>
					<h2>search</h2>
				</li>
				<li title="settings">
					<div class="icon-box">
						
						<!-- settings -->
						<svg class="svg-settings-dims">
							<use xlink:href="#settings"></use>
						</svg>
						
					</div>
					<h2>settings</h2>
				</li>
				<li title="setup">
					<div class="icon-box">
						
						<!-- setup -->
						<svg class="svg-setup-dims">
							<use xlink:href="#setup"></use>
						</svg>
						
					</div>
					<h2>setup</h2>
				</li>
				<li title="setup_assistant_guide">
					<div class="icon-box">
						
						<!-- setup_assistant_guide -->
						<svg class="svg-setup_assistant_guide-dims">
							<use xlink:href="#setup_assistant_guide"></use>
						</svg>
						
					</div>
					<h2>setup_assistant_guide</h2>
				</li>
				<li title="share">
					<div class="icon-box">
						
						<!-- share -->
						<svg class="svg-share-dims">
							<use xlink:href="#share"></use>
						</svg>
						
					</div>
					<h2>share</h2>
				</li>
				<li title="share_mobile">
					<div class="icon-box">
						
						<!-- share_mobile -->
						<svg class="svg-share_mobile-dims">
							<use xlink:href="#share_mobile"></use>
						</svg>
						
					</div>
					<h2>share_mobile</h2>
				</li>
				<li title="share_post">
					<div class="icon-box">
						
						<!-- share_post -->
						<svg class="svg-share_post-dims">
							<use xlink:href="#share_post"></use>
						</svg>
						
					</div>
					<h2>share_post</h2>
				</li>
				<li title="shield">
					<div class="icon-box">
						
						<!-- shield -->
						<svg class="svg-shield-dims">
							<use xlink:href="#shield"></use>
						</svg>
						
					</div>
					<h2>shield</h2>
				</li>
				<li title="side_list">
					<div class="icon-box">
						
						<!-- side_list -->
						<svg class="svg-side_list-dims">
							<use xlink:href="#side_list"></use>
						</svg>
						
					</div>
					<h2>side_list</h2>
				</li>
				<li title="signpost">
					<div class="icon-box">
						
						<!-- signpost -->
						<svg class="svg-signpost-dims">
							<use xlink:href="#signpost"></use>
						</svg>
						
					</div>
					<h2>signpost</h2>
				</li>
				<li title="sms">
					<div class="icon-box">
						
						<!-- sms -->
						<svg class="svg-sms-dims">
							<use xlink:href="#sms"></use>
						</svg>
						
					</div>
					<h2>sms</h2>
				</li>
				<li title="snippet">
					<div class="icon-box">
						
						<!-- snippet -->
						<svg class="svg-snippet-dims">
							<use xlink:href="#snippet"></use>
						</svg>
						
					</div>
					<h2>snippet</h2>
				</li>
				<li title="socialshare">
					<div class="icon-box">
						
						<!-- socialshare -->
						<svg class="svg-socialshare-dims">
							<use xlink:href="#socialshare"></use>
						</svg>
						
					</div>
					<h2>socialshare</h2>
				</li>
				<li title="sort">
					<div class="icon-box">
						
						<!-- sort -->
						<svg class="svg-sort-dims">
							<use xlink:href="#sort"></use>
						</svg>
						
					</div>
					<h2>sort</h2>
				</li>
				<li title="spinner">
					<div class="icon-box">
						
						<!-- spinner -->
						<svg class="svg-spinner-dims">
							<use xlink:href="#spinner"></use>
						</svg>
						
					</div>
					<h2>spinner</h2>
				</li>
				<li title="standard_objects">
					<div class="icon-box">
						
						<!-- standard_objects -->
						<svg class="svg-standard_objects-dims">
							<use xlink:href="#standard_objects"></use>
						</svg>
						
					</div>
					<h2>standard_objects</h2>
				</li>
				<li title="stop">
					<div class="icon-box">
						
						<!-- stop -->
						<svg class="svg-stop-dims">
							<use xlink:href="#stop"></use>
						</svg>
						
					</div>
					<h2>stop</h2>
				</li>
				<li title="strikethrough">
					<div class="icon-box">
						
						<!-- strikethrough -->
						<svg class="svg-strikethrough-dims">
							<use xlink:href="#strikethrough"></use>
						</svg>
						
					</div>
					<h2>strikethrough</h2>
				</li>
				<li title="success">
					<div class="icon-box">
						
						<!-- success -->
						<svg class="svg-success-dims">
							<use xlink:href="#success"></use>
						</svg>
						
					</div>
					<h2>success</h2>
				</li>
				<li title="summary">
					<div class="icon-box">
						
						<!-- summary -->
						<svg class="svg-summary-dims">
							<use xlink:href="#summary"></use>
						</svg>
						
					</div>
					<h2>summary</h2>
				</li>
				<li title="summarydetail">
					<div class="icon-box">
						
						<!-- summarydetail -->
						<svg class="svg-summarydetail-dims">
							<use xlink:href="#summarydetail"></use>
						</svg>
						
					</div>
					<h2>summarydetail</h2>
				</li>
				<li title="switch">
					<div class="icon-box">
						
						<!-- switch -->
						<svg class="svg-switch-dims">
							<use xlink:href="#switch"></use>
						</svg>
						
					</div>
					<h2>switch</h2>
				</li>
				<li title="sync">
					<div class="icon-box">
						
						<!-- sync -->
						<svg class="svg-sync-dims">
							<use xlink:href="#sync"></use>
						</svg>
						
					</div>
					<h2>sync</h2>
				</li>
				<li title="table">
					<div class="icon-box">
						
						<!-- table -->
						<svg class="svg-table-dims">
							<use xlink:href="#table"></use>
						</svg>
						
					</div>
					<h2>table</h2>
				</li>
				<li title="tablet_landscape">
					<div class="icon-box">
						
						<!-- tablet_landscape -->
						<svg class="svg-tablet_landscape-dims">
							<use xlink:href="#tablet_landscape"></use>
						</svg>
						
					</div>
					<h2>tablet_landscape</h2>
				</li>
				<li title="tablet_portrait">
					<div class="icon-box">
						
						<!-- tablet_portrait -->
						<svg class="svg-tablet_portrait-dims">
							<use xlink:href="#tablet_portrait"></use>
						</svg>
						
					</div>
					<h2>tablet_portrait</h2>
				</li>
				<li title="tabset">
					<div class="icon-box">
						
						<!-- tabset -->
						<svg class="svg-tabset-dims">
							<use xlink:href="#tabset"></use>
						</svg>
						
					</div>
					<h2>tabset</h2>
				</li>
				<li title="task">
					<div class="icon-box">
						
						<!-- task -->
						<svg class="svg-task-dims">
							<use xlink:href="#task"></use>
						</svg>
						
					</div>
					<h2>task</h2>
				</li>
				<li title="text_background_color">
					<div class="icon-box">
						
						<!-- text_background_color -->
						<svg class="svg-text_background_color-dims">
							<use xlink:href="#text_background_color"></use>
						</svg>
						
					</div>
					<h2>text_background_color</h2>
				</li>
				<li title="text_color">
					<div class="icon-box">
						
						<!-- text_color -->
						<svg class="svg-text_color-dims">
							<use xlink:href="#text_color"></use>
						</svg>
						
					</div>
					<h2>text_color</h2>
				</li>
				<li title="threedots">
					<div class="icon-box">
						
						<!-- threedots -->
						<svg class="svg-threedots-dims">
							<use xlink:href="#threedots"></use>
						</svg>
						
					</div>
					<h2>threedots</h2>
				</li>
				<li title="threedots_vertical">
					<div class="icon-box">
						
						<!-- threedots_vertical -->
						<svg class="svg-threedots_vertical-dims">
							<use xlink:href="#threedots_vertical"></use>
						</svg>
						
					</div>
					<h2>threedots_vertical</h2>
				</li>
				<li title="thunder">
					<div class="icon-box">
						
						<!-- thunder -->
						<svg class="svg-thunder-dims">
							<use xlink:href="#thunder"></use>
						</svg>
						
					</div>
					<h2>thunder</h2>
				</li>
				<li title="tile_card_list">
					<div class="icon-box">
						
						<!-- tile_card_list -->
						<svg class="svg-tile_card_list-dims">
							<use xlink:href="#tile_card_list"></use>
						</svg>
						
					</div>
					<h2>tile_card_list</h2>
				</li>
				<li title="topic">
					<div class="icon-box">
						
						<!-- topic -->
						<svg class="svg-topic-dims">
							<use xlink:href="#topic"></use>
						</svg>
						
					</div>
					<h2>topic</h2>
				</li>
				<li title="touch_action">
					<div class="icon-box">
						
						<!-- touch_action -->
						<svg class="svg-touch_action-dims">
							<use xlink:href="#touch_action"></use>
						</svg>
						
					</div>
					<h2>touch_action</h2>
				</li>
				<li title="trail">
					<div class="icon-box">
						
						<!-- trail -->
						<svg class="svg-trail-dims">
							<use xlink:href="#trail"></use>
						</svg>
						
					</div>
					<h2>trail</h2>
				</li>
				<li title="turn_off_notifications">
					<div class="icon-box">
						
						<!-- turn_off_notifications -->
						<svg class="svg-turn_off_notifications-dims">
							<use xlink:href="#turn_off_notifications"></use>
						</svg>
						
					</div>
					<h2>turn_off_notifications</h2>
				</li>
				<li title="undelete">
					<div class="icon-box">
						
						<!-- undelete -->
						<svg class="svg-undelete-dims">
							<use xlink:href="#undelete"></use>
						</svg>
						
					</div>
					<h2>undelete</h2>
				</li>
				<li title="undeprecate">
					<div class="icon-box">
						
						<!-- undeprecate -->
						<svg class="svg-undeprecate-dims">
							<use xlink:href="#undeprecate"></use>
						</svg>
						
					</div>
					<h2>undeprecate</h2>
				</li>
				<li title="underline">
					<div class="icon-box">
						
						<!-- underline -->
						<svg class="svg-underline-dims">
							<use xlink:href="#underline"></use>
						</svg>
						
					</div>
					<h2>underline</h2>
				</li>
				<li title="undo">
					<div class="icon-box">
						
						<!-- undo -->
						<svg class="svg-undo-dims">
							<use xlink:href="#undo"></use>
						</svg>
						
					</div>
					<h2>undo</h2>
				</li>
				<li title="unlock">
					<div class="icon-box">
						
						<!-- unlock -->
						<svg class="svg-unlock-dims">
							<use xlink:href="#unlock"></use>
						</svg>
						
					</div>
					<h2>unlock</h2>
				</li>
				<li title="unmuted">
					<div class="icon-box">
						
						<!-- unmuted -->
						<svg class="svg-unmuted-dims">
							<use xlink:href="#unmuted"></use>
						</svg>
						
					</div>
					<h2>unmuted</h2>
				</li>
				<li title="up">
					<div class="icon-box">
						
						<!-- up -->
						<svg class="svg-up-dims">
							<use xlink:href="#up"></use>
						</svg>
						
					</div>
					<h2>up</h2>
				</li>
				<li title="upload">
					<div class="icon-box">
						
						<!-- upload -->
						<svg class="svg-upload-dims">
							<use xlink:href="#upload"></use>
						</svg>
						
					</div>
					<h2>upload</h2>
				</li>
				<li title="user">
					<div class="icon-box">
						
						<!-- user -->
						<svg class="svg-user-dims">
							<use xlink:href="#user"></use>
						</svg>
						
					</div>
					<h2>user</h2>
				</li>
				<li title="user_role">
					<div class="icon-box">
						
						<!-- user_role -->
						<svg class="svg-user_role-dims">
							<use xlink:href="#user_role"></use>
						</svg>
						
					</div>
					<h2>user_role</h2>
				</li>
				<li title="volume_high">
					<div class="icon-box">
						
						<!-- volume_high -->
						<svg class="svg-volume_high-dims">
							<use xlink:href="#volume_high"></use>
						</svg>
						
					</div>
					<h2>volume_high</h2>
				</li>
				<li title="volume_low">
					<div class="icon-box">
						
						<!-- volume_low -->
						<svg class="svg-volume_low-dims">
							<use xlink:href="#volume_low"></use>
						</svg>
						
					</div>
					<h2>volume_low</h2>
				</li>
				<li title="volume_off">
					<div class="icon-box">
						
						<!-- volume_off -->
						<svg class="svg-volume_off-dims">
							<use xlink:href="#volume_off"></use>
						</svg>
						
					</div>
					<h2>volume_off</h2>
				</li>
				<li title="warning">
					<div class="icon-box">
						
						<!-- warning -->
						<svg class="svg-warning-dims">
							<use xlink:href="#warning"></use>
						</svg>
						
					</div>
					<h2>warning</h2>
				</li>
				<li title="weeklyview">
					<div class="icon-box">
						
						<!-- weeklyview -->
						<svg class="svg-weeklyview-dims">
							<use xlink:href="#weeklyview"></use>
						</svg>
						
					</div>
					<h2>weeklyview</h2>
				</li>
				<li title="wifi">
					<div class="icon-box">
						
						<!-- wifi -->
						<svg class="svg-wifi-dims">
							<use xlink:href="#wifi"></use>
						</svg>
						
					</div>
					<h2>wifi</h2>
				</li>
				<li title="work_order_type">
					<div class="icon-box">
						
						<!-- work_order_type -->
						<svg class="svg-work_order_type-dims">
							<use xlink:href="#work_order_type"></use>
						</svg>
						
					</div>
					<h2>work_order_type</h2>
				</li>
				<li title="world">
					<div class="icon-box">
						
						<!-- world -->
						<svg class="svg-world-dims">
							<use xlink:href="#world"></use>
						</svg>
						
					</div>
					<h2>world</h2>
				</li>
				<li title="yubi_key">
					<div class="icon-box">
						
						<!-- yubi_key -->
						<svg class="svg-yubi_key-dims">
							<use xlink:href="#yubi_key"></use>
						</svg>
						
					</div>
					<h2>yubi_key</h2>
				</li>
				<li title="zoomin">
					<div class="icon-box">
						
						<!-- zoomin -->
						<svg class="svg-zoomin-dims">
							<use xlink:href="#zoomin"></use>
						</svg>
						
					</div>
					<h2>zoomin</h2>
				</li>
				<li title="zoomout">
					<div class="icon-box">
						
						<!-- zoomout -->
						<svg class="svg-zoomout-dims">
							<use xlink:href="#zoomout"></use>
						</svg>
						
					</div>
					<h2>zoomout</h2>
				</li>
			</ul>

<!--
====================================================================================================
-->

		</section>
		<section>

<!--
	
B) Inline SVG with external sprite (IE 9-11 with polyfill only)
====================================================================================================
These SVG images make use of an URL + fragment identifiers (IDs) and refer to the regular external
SVG sprite. They may be styled via CSS. (IE 9-11 with polyfill only)

-->

			<h3>B) Inline SVG with external sprite (IE 9-11 with polyfill only)</h3>
			<ul>
				
				<li title="add">
					<div class="icon-box">
						
						<!-- add -->
						<svg class="svg-add-dims">
							<use xlink:href="svg/symbols.svg#add"></use>
						</svg>
						
					</div>
					<h2>add</h2>
				</li>
				<li title="adduser">
					<div class="icon-box">
						
						<!-- adduser -->
						<svg class="svg-adduser-dims">
							<use xlink:href="svg/symbols.svg#adduser"></use>
						</svg>
						
					</div>
					<h2>adduser</h2>
				</li>
				<li title="announcement">
					<div class="icon-box">
						
						<!-- announcement -->
						<svg class="svg-announcement-dims">
							<use xlink:href="svg/symbols.svg#announcement"></use>
						</svg>
						
					</div>
					<h2>announcement</h2>
				</li>
				<li title="answer">
					<div class="icon-box">
						
						<!-- answer -->
						<svg class="svg-answer-dims">
							<use xlink:href="svg/symbols.svg#answer"></use>
						</svg>
						
					</div>
					<h2>answer</h2>
				</li>
				<li title="apex">
					<div class="icon-box">
						
						<!-- apex -->
						<svg class="svg-apex-dims">
							<use xlink:href="svg/symbols.svg#apex"></use>
						</svg>
						
					</div>
					<h2>apex</h2>
				</li>
				<li title="approval">
					<div class="icon-box">
						
						<!-- approval -->
						<svg class="svg-approval-dims">
							<use xlink:href="svg/symbols.svg#approval"></use>
						</svg>
						
					</div>
					<h2>approval</h2>
				</li>
				<li title="apps">
					<div class="icon-box">
						
						<!-- apps -->
						<svg class="svg-apps-dims">
							<use xlink:href="svg/symbols.svg#apps"></use>
						</svg>
						
					</div>
					<h2>apps</h2>
				</li>
				<li title="arrowdown">
					<div class="icon-box">
						
						<!-- arrowdown -->
						<svg class="svg-arrowdown-dims">
							<use xlink:href="svg/symbols.svg#arrowdown"></use>
						</svg>
						
					</div>
					<h2>arrowdown</h2>
				</li>
				<li title="arrowup">
					<div class="icon-box">
						
						<!-- arrowup -->
						<svg class="svg-arrowup-dims">
							<use xlink:href="svg/symbols.svg#arrowup"></use>
						</svg>
						
					</div>
					<h2>arrowup</h2>
				</li>
				<li title="attach">
					<div class="icon-box">
						
						<!-- attach -->
						<svg class="svg-attach-dims">
							<use xlink:href="svg/symbols.svg#attach"></use>
						</svg>
						
					</div>
					<h2>attach</h2>
				</li>
				<li title="back">
					<div class="icon-box">
						
						<!-- back -->
						<svg class="svg-back-dims">
							<use xlink:href="svg/symbols.svg#back"></use>
						</svg>
						
					</div>
					<h2>back</h2>
				</li>
				<li title="ban">
					<div class="icon-box">
						
						<!-- ban -->
						<svg class="svg-ban-dims">
							<use xlink:href="svg/symbols.svg#ban"></use>
						</svg>
						
					</div>
					<h2>ban</h2>
				</li>
				<li title="bold">
					<div class="icon-box">
						
						<!-- bold -->
						<svg class="svg-bold-dims">
							<use xlink:href="svg/symbols.svg#bold"></use>
						</svg>
						
					</div>
					<h2>bold</h2>
				</li>
				<li title="bookmark">
					<div class="icon-box">
						
						<!-- bookmark -->
						<svg class="svg-bookmark-dims">
							<use xlink:href="svg/symbols.svg#bookmark"></use>
						</svg>
						
					</div>
					<h2>bookmark</h2>
				</li>
				<li title="breadcrumbs">
					<div class="icon-box">
						
						<!-- breadcrumbs -->
						<svg class="svg-breadcrumbs-dims">
							<use xlink:href="svg/symbols.svg#breadcrumbs"></use>
						</svg>
						
					</div>
					<h2>breadcrumbs</h2>
				</li>
				<li title="broadcast">
					<div class="icon-box">
						
						<!-- broadcast -->
						<svg class="svg-broadcast-dims">
							<use xlink:href="svg/symbols.svg#broadcast"></use>
						</svg>
						
					</div>
					<h2>broadcast</h2>
				</li>
				<li title="brush">
					<div class="icon-box">
						
						<!-- brush -->
						<svg class="svg-brush-dims">
							<use xlink:href="svg/symbols.svg#brush"></use>
						</svg>
						
					</div>
					<h2>brush</h2>
				</li>
				<li title="bucket">
					<div class="icon-box">
						
						<!-- bucket -->
						<svg class="svg-bucket-dims">
							<use xlink:href="svg/symbols.svg#bucket"></use>
						</svg>
						
					</div>
					<h2>bucket</h2>
				</li>
				<li title="builder">
					<div class="icon-box">
						
						<!-- builder -->
						<svg class="svg-builder-dims">
							<use xlink:href="svg/symbols.svg#builder"></use>
						</svg>
						
					</div>
					<h2>builder</h2>
				</li>
				<li title="call">
					<div class="icon-box">
						
						<!-- call -->
						<svg class="svg-call-dims">
							<use xlink:href="svg/symbols.svg#call"></use>
						</svg>
						
					</div>
					<h2>call</h2>
				</li>
				<li title="capslock">
					<div class="icon-box">
						
						<!-- capslock -->
						<svg class="svg-capslock-dims">
							<use xlink:href="svg/symbols.svg#capslock"></use>
						</svg>
						
					</div>
					<h2>capslock</h2>
				</li>
				<li title="cases">
					<div class="icon-box">
						
						<!-- cases -->
						<svg class="svg-cases-dims">
							<use xlink:href="svg/symbols.svg#cases"></use>
						</svg>
						
					</div>
					<h2>cases</h2>
				</li>
				<li title="center_align_text">
					<div class="icon-box">
						
						<!-- center_align_text -->
						<svg class="svg-center_align_text-dims">
							<use xlink:href="svg/symbols.svg#center_align_text"></use>
						</svg>
						
					</div>
					<h2>center_align_text</h2>
				</li>
				<li title="change_owner">
					<div class="icon-box">
						
						<!-- change_owner -->
						<svg class="svg-change_owner-dims">
							<use xlink:href="svg/symbols.svg#change_owner"></use>
						</svg>
						
					</div>
					<h2>change_owner</h2>
				</li>
				<li title="change_record_type">
					<div class="icon-box">
						
						<!-- change_record_type -->
						<svg class="svg-change_record_type-dims">
							<use xlink:href="svg/symbols.svg#change_record_type"></use>
						</svg>
						
					</div>
					<h2>change_record_type</h2>
				</li>
				<li title="chart">
					<div class="icon-box">
						
						<!-- chart -->
						<svg class="svg-chart-dims">
							<use xlink:href="svg/symbols.svg#chart"></use>
						</svg>
						
					</div>
					<h2>chart</h2>
				</li>
				<li title="chat">
					<div class="icon-box">
						
						<!-- chat -->
						<svg class="svg-chat-dims">
							<use xlink:href="svg/symbols.svg#chat"></use>
						</svg>
						
					</div>
					<h2>chat</h2>
				</li>
				<li title="check">
					<div class="icon-box">
						
						<!-- check -->
						<svg class="svg-check-dims">
							<use xlink:href="svg/symbols.svg#check"></use>
						</svg>
						
					</div>
					<h2>check</h2>
				</li>
				<li title="checkin">
					<div class="icon-box">
						
						<!-- checkin -->
						<svg class="svg-checkin-dims">
							<use xlink:href="svg/symbols.svg#checkin"></use>
						</svg>
						
					</div>
					<h2>checkin</h2>
				</li>
				<li title="chevrondown">
					<div class="icon-box">
						
						<!-- chevrondown -->
						<svg class="svg-chevrondown-dims">
							<use xlink:href="svg/symbols.svg#chevrondown"></use>
						</svg>
						
					</div>
					<h2>chevrondown</h2>
				</li>
				<li title="chevronleft">
					<div class="icon-box">
						
						<!-- chevronleft -->
						<svg class="svg-chevronleft-dims">
							<use xlink:href="svg/symbols.svg#chevronleft"></use>
						</svg>
						
					</div>
					<h2>chevronleft</h2>
				</li>
				<li title="chevronright">
					<div class="icon-box">
						
						<!-- chevronright -->
						<svg class="svg-chevronright-dims">
							<use xlink:href="svg/symbols.svg#chevronright"></use>
						</svg>
						
					</div>
					<h2>chevronright</h2>
				</li>
				<li title="chevronup">
					<div class="icon-box">
						
						<!-- chevronup -->
						<svg class="svg-chevronup-dims">
							<use xlink:href="svg/symbols.svg#chevronup"></use>
						</svg>
						
					</div>
					<h2>chevronup</h2>
				</li>
				<li title="clear">
					<div class="icon-box">
						
						<!-- clear -->
						<svg class="svg-clear-dims">
							<use xlink:href="svg/symbols.svg#clear"></use>
						</svg>
						
					</div>
					<h2>clear</h2>
				</li>
				<li title="clock">
					<div class="icon-box">
						
						<!-- clock -->
						<svg class="svg-clock-dims">
							<use xlink:href="svg/symbols.svg#clock"></use>
						</svg>
						
					</div>
					<h2>clock</h2>
				</li>
				<li title="close">
					<div class="icon-box">
						
						<!-- close -->
						<svg class="svg-close-dims">
							<use xlink:href="svg/symbols.svg#close"></use>
						</svg>
						
					</div>
					<h2>close</h2>
				</li>
				<li title="comments">
					<div class="icon-box">
						
						<!-- comments -->
						<svg class="svg-comments-dims">
							<use xlink:href="svg/symbols.svg#comments"></use>
						</svg>
						
					</div>
					<h2>comments</h2>
				</li>
				<li title="company">
					<div class="icon-box">
						
						<!-- company -->
						<svg class="svg-company-dims">
							<use xlink:href="svg/symbols.svg#company"></use>
						</svg>
						
					</div>
					<h2>company</h2>
				</li>
				<li title="connected_apps">
					<div class="icon-box">
						
						<!-- connected_apps -->
						<svg class="svg-connected_apps-dims">
							<use xlink:href="svg/symbols.svg#connected_apps"></use>
						</svg>
						
					</div>
					<h2>connected_apps</h2>
				</li>
				<li title="contract">
					<div class="icon-box">
						
						<!-- contract -->
						<svg class="svg-contract-dims">
							<use xlink:href="svg/symbols.svg#contract"></use>
						</svg>
						
					</div>
					<h2>contract</h2>
				</li>
				<li title="contract_alt">
					<div class="icon-box">
						
						<!-- contract_alt -->
						<svg class="svg-contract_alt-dims">
							<use xlink:href="svg/symbols.svg#contract_alt"></use>
						</svg>
						
					</div>
					<h2>contract_alt</h2>
				</li>
				<li title="copy">
					<div class="icon-box">
						
						<!-- copy -->
						<svg class="svg-copy-dims">
							<use xlink:href="svg/symbols.svg#copy"></use>
						</svg>
						
					</div>
					<h2>copy</h2>
				</li>
				<li title="crossfilter">
					<div class="icon-box">
						
						<!-- crossfilter -->
						<svg class="svg-crossfilter-dims">
							<use xlink:href="svg/symbols.svg#crossfilter"></use>
						</svg>
						
					</div>
					<h2>crossfilter</h2>
				</li>
				<li title="custom_apps">
					<div class="icon-box">
						
						<!-- custom_apps -->
						<svg class="svg-custom_apps-dims">
							<use xlink:href="svg/symbols.svg#custom_apps"></use>
						</svg>
						
					</div>
					<h2>custom_apps</h2>
				</li>
				<li title="cut">
					<div class="icon-box">
						
						<!-- cut -->
						<svg class="svg-cut-dims">
							<use xlink:href="svg/symbols.svg#cut"></use>
						</svg>
						
					</div>
					<h2>cut</h2>
				</li>
				<li title="dash">
					<div class="icon-box">
						
						<!-- dash -->
						<svg class="svg-dash-dims">
							<use xlink:href="svg/symbols.svg#dash"></use>
						</svg>
						
					</div>
					<h2>dash</h2>
				</li>
				<li title="database">
					<div class="icon-box">
						
						<!-- database -->
						<svg class="svg-database-dims">
							<use xlink:href="svg/symbols.svg#database"></use>
						</svg>
						
					</div>
					<h2>database</h2>
				</li>
				<li title="datadotcom">
					<div class="icon-box">
						
						<!-- datadotcom -->
						<svg class="svg-datadotcom-dims">
							<use xlink:href="svg/symbols.svg#datadotcom"></use>
						</svg>
						
					</div>
					<h2>datadotcom</h2>
				</li>
				<li title="dayview">
					<div class="icon-box">
						
						<!-- dayview -->
						<svg class="svg-dayview-dims">
							<use xlink:href="svg/symbols.svg#dayview"></use>
						</svg>
						
					</div>
					<h2>dayview</h2>
				</li>
				<li title="delete">
					<div class="icon-box">
						
						<!-- delete -->
						<svg class="svg-delete-dims">
							<use xlink:href="svg/symbols.svg#delete"></use>
						</svg>
						
					</div>
					<h2>delete</h2>
				</li>
				<li title="deprecate">
					<div class="icon-box">
						
						<!-- deprecate -->
						<svg class="svg-deprecate-dims">
							<use xlink:href="svg/symbols.svg#deprecate"></use>
						</svg>
						
					</div>
					<h2>deprecate</h2>
				</li>
				<li title="description">
					<div class="icon-box">
						
						<!-- description -->
						<svg class="svg-description-dims">
							<use xlink:href="svg/symbols.svg#description"></use>
						</svg>
						
					</div>
					<h2>description</h2>
				</li>
				<li title="desktop">
					<div class="icon-box">
						
						<!-- desktop -->
						<svg class="svg-desktop-dims">
							<use xlink:href="svg/symbols.svg#desktop"></use>
						</svg>
						
					</div>
					<h2>desktop</h2>
				</li>
				<li title="dislike">
					<div class="icon-box">
						
						<!-- dislike -->
						<svg class="svg-dislike-dims">
							<use xlink:href="svg/symbols.svg#dislike"></use>
						</svg>
						
					</div>
					<h2>dislike</h2>
				</li>
				<li title="dock_panel">
					<div class="icon-box">
						
						<!-- dock_panel -->
						<svg class="svg-dock_panel-dims">
							<use xlink:href="svg/symbols.svg#dock_panel"></use>
						</svg>
						
					</div>
					<h2>dock_panel</h2>
				</li>
				<li title="down">
					<div class="icon-box">
						
						<!-- down -->
						<svg class="svg-down-dims">
							<use xlink:href="svg/symbols.svg#down"></use>
						</svg>
						
					</div>
					<h2>down</h2>
				</li>
				<li title="download">
					<div class="icon-box">
						
						<!-- download -->
						<svg class="svg-download-dims">
							<use xlink:href="svg/symbols.svg#download"></use>
						</svg>
						
					</div>
					<h2>download</h2>
				</li>
				<li title="edit">
					<div class="icon-box">
						
						<!-- edit -->
						<svg class="svg-edit-dims">
							<use xlink:href="svg/symbols.svg#edit"></use>
						</svg>
						
					</div>
					<h2>edit</h2>
				</li>
				<li title="edit_form">
					<div class="icon-box">
						
						<!-- edit_form -->
						<svg class="svg-edit_form-dims">
							<use xlink:href="svg/symbols.svg#edit_form"></use>
						</svg>
						
					</div>
					<h2>edit_form</h2>
				</li>
				<li title="email">
					<div class="icon-box">
						
						<!-- email -->
						<svg class="svg-email-dims">
							<use xlink:href="svg/symbols.svg#email"></use>
						</svg>
						
					</div>
					<h2>email</h2>
				</li>
				<li title="end_call">
					<div class="icon-box">
						
						<!-- end_call -->
						<svg class="svg-end_call-dims">
							<use xlink:href="svg/symbols.svg#end_call"></use>
						</svg>
						
					</div>
					<h2>end_call</h2>
				</li>
				<li title="erect_window">
					<div class="icon-box">
						
						<!-- erect_window -->
						<svg class="svg-erect_window-dims">
							<use xlink:href="svg/symbols.svg#erect_window"></use>
						</svg>
						
					</div>
					<h2>erect_window</h2>
				</li>
				<li title="error">
					<div class="icon-box">
						
						<!-- error -->
						<svg class="svg-error-dims">
							<use xlink:href="svg/symbols.svg#error"></use>
						</svg>
						
					</div>
					<h2>error</h2>
				</li>
				<li title="event">
					<div class="icon-box">
						
						<!-- event -->
						<svg class="svg-event-dims">
							<use xlink:href="svg/symbols.svg#event"></use>
						</svg>
						
					</div>
					<h2>event</h2>
				</li>
				<li title="expand">
					<div class="icon-box">
						
						<!-- expand -->
						<svg class="svg-expand-dims">
							<use xlink:href="svg/symbols.svg#expand"></use>
						</svg>
						
					</div>
					<h2>expand</h2>
				</li>
				<li title="expand_alt">
					<div class="icon-box">
						
						<!-- expand_alt -->
						<svg class="svg-expand_alt-dims">
							<use xlink:href="svg/symbols.svg#expand_alt"></use>
						</svg>
						
					</div>
					<h2>expand_alt</h2>
				</li>
				<li title="fallback">
					<div class="icon-box">
						
						<!-- fallback -->
						<svg class="svg-fallback-dims">
							<use xlink:href="svg/symbols.svg#fallback"></use>
						</svg>
						
					</div>
					<h2>fallback</h2>
				</li>
				<li title="favorite">
					<div class="icon-box">
						
						<!-- favorite -->
						<svg class="svg-favorite-dims">
							<use xlink:href="svg/symbols.svg#favorite"></use>
						</svg>
						
					</div>
					<h2>favorite</h2>
				</li>
				<li title="feed">
					<div class="icon-box">
						
						<!-- feed -->
						<svg class="svg-feed-dims">
							<use xlink:href="svg/symbols.svg#feed"></use>
						</svg>
						
					</div>
					<h2>feed</h2>
				</li>
				<li title="file">
					<div class="icon-box">
						
						<!-- file -->
						<svg class="svg-file-dims">
							<use xlink:href="svg/symbols.svg#file"></use>
						</svg>
						
					</div>
					<h2>file</h2>
				</li>
				<li title="filter">
					<div class="icon-box">
						
						<!-- filter -->
						<svg class="svg-filter-dims">
							<use xlink:href="svg/symbols.svg#filter"></use>
						</svg>
						
					</div>
					<h2>filter</h2>
				</li>
				<li title="filterList">
					<div class="icon-box">
						
						<!-- filterList -->
						<svg class="svg-filterList-dims">
							<use xlink:href="svg/symbols.svg#filterList"></use>
						</svg>
						
					</div>
					<h2>filterList</h2>
				</li>
				<li title="flow">
					<div class="icon-box">
						
						<!-- flow -->
						<svg class="svg-flow-dims">
							<use xlink:href="svg/symbols.svg#flow"></use>
						</svg>
						
					</div>
					<h2>flow</h2>
				</li>
				<li title="forward">
					<div class="icon-box">
						
						<!-- forward -->
						<svg class="svg-forward-dims">
							<use xlink:href="svg/symbols.svg#forward"></use>
						</svg>
						
					</div>
					<h2>forward</h2>
				</li>
				<li title="frozen">
					<div class="icon-box">
						
						<!-- frozen -->
						<svg class="svg-frozen-dims">
							<use xlink:href="svg/symbols.svg#frozen"></use>
						</svg>
						
					</div>
					<h2>frozen</h2>
				</li>
				<li title="full_width_view">
					<div class="icon-box">
						
						<!-- full_width_view -->
						<svg class="svg-full_width_view-dims">
							<use xlink:href="svg/symbols.svg#full_width_view"></use>
						</svg>
						
					</div>
					<h2>full_width_view</h2>
				</li>
				<li title="groups">
					<div class="icon-box">
						
						<!-- groups -->
						<svg class="svg-groups-dims">
							<use xlink:href="svg/symbols.svg#groups"></use>
						</svg>
						
					</div>
					<h2>groups</h2>
				</li>
				<li title="help">
					<div class="icon-box">
						
						<!-- help -->
						<svg class="svg-help-dims">
							<use xlink:href="svg/symbols.svg#help"></use>
						</svg>
						
					</div>
					<h2>help</h2>
				</li>
				<li title="home">
					<div class="icon-box">
						
						<!-- home -->
						<svg class="svg-home-dims">
							<use xlink:href="svg/symbols.svg#home"></use>
						</svg>
						
					</div>
					<h2>home</h2>
				</li>
				<li title="identity">
					<div class="icon-box">
						
						<!-- identity -->
						<svg class="svg-identity-dims">
							<use xlink:href="svg/symbols.svg#identity"></use>
						</svg>
						
					</div>
					<h2>identity</h2>
				</li>
				<li title="image">
					<div class="icon-box">
						
						<!-- image -->
						<svg class="svg-image-dims">
							<use xlink:href="svg/symbols.svg#image"></use>
						</svg>
						
					</div>
					<h2>image</h2>
				</li>
				<li title="inbox">
					<div class="icon-box">
						
						<!-- inbox -->
						<svg class="svg-inbox-dims">
							<use xlink:href="svg/symbols.svg#inbox"></use>
						</svg>
						
					</div>
					<h2>inbox</h2>
				</li>
				<li title="info">
					<div class="icon-box">
						
						<!-- info -->
						<svg class="svg-info-dims">
							<use xlink:href="svg/symbols.svg#info"></use>
						</svg>
						
					</div>
					<h2>info</h2>
				</li>
				<li title="info_alt">
					<div class="icon-box">
						
						<!-- info_alt -->
						<svg class="svg-info_alt-dims">
							<use xlink:href="svg/symbols.svg#info_alt"></use>
						</svg>
						
					</div>
					<h2>info_alt</h2>
				</li>
				<li title="insert_tag_field">
					<div class="icon-box">
						
						<!-- insert_tag_field -->
						<svg class="svg-insert_tag_field-dims">
							<use xlink:href="svg/symbols.svg#insert_tag_field"></use>
						</svg>
						
					</div>
					<h2>insert_tag_field</h2>
				</li>
				<li title="insert_template">
					<div class="icon-box">
						
						<!-- insert_template -->
						<svg class="svg-insert_template-dims">
							<use xlink:href="svg/symbols.svg#insert_template"></use>
						</svg>
						
					</div>
					<h2>insert_template</h2>
				</li>
				<li title="italic">
					<div class="icon-box">
						
						<!-- italic -->
						<svg class="svg-italic-dims">
							<use xlink:href="svg/symbols.svg#italic"></use>
						</svg>
						
					</div>
					<h2>italic</h2>
				</li>
				<li title="jump_to_bottom">
					<div class="icon-box">
						
						<!-- jump_to_bottom -->
						<svg class="svg-jump_to_bottom-dims">
							<use xlink:href="svg/symbols.svg#jump_to_bottom"></use>
						</svg>
						
					</div>
					<h2>jump_to_bottom</h2>
				</li>
				<li title="jump_to_top">
					<div class="icon-box">
						
						<!-- jump_to_top -->
						<svg class="svg-jump_to_top-dims">
							<use xlink:href="svg/symbols.svg#jump_to_top"></use>
						</svg>
						
					</div>
					<h2>jump_to_top</h2>
				</li>
				<li title="justify_text">
					<div class="icon-box">
						
						<!-- justify_text -->
						<svg class="svg-justify_text-dims">
							<use xlink:href="svg/symbols.svg#justify_text"></use>
						</svg>
						
					</div>
					<h2>justify_text</h2>
				</li>
				<li title="kanban">
					<div class="icon-box">
						
						<!-- kanban -->
						<svg class="svg-kanban-dims">
							<use xlink:href="svg/symbols.svg#kanban"></use>
						</svg>
						
					</div>
					<h2>kanban</h2>
				</li>
				<li title="keyboard_dismiss">
					<div class="icon-box">
						
						<!-- keyboard_dismiss -->
						<svg class="svg-keyboard_dismiss-dims">
							<use xlink:href="svg/symbols.svg#keyboard_dismiss"></use>
						</svg>
						
					</div>
					<h2>keyboard_dismiss</h2>
				</li>
				<li title="knowledge_base">
					<div class="icon-box">
						
						<!-- knowledge_base -->
						<svg class="svg-knowledge_base-dims">
							<use xlink:href="svg/symbols.svg#knowledge_base"></use>
						</svg>
						
					</div>
					<h2>knowledge_base</h2>
				</li>
				<li title="layers">
					<div class="icon-box">
						
						<!-- layers -->
						<svg class="svg-layers-dims">
							<use xlink:href="svg/symbols.svg#layers"></use>
						</svg>
						
					</div>
					<h2>layers</h2>
				</li>
				<li title="layout">
					<div class="icon-box">
						
						<!-- layout -->
						<svg class="svg-layout-dims">
							<use xlink:href="svg/symbols.svg#layout"></use>
						</svg>
						
					</div>
					<h2>layout</h2>
				</li>
				<li title="left">
					<div class="icon-box">
						
						<!-- left -->
						<svg class="svg-left-dims">
							<use xlink:href="svg/symbols.svg#left"></use>
						</svg>
						
					</div>
					<h2>left</h2>
				</li>
				<li title="left_align_text">
					<div class="icon-box">
						
						<!-- left_align_text -->
						<svg class="svg-left_align_text-dims">
							<use xlink:href="svg/symbols.svg#left_align_text"></use>
						</svg>
						
					</div>
					<h2>left_align_text</h2>
				</li>
				<li title="level_up">
					<div class="icon-box">
						
						<!-- level_up -->
						<svg class="svg-level_up-dims">
							<use xlink:href="svg/symbols.svg#level_up"></use>
						</svg>
						
					</div>
					<h2>level_up</h2>
				</li>
				<li title="like">
					<div class="icon-box">
						
						<!-- like -->
						<svg class="svg-like-dims">
							<use xlink:href="svg/symbols.svg#like"></use>
						</svg>
						
					</div>
					<h2>like</h2>
				</li>
				<li title="link">
					<div class="icon-box">
						
						<!-- link -->
						<svg class="svg-link-dims">
							<use xlink:href="svg/symbols.svg#link"></use>
						</svg>
						
					</div>
					<h2>link</h2>
				</li>
				<li title="list">
					<div class="icon-box">
						
						<!-- list -->
						<svg class="svg-list-dims">
							<use xlink:href="svg/symbols.svg#list"></use>
						</svg>
						
					</div>
					<h2>list</h2>
				</li>
				<li title="location">
					<div class="icon-box">
						
						<!-- location -->
						<svg class="svg-location-dims">
							<use xlink:href="svg/symbols.svg#location"></use>
						</svg>
						
					</div>
					<h2>location</h2>
				</li>
				<li title="lock">
					<div class="icon-box">
						
						<!-- lock -->
						<svg class="svg-lock-dims">
							<use xlink:href="svg/symbols.svg#lock"></use>
						</svg>
						
					</div>
					<h2>lock</h2>
				</li>
				<li title="log_a_call">
					<div class="icon-box">
						
						<!-- log_a_call -->
						<svg class="svg-log_a_call-dims">
							<use xlink:href="svg/symbols.svg#log_a_call"></use>
						</svg>
						
					</div>
					<h2>log_a_call</h2>
				</li>
				<li title="logout">
					<div class="icon-box">
						
						<!-- logout -->
						<svg class="svg-logout-dims">
							<use xlink:href="svg/symbols.svg#logout"></use>
						</svg>
						
					</div>
					<h2>logout</h2>
				</li>
				<li title="magicwand">
					<div class="icon-box">
						
						<!-- magicwand -->
						<svg class="svg-magicwand-dims">
							<use xlink:href="svg/symbols.svg#magicwand"></use>
						</svg>
						
					</div>
					<h2>magicwand</h2>
				</li>
				<li title="mark_all_as_read">
					<div class="icon-box">
						
						<!-- mark_all_as_read -->
						<svg class="svg-mark_all_as_read-dims">
							<use xlink:href="svg/symbols.svg#mark_all_as_read"></use>
						</svg>
						
					</div>
					<h2>mark_all_as_read</h2>
				</li>
				<li title="matrix">
					<div class="icon-box">
						
						<!-- matrix -->
						<svg class="svg-matrix-dims">
							<use xlink:href="svg/symbols.svg#matrix"></use>
						</svg>
						
					</div>
					<h2>matrix</h2>
				</li>
				<li title="merge">
					<div class="icon-box">
						
						<!-- merge -->
						<svg class="svg-merge-dims">
							<use xlink:href="svg/symbols.svg#merge"></use>
						</svg>
						
					</div>
					<h2>merge</h2>
				</li>
				<li title="metrics">
					<div class="icon-box">
						
						<!-- metrics -->
						<svg class="svg-metrics-dims">
							<use xlink:href="svg/symbols.svg#metrics"></use>
						</svg>
						
					</div>
					<h2>metrics</h2>
				</li>
				<li title="minimize_window">
					<div class="icon-box">
						
						<!-- minimize_window -->
						<svg class="svg-minimize_window-dims">
							<use xlink:href="svg/symbols.svg#minimize_window"></use>
						</svg>
						
					</div>
					<h2>minimize_window</h2>
				</li>
				<li title="moneybag">
					<div class="icon-box">
						
						<!-- moneybag -->
						<svg class="svg-moneybag-dims">
							<use xlink:href="svg/symbols.svg#moneybag"></use>
						</svg>
						
					</div>
					<h2>moneybag</h2>
				</li>
				<li title="monthlyview">
					<div class="icon-box">
						
						<!-- monthlyview -->
						<svg class="svg-monthlyview-dims">
							<use xlink:href="svg/symbols.svg#monthlyview"></use>
						</svg>
						
					</div>
					<h2>monthlyview</h2>
				</li>
				<li title="move">
					<div class="icon-box">
						
						<!-- move -->
						<svg class="svg-move-dims">
							<use xlink:href="svg/symbols.svg#move"></use>
						</svg>
						
					</div>
					<h2>move</h2>
				</li>
				<li title="muted">
					<div class="icon-box">
						
						<!-- muted -->
						<svg class="svg-muted-dims">
							<use xlink:href="svg/symbols.svg#muted"></use>
						</svg>
						
					</div>
					<h2>muted</h2>
				</li>
				<li title="new">
					<div class="icon-box">
						
						<!-- new -->
						<svg class="svg-new-dims">
							<use xlink:href="svg/symbols.svg#new"></use>
						</svg>
						
					</div>
					<h2>new</h2>
				</li>
				<li title="new_window">
					<div class="icon-box">
						
						<!-- new_window -->
						<svg class="svg-new_window-dims">
							<use xlink:href="svg/symbols.svg#new_window"></use>
						</svg>
						
					</div>
					<h2>new_window</h2>
				</li>
				<li title="news">
					<div class="icon-box">
						
						<!-- news -->
						<svg class="svg-news-dims">
							<use xlink:href="svg/symbols.svg#news"></use>
						</svg>
						
					</div>
					<h2>news</h2>
				</li>
				<li title="note">
					<div class="icon-box">
						
						<!-- note -->
						<svg class="svg-note-dims">
							<use xlink:href="svg/symbols.svg#note"></use>
						</svg>
						
					</div>
					<h2>note</h2>
				</li>
				<li title="notebook">
					<div class="icon-box">
						
						<!-- notebook -->
						<svg class="svg-notebook-dims">
							<use xlink:href="svg/symbols.svg#notebook"></use>
						</svg>
						
					</div>
					<h2>notebook</h2>
				</li>
				<li title="notification">
					<div class="icon-box">
						
						<!-- notification -->
						<svg class="svg-notification-dims">
							<use xlink:href="svg/symbols.svg#notification"></use>
						</svg>
						
					</div>
					<h2>notification</h2>
				</li>
				<li title="office365">
					<div class="icon-box">
						
						<!-- office365 -->
						<svg class="svg-office365-dims">
							<use xlink:href="svg/symbols.svg#office365"></use>
						</svg>
						
					</div>
					<h2>office365</h2>
				</li>
				<li title="offline">
					<div class="icon-box">
						
						<!-- offline -->
						<svg class="svg-offline-dims">
							<use xlink:href="svg/symbols.svg#offline"></use>
						</svg>
						
					</div>
					<h2>offline</h2>
				</li>
				<li title="open">
					<div class="icon-box">
						
						<!-- open -->
						<svg class="svg-open-dims">
							<use xlink:href="svg/symbols.svg#open"></use>
						</svg>
						
					</div>
					<h2>open</h2>
				</li>
				<li title="open_folder">
					<div class="icon-box">
						
						<!-- open_folder -->
						<svg class="svg-open_folder-dims">
							<use xlink:href="svg/symbols.svg#open_folder"></use>
						</svg>
						
					</div>
					<h2>open_folder</h2>
				</li>
				<li title="opened_folder">
					<div class="icon-box">
						
						<!-- opened_folder -->
						<svg class="svg-opened_folder-dims">
							<use xlink:href="svg/symbols.svg#opened_folder"></use>
						</svg>
						
					</div>
					<h2>opened_folder</h2>
				</li>
				<li title="overflow">
					<div class="icon-box">
						
						<!-- overflow -->
						<svg class="svg-overflow-dims">
							<use xlink:href="svg/symbols.svg#overflow"></use>
						</svg>
						
					</div>
					<h2>overflow</h2>
				</li>
				<li title="package">
					<div class="icon-box">
						
						<!-- package -->
						<svg class="svg-package-dims">
							<use xlink:href="svg/symbols.svg#package"></use>
						</svg>
						
					</div>
					<h2>package</h2>
				</li>
				<li title="package_org">
					<div class="icon-box">
						
						<!-- package_org -->
						<svg class="svg-package_org-dims">
							<use xlink:href="svg/symbols.svg#package_org"></use>
						</svg>
						
					</div>
					<h2>package_org</h2>
				</li>
				<li title="package_org_beta">
					<div class="icon-box">
						
						<!-- package_org_beta -->
						<svg class="svg-package_org_beta-dims">
							<use xlink:href="svg/symbols.svg#package_org_beta"></use>
						</svg>
						
					</div>
					<h2>package_org_beta</h2>
				</li>
				<li title="page">
					<div class="icon-box">
						
						<!-- page -->
						<svg class="svg-page-dims">
							<use xlink:href="svg/symbols.svg#page"></use>
						</svg>
						
					</div>
					<h2>page</h2>
				</li>
				<li title="palette">
					<div class="icon-box">
						
						<!-- palette -->
						<svg class="svg-palette-dims">
							<use xlink:href="svg/symbols.svg#palette"></use>
						</svg>
						
					</div>
					<h2>palette</h2>
				</li>
				<li title="paste">
					<div class="icon-box">
						
						<!-- paste -->
						<svg class="svg-paste-dims">
							<use xlink:href="svg/symbols.svg#paste"></use>
						</svg>
						
					</div>
					<h2>paste</h2>
				</li>
				<li title="people">
					<div class="icon-box">
						
						<!-- people -->
						<svg class="svg-people-dims">
							<use xlink:href="svg/symbols.svg#people"></use>
						</svg>
						
					</div>
					<h2>people</h2>
				</li>
				<li title="phone_landscape">
					<div class="icon-box">
						
						<!-- phone_landscape -->
						<svg class="svg-phone_landscape-dims">
							<use xlink:href="svg/symbols.svg#phone_landscape"></use>
						</svg>
						
					</div>
					<h2>phone_landscape</h2>
				</li>
				<li title="phone_portrait">
					<div class="icon-box">
						
						<!-- phone_portrait -->
						<svg class="svg-phone_portrait-dims">
							<use xlink:href="svg/symbols.svg#phone_portrait"></use>
						</svg>
						
					</div>
					<h2>phone_portrait</h2>
				</li>
				<li title="photo">
					<div class="icon-box">
						
						<!-- photo -->
						<svg class="svg-photo-dims">
							<use xlink:href="svg/symbols.svg#photo"></use>
						</svg>
						
					</div>
					<h2>photo</h2>
				</li>
				<li title="picklist">
					<div class="icon-box">
						
						<!-- picklist -->
						<svg class="svg-picklist-dims">
							<use xlink:href="svg/symbols.svg#picklist"></use>
						</svg>
						
					</div>
					<h2>picklist</h2>
				</li>
				<li title="power">
					<div class="icon-box">
						
						<!-- power -->
						<svg class="svg-power-dims">
							<use xlink:href="svg/symbols.svg#power"></use>
						</svg>
						
					</div>
					<h2>power</h2>
				</li>
				<li title="preview">
					<div class="icon-box">
						
						<!-- preview -->
						<svg class="svg-preview-dims">
							<use xlink:href="svg/symbols.svg#preview"></use>
						</svg>
						
					</div>
					<h2>preview</h2>
				</li>
				<li title="priority">
					<div class="icon-box">
						
						<!-- priority -->
						<svg class="svg-priority-dims">
							<use xlink:href="svg/symbols.svg#priority"></use>
						</svg>
						
					</div>
					<h2>priority</h2>
				</li>
				<li title="process">
					<div class="icon-box">
						
						<!-- process -->
						<svg class="svg-process-dims">
							<use xlink:href="svg/symbols.svg#process"></use>
						</svg>
						
					</div>
					<h2>process</h2>
				</li>
				<li title="push">
					<div class="icon-box">
						
						<!-- push -->
						<svg class="svg-push-dims">
							<use xlink:href="svg/symbols.svg#push"></use>
						</svg>
						
					</div>
					<h2>push</h2>
				</li>
				<li title="puzzle">
					<div class="icon-box">
						
						<!-- puzzle -->
						<svg class="svg-puzzle-dims">
							<use xlink:href="svg/symbols.svg#puzzle"></use>
						</svg>
						
					</div>
					<h2>puzzle</h2>
				</li>
				<li title="question">
					<div class="icon-box">
						
						<!-- question -->
						<svg class="svg-question-dims">
							<use xlink:href="svg/symbols.svg#question"></use>
						</svg>
						
					</div>
					<h2>question</h2>
				</li>
				<li title="questions_and_answers">
					<div class="icon-box">
						
						<!-- questions_and_answers -->
						<svg class="svg-questions_and_answers-dims">
							<use xlink:href="svg/symbols.svg#questions_and_answers"></use>
						</svg>
						
					</div>
					<h2>questions_and_answers</h2>
				</li>
				<li title="record">
					<div class="icon-box">
						
						<!-- record -->
						<svg class="svg-record-dims">
							<use xlink:href="svg/symbols.svg#record"></use>
						</svg>
						
					</div>
					<h2>record</h2>
				</li>
				<li title="record_create">
					<div class="icon-box">
						
						<!-- record_create -->
						<svg class="svg-record_create-dims">
							<use xlink:href="svg/symbols.svg#record_create"></use>
						</svg>
						
					</div>
					<h2>record_create</h2>
				</li>
				<li title="redo">
					<div class="icon-box">
						
						<!-- redo -->
						<svg class="svg-redo-dims">
							<use xlink:href="svg/symbols.svg#redo"></use>
						</svg>
						
					</div>
					<h2>redo</h2>
				</li>
				<li title="refresh">
					<div class="icon-box">
						
						<!-- refresh -->
						<svg class="svg-refresh-dims">
							<use xlink:href="svg/symbols.svg#refresh"></use>
						</svg>
						
					</div>
					<h2>refresh</h2>
				</li>
				<li title="relate">
					<div class="icon-box">
						
						<!-- relate -->
						<svg class="svg-relate-dims">
							<use xlink:href="svg/symbols.svg#relate"></use>
						</svg>
						
					</div>
					<h2>relate</h2>
				</li>
				<li title="remove_formatting">
					<div class="icon-box">
						
						<!-- remove_formatting -->
						<svg class="svg-remove_formatting-dims">
							<use xlink:href="svg/symbols.svg#remove_formatting"></use>
						</svg>
						
					</div>
					<h2>remove_formatting</h2>
				</li>
				<li title="remove_link">
					<div class="icon-box">
						
						<!-- remove_link -->
						<svg class="svg-remove_link-dims">
							<use xlink:href="svg/symbols.svg#remove_link"></use>
						</svg>
						
					</div>
					<h2>remove_link</h2>
				</li>
				<li title="replace">
					<div class="icon-box">
						
						<!-- replace -->
						<svg class="svg-replace-dims">
							<use xlink:href="svg/symbols.svg#replace"></use>
						</svg>
						
					</div>
					<h2>replace</h2>
				</li>
				<li title="reply">
					<div class="icon-box">
						
						<!-- reply -->
						<svg class="svg-reply-dims">
							<use xlink:href="svg/symbols.svg#reply"></use>
						</svg>
						
					</div>
					<h2>reply</h2>
				</li>
				<li title="reply_all">
					<div class="icon-box">
						
						<!-- reply_all -->
						<svg class="svg-reply_all-dims">
							<use xlink:href="svg/symbols.svg#reply_all"></use>
						</svg>
						
					</div>
					<h2>reply_all</h2>
				</li>
				<li title="reset_password">
					<div class="icon-box">
						
						<!-- reset_password -->
						<svg class="svg-reset_password-dims">
							<use xlink:href="svg/symbols.svg#reset_password"></use>
						</svg>
						
					</div>
					<h2>reset_password</h2>
				</li>
				<li title="resource_absence">
					<div class="icon-box">
						
						<!-- resource_absence -->
						<svg class="svg-resource_absence-dims">
							<use xlink:href="svg/symbols.svg#resource_absence"></use>
						</svg>
						
					</div>
					<h2>resource_absence</h2>
				</li>
				<li title="resource_capacity">
					<div class="icon-box">
						
						<!-- resource_capacity -->
						<svg class="svg-resource_capacity-dims">
							<use xlink:href="svg/symbols.svg#resource_capacity"></use>
						</svg>
						
					</div>
					<h2>resource_capacity</h2>
				</li>
				<li title="resource_territory">
					<div class="icon-box">
						
						<!-- resource_territory -->
						<svg class="svg-resource_territory-dims">
							<use xlink:href="svg/symbols.svg#resource_territory"></use>
						</svg>
						
					</div>
					<h2>resource_territory</h2>
				</li>
				<li title="retweet">
					<div class="icon-box">
						
						<!-- retweet -->
						<svg class="svg-retweet-dims">
							<use xlink:href="svg/symbols.svg#retweet"></use>
						</svg>
						
					</div>
					<h2>retweet</h2>
				</li>
				<li title="richtextbulletedlist">
					<div class="icon-box">
						
						<!-- richtextbulletedlist -->
						<svg class="svg-richtextbulletedlist-dims">
							<use xlink:href="svg/symbols.svg#richtextbulletedlist"></use>
						</svg>
						
					</div>
					<h2>richtextbulletedlist</h2>
				</li>
				<li title="richtextindent">
					<div class="icon-box">
						
						<!-- richtextindent -->
						<svg class="svg-richtextindent-dims">
							<use xlink:href="svg/symbols.svg#richtextindent"></use>
						</svg>
						
					</div>
					<h2>richtextindent</h2>
				</li>
				<li title="richtextnumberedlist">
					<div class="icon-box">
						
						<!-- richtextnumberedlist -->
						<svg class="svg-richtextnumberedlist-dims">
							<use xlink:href="svg/symbols.svg#richtextnumberedlist"></use>
						</svg>
						
					</div>
					<h2>richtextnumberedlist</h2>
				</li>
				<li title="richtextoutdent">
					<div class="icon-box">
						
						<!-- richtextoutdent -->
						<svg class="svg-richtextoutdent-dims">
							<use xlink:href="svg/symbols.svg#richtextoutdent"></use>
						</svg>
						
					</div>
					<h2>richtextoutdent</h2>
				</li>
				<li title="right">
					<div class="icon-box">
						
						<!-- right -->
						<svg class="svg-right-dims">
							<use xlink:href="svg/symbols.svg#right"></use>
						</svg>
						
					</div>
					<h2>right</h2>
				</li>
				<li title="right_align_text">
					<div class="icon-box">
						
						<!-- right_align_text -->
						<svg class="svg-right_align_text-dims">
							<use xlink:href="svg/symbols.svg#right_align_text"></use>
						</svg>
						
					</div>
					<h2>right_align_text</h2>
				</li>
				<li title="rotate">
					<div class="icon-box">
						
						<!-- rotate -->
						<svg class="svg-rotate-dims">
							<use xlink:href="svg/symbols.svg#rotate"></use>
						</svg>
						
					</div>
					<h2>rotate</h2>
				</li>
				<li title="rows">
					<div class="icon-box">
						
						<!-- rows -->
						<svg class="svg-rows-dims">
							<use xlink:href="svg/symbols.svg#rows"></use>
						</svg>
						
					</div>
					<h2>rows</h2>
				</li>
				<li title="salesforce1">
					<div class="icon-box">
						
						<!-- salesforce1 -->
						<svg class="svg-salesforce1-dims">
							<use xlink:href="svg/symbols.svg#salesforce1"></use>
						</svg>
						
					</div>
					<h2>salesforce1</h2>
				</li>
				<li title="search">
					<div class="icon-box">
						
						<!-- search -->
						<svg class="svg-search-dims">
							<use xlink:href="svg/symbols.svg#search"></use>
						</svg>
						
					</div>
					<h2>search</h2>
				</li>
				<li title="settings">
					<div class="icon-box">
						
						<!-- settings -->
						<svg class="svg-settings-dims">
							<use xlink:href="svg/symbols.svg#settings"></use>
						</svg>
						
					</div>
					<h2>settings</h2>
				</li>
				<li title="setup">
					<div class="icon-box">
						
						<!-- setup -->
						<svg class="svg-setup-dims">
							<use xlink:href="svg/symbols.svg#setup"></use>
						</svg>
						
					</div>
					<h2>setup</h2>
				</li>
				<li title="setup_assistant_guide">
					<div class="icon-box">
						
						<!-- setup_assistant_guide -->
						<svg class="svg-setup_assistant_guide-dims">
							<use xlink:href="svg/symbols.svg#setup_assistant_guide"></use>
						</svg>
						
					</div>
					<h2>setup_assistant_guide</h2>
				</li>
				<li title="share">
					<div class="icon-box">
						
						<!-- share -->
						<svg class="svg-share-dims">
							<use xlink:href="svg/symbols.svg#share"></use>
						</svg>
						
					</div>
					<h2>share</h2>
				</li>
				<li title="share_mobile">
					<div class="icon-box">
						
						<!-- share_mobile -->
						<svg class="svg-share_mobile-dims">
							<use xlink:href="svg/symbols.svg#share_mobile"></use>
						</svg>
						
					</div>
					<h2>share_mobile</h2>
				</li>
				<li title="share_post">
					<div class="icon-box">
						
						<!-- share_post -->
						<svg class="svg-share_post-dims">
							<use xlink:href="svg/symbols.svg#share_post"></use>
						</svg>
						
					</div>
					<h2>share_post</h2>
				</li>
				<li title="shield">
					<div class="icon-box">
						
						<!-- shield -->
						<svg class="svg-shield-dims">
							<use xlink:href="svg/symbols.svg#shield"></use>
						</svg>
						
					</div>
					<h2>shield</h2>
				</li>
				<li title="side_list">
					<div class="icon-box">
						
						<!-- side_list -->
						<svg class="svg-side_list-dims">
							<use xlink:href="svg/symbols.svg#side_list"></use>
						</svg>
						
					</div>
					<h2>side_list</h2>
				</li>
				<li title="signpost">
					<div class="icon-box">
						
						<!-- signpost -->
						<svg class="svg-signpost-dims">
							<use xlink:href="svg/symbols.svg#signpost"></use>
						</svg>
						
					</div>
					<h2>signpost</h2>
				</li>
				<li title="sms">
					<div class="icon-box">
						
						<!-- sms -->
						<svg class="svg-sms-dims">
							<use xlink:href="svg/symbols.svg#sms"></use>
						</svg>
						
					</div>
					<h2>sms</h2>
				</li>
				<li title="snippet">
					<div class="icon-box">
						
						<!-- snippet -->
						<svg class="svg-snippet-dims">
							<use xlink:href="svg/symbols.svg#snippet"></use>
						</svg>
						
					</div>
					<h2>snippet</h2>
				</li>
				<li title="socialshare">
					<div class="icon-box">
						
						<!-- socialshare -->
						<svg class="svg-socialshare-dims">
							<use xlink:href="svg/symbols.svg#socialshare"></use>
						</svg>
						
					</div>
					<h2>socialshare</h2>
				</li>
				<li title="sort">
					<div class="icon-box">
						
						<!-- sort -->
						<svg class="svg-sort-dims">
							<use xlink:href="svg/symbols.svg#sort"></use>
						</svg>
						
					</div>
					<h2>sort</h2>
				</li>
				<li title="spinner">
					<div class="icon-box">
						
						<!-- spinner -->
						<svg class="svg-spinner-dims">
							<use xlink:href="svg/symbols.svg#spinner"></use>
						</svg>
						
					</div>
					<h2>spinner</h2>
				</li>
				<li title="standard_objects">
					<div class="icon-box">
						
						<!-- standard_objects -->
						<svg class="svg-standard_objects-dims">
							<use xlink:href="svg/symbols.svg#standard_objects"></use>
						</svg>
						
					</div>
					<h2>standard_objects</h2>
				</li>
				<li title="stop">
					<div class="icon-box">
						
						<!-- stop -->
						<svg class="svg-stop-dims">
							<use xlink:href="svg/symbols.svg#stop"></use>
						</svg>
						
					</div>
					<h2>stop</h2>
				</li>
				<li title="strikethrough">
					<div class="icon-box">
						
						<!-- strikethrough -->
						<svg class="svg-strikethrough-dims">
							<use xlink:href="svg/symbols.svg#strikethrough"></use>
						</svg>
						
					</div>
					<h2>strikethrough</h2>
				</li>
				<li title="success">
					<div class="icon-box">
						
						<!-- success -->
						<svg class="svg-success-dims">
							<use xlink:href="svg/symbols.svg#success"></use>
						</svg>
						
					</div>
					<h2>success</h2>
				</li>
				<li title="summary">
					<div class="icon-box">
						
						<!-- summary -->
						<svg class="svg-summary-dims">
							<use xlink:href="svg/symbols.svg#summary"></use>
						</svg>
						
					</div>
					<h2>summary</h2>
				</li>
				<li title="summarydetail">
					<div class="icon-box">
						
						<!-- summarydetail -->
						<svg class="svg-summarydetail-dims">
							<use xlink:href="svg/symbols.svg#summarydetail"></use>
						</svg>
						
					</div>
					<h2>summarydetail</h2>
				</li>
				<li title="switch">
					<div class="icon-box">
						
						<!-- switch -->
						<svg class="svg-switch-dims">
							<use xlink:href="svg/symbols.svg#switch"></use>
						</svg>
						
					</div>
					<h2>switch</h2>
				</li>
				<li title="sync">
					<div class="icon-box">
						
						<!-- sync -->
						<svg class="svg-sync-dims">
							<use xlink:href="svg/symbols.svg#sync"></use>
						</svg>
						
					</div>
					<h2>sync</h2>
				</li>
				<li title="table">
					<div class="icon-box">
						
						<!-- table -->
						<svg class="svg-table-dims">
							<use xlink:href="svg/symbols.svg#table"></use>
						</svg>
						
					</div>
					<h2>table</h2>
				</li>
				<li title="tablet_landscape">
					<div class="icon-box">
						
						<!-- tablet_landscape -->
						<svg class="svg-tablet_landscape-dims">
							<use xlink:href="svg/symbols.svg#tablet_landscape"></use>
						</svg>
						
					</div>
					<h2>tablet_landscape</h2>
				</li>
				<li title="tablet_portrait">
					<div class="icon-box">
						
						<!-- tablet_portrait -->
						<svg class="svg-tablet_portrait-dims">
							<use xlink:href="svg/symbols.svg#tablet_portrait"></use>
						</svg>
						
					</div>
					<h2>tablet_portrait</h2>
				</li>
				<li title="tabset">
					<div class="icon-box">
						
						<!-- tabset -->
						<svg class="svg-tabset-dims">
							<use xlink:href="svg/symbols.svg#tabset"></use>
						</svg>
						
					</div>
					<h2>tabset</h2>
				</li>
				<li title="task">
					<div class="icon-box">
						
						<!-- task -->
						<svg class="svg-task-dims">
							<use xlink:href="svg/symbols.svg#task"></use>
						</svg>
						
					</div>
					<h2>task</h2>
				</li>
				<li title="text_background_color">
					<div class="icon-box">
						
						<!-- text_background_color -->
						<svg class="svg-text_background_color-dims">
							<use xlink:href="svg/symbols.svg#text_background_color"></use>
						</svg>
						
					</div>
					<h2>text_background_color</h2>
				</li>
				<li title="text_color">
					<div class="icon-box">
						
						<!-- text_color -->
						<svg class="svg-text_color-dims">
							<use xlink:href="svg/symbols.svg#text_color"></use>
						</svg>
						
					</div>
					<h2>text_color</h2>
				</li>
				<li title="threedots">
					<div class="icon-box">
						
						<!-- threedots -->
						<svg class="svg-threedots-dims">
							<use xlink:href="svg/symbols.svg#threedots"></use>
						</svg>
						
					</div>
					<h2>threedots</h2>
				</li>
				<li title="threedots_vertical">
					<div class="icon-box">
						
						<!-- threedots_vertical -->
						<svg class="svg-threedots_vertical-dims">
							<use xlink:href="svg/symbols.svg#threedots_vertical"></use>
						</svg>
						
					</div>
					<h2>threedots_vertical</h2>
				</li>
				<li title="thunder">
					<div class="icon-box">
						
						<!-- thunder -->
						<svg class="svg-thunder-dims">
							<use xlink:href="svg/symbols.svg#thunder"></use>
						</svg>
						
					</div>
					<h2>thunder</h2>
				</li>
				<li title="tile_card_list">
					<div class="icon-box">
						
						<!-- tile_card_list -->
						<svg class="svg-tile_card_list-dims">
							<use xlink:href="svg/symbols.svg#tile_card_list"></use>
						</svg>
						
					</div>
					<h2>tile_card_list</h2>
				</li>
				<li title="topic">
					<div class="icon-box">
						
						<!-- topic -->
						<svg class="svg-topic-dims">
							<use xlink:href="svg/symbols.svg#topic"></use>
						</svg>
						
					</div>
					<h2>topic</h2>
				</li>
				<li title="touch_action">
					<div class="icon-box">
						
						<!-- touch_action -->
						<svg class="svg-touch_action-dims">
							<use xlink:href="svg/symbols.svg#touch_action"></use>
						</svg>
						
					</div>
					<h2>touch_action</h2>
				</li>
				<li title="trail">
					<div class="icon-box">
						
						<!-- trail -->
						<svg class="svg-trail-dims">
							<use xlink:href="svg/symbols.svg#trail"></use>
						</svg>
						
					</div>
					<h2>trail</h2>
				</li>
				<li title="turn_off_notifications">
					<div class="icon-box">
						
						<!-- turn_off_notifications -->
						<svg class="svg-turn_off_notifications-dims">
							<use xlink:href="svg/symbols.svg#turn_off_notifications"></use>
						</svg>
						
					</div>
					<h2>turn_off_notifications</h2>
				</li>
				<li title="undelete">
					<div class="icon-box">
						
						<!-- undelete -->
						<svg class="svg-undelete-dims">
							<use xlink:href="svg/symbols.svg#undelete"></use>
						</svg>
						
					</div>
					<h2>undelete</h2>
				</li>
				<li title="undeprecate">
					<div class="icon-box">
						
						<!-- undeprecate -->
						<svg class="svg-undeprecate-dims">
							<use xlink:href="svg/symbols.svg#undeprecate"></use>
						</svg>
						
					</div>
					<h2>undeprecate</h2>
				</li>
				<li title="underline">
					<div class="icon-box">
						
						<!-- underline -->
						<svg class="svg-underline-dims">
							<use xlink:href="svg/symbols.svg#underline"></use>
						</svg>
						
					</div>
					<h2>underline</h2>
				</li>
				<li title="undo">
					<div class="icon-box">
						
						<!-- undo -->
						<svg class="svg-undo-dims">
							<use xlink:href="svg/symbols.svg#undo"></use>
						</svg>
						
					</div>
					<h2>undo</h2>
				</li>
				<li title="unlock">
					<div class="icon-box">
						
						<!-- unlock -->
						<svg class="svg-unlock-dims">
							<use xlink:href="svg/symbols.svg#unlock"></use>
						</svg>
						
					</div>
					<h2>unlock</h2>
				</li>
				<li title="unmuted">
					<div class="icon-box">
						
						<!-- unmuted -->
						<svg class="svg-unmuted-dims">
							<use xlink:href="svg/symbols.svg#unmuted"></use>
						</svg>
						
					</div>
					<h2>unmuted</h2>
				</li>
				<li title="up">
					<div class="icon-box">
						
						<!-- up -->
						<svg class="svg-up-dims">
							<use xlink:href="svg/symbols.svg#up"></use>
						</svg>
						
					</div>
					<h2>up</h2>
				</li>
				<li title="upload">
					<div class="icon-box">
						
						<!-- upload -->
						<svg class="svg-upload-dims">
							<use xlink:href="svg/symbols.svg#upload"></use>
						</svg>
						
					</div>
					<h2>upload</h2>
				</li>
				<li title="user">
					<div class="icon-box">
						
						<!-- user -->
						<svg class="svg-user-dims">
							<use xlink:href="svg/symbols.svg#user"></use>
						</svg>
						
					</div>
					<h2>user</h2>
				</li>
				<li title="user_role">
					<div class="icon-box">
						
						<!-- user_role -->
						<svg class="svg-user_role-dims">
							<use xlink:href="svg/symbols.svg#user_role"></use>
						</svg>
						
					</div>
					<h2>user_role</h2>
				</li>
				<li title="volume_high">
					<div class="icon-box">
						
						<!-- volume_high -->
						<svg class="svg-volume_high-dims">
							<use xlink:href="svg/symbols.svg#volume_high"></use>
						</svg>
						
					</div>
					<h2>volume_high</h2>
				</li>
				<li title="volume_low">
					<div class="icon-box">
						
						<!-- volume_low -->
						<svg class="svg-volume_low-dims">
							<use xlink:href="svg/symbols.svg#volume_low"></use>
						</svg>
						
					</div>
					<h2>volume_low</h2>
				</li>
				<li title="volume_off">
					<div class="icon-box">
						
						<!-- volume_off -->
						<svg class="svg-volume_off-dims">
							<use xlink:href="svg/symbols.svg#volume_off"></use>
						</svg>
						
					</div>
					<h2>volume_off</h2>
				</li>
				<li title="warning">
					<div class="icon-box">
						
						<!-- warning -->
						<svg class="svg-warning-dims">
							<use xlink:href="svg/symbols.svg#warning"></use>
						</svg>
						
					</div>
					<h2>warning</h2>
				</li>
				<li title="weeklyview">
					<div class="icon-box">
						
						<!-- weeklyview -->
						<svg class="svg-weeklyview-dims">
							<use xlink:href="svg/symbols.svg#weeklyview"></use>
						</svg>
						
					</div>
					<h2>weeklyview</h2>
				</li>
				<li title="wifi">
					<div class="icon-box">
						
						<!-- wifi -->
						<svg class="svg-wifi-dims">
							<use xlink:href="svg/symbols.svg#wifi"></use>
						</svg>
						
					</div>
					<h2>wifi</h2>
				</li>
				<li title="work_order_type">
					<div class="icon-box">
						
						<!-- work_order_type -->
						<svg class="svg-work_order_type-dims">
							<use xlink:href="svg/symbols.svg#work_order_type"></use>
						</svg>
						
					</div>
					<h2>work_order_type</h2>
				</li>
				<li title="world">
					<div class="icon-box">
						
						<!-- world -->
						<svg class="svg-world-dims">
							<use xlink:href="svg/symbols.svg#world"></use>
						</svg>
						
					</div>
					<h2>world</h2>
				</li>
				<li title="yubi_key">
					<div class="icon-box">
						
						<!-- yubi_key -->
						<svg class="svg-yubi_key-dims">
							<use xlink:href="svg/symbols.svg#yubi_key"></use>
						</svg>
						
					</div>
					<h2>yubi_key</h2>
				</li>
				<li title="zoomin">
					<div class="icon-box">
						
						<!-- zoomin -->
						<svg class="svg-zoomin-dims">
							<use xlink:href="svg/symbols.svg#zoomin"></use>
						</svg>
						
					</div>
					<h2>zoomin</h2>
				</li>
				<li title="zoomout">
					<div class="icon-box">
						
						<!-- zoomout -->
						<svg class="svg-zoomout-dims">
							<use xlink:href="svg/symbols.svg#zoomout"></use>
						</svg>
						
					</div>
					<h2>zoomout</h2>
				</li>
			</ul>

<!--
====================================================================================================
-->

		</section>
		<footer>
			<p>Generated at Wed, 27 Jul 2016 17:59:49 GMT by <a href="https://github.com/jkphl/svg-sprite" target="_blank">svg-sprite</a>.</p>
		</footer>
	</body>
</html>
