// Lightning Design System 2.1.2
// Copyright (c) 2015, salesforce.com, inc. All rights reserved.

// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
// Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
// Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
// Neither the name of salesforce.com, inc. nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



.#{$css-prefix}button--neutral.#{$css-prefix}is-selected {
  border-color: transparent;
  background-color: transparent;

  &:hover:not([disabled]),
  &:focus:not([disabled]) {
    border: $border-width-thin solid $color-border-button-default;
    background-color: $color-background-button-default-hover;
  }

  &:active {
    background-color: $color-background-button-default-active;
  }
}

// Icon modifier for stateful buttons
.#{$css-prefix}button__icon--stateful {
  @include square($square-icon-small-content);
  fill: currentColor;
}

// Inverse Button
.#{$css-prefix}button--inverse.#{$css-prefix}is-selected {
  border-color: transparent;
}

// These styles show/hide text based on interaction
.#{$css-prefix}text-not-selected,
.#{$css-prefix}text-selected,
.#{$css-prefix}text-selected-focus,
.#{$css-prefix}is-selected[disabled]:hover .#{$css-prefix}text-selected,
.#{$css-prefix}is-selected[disabled]:focus .#{$css-prefix}text-selected {
  display: block;
}

.#{$css-prefix}not-selected .#{$css-prefix}text-selected,
.#{$css-prefix}not-selected .#{$css-prefix}text-selected-focus,
.#{$css-prefix}is-selected .#{$css-prefix}text-not-selected,
.#{$css-prefix}is-selected:not(:hover):not(:focus) .#{$css-prefix}text-selected-focus,
.#{$css-prefix}is-selected[disabled]:hover .#{$css-prefix}text-selected-focus,
.#{$css-prefix}is-selected:hover .#{$css-prefix}text-selected,
.#{$css-prefix}is-selected:focus .#{$css-prefix}text-selected {
  display: none;
}
