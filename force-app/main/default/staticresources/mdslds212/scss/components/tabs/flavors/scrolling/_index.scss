// Lightning Design System 2.1.2
// Copyright (c) 2015, salesforce.com, inc. All rights reserved.

// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
// Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
// Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
// Neither the name of salesforce.com, inc. nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


.#{$css-prefix}tabs__nav-scroller.#{$css-prefix}has-overflow {
  position: relative;
  padding-right: 4.7rem; // Allow for controls
}

.#{$css-prefix}tab__scroll-controls {
  display: none;
}

.#{$css-prefix}has-overflow {

  .#{$css-prefix}tabs__nav-scroller--inner {
    overflow: hidden;
  }

  .#{$css-prefix}tabs--scoped__nav,
  .#{$css-prefix}tabs--default__nav {
    border: 0;
  }

  .#{$css-prefix}tab__scroll-controls {
    display: flex; // This works to remove the whitespace between buttons
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: $color-background-alt;
  }
}

.#{$css-prefix}tabs--default {

  .#{$css-prefix}has-overflow .#{$css-prefix}tabs__nav-scroller--inner {
    border-bottom: $border-width-thin solid $color-border;
  }

  .#{$css-prefix}tab__scroll-controls {
    padding: calc(#{$spacing-xx-small} - #{$border-width-thin}) $border-width-thin calc(#{$spacing-xx-small} - #{$border-width-thin}) $spacing-x-small;
    border-bottom: $border-width-thin solid $color-border;
  }
}

.#{$css-prefix}tabs--scoped {

  .#{$css-prefix}has-overflow {

    .#{$css-prefix}tabs__nav-scroller--inner {
      border: $border-width-thin solid $color-border;
      border-bottom: 0;
      border-radius: $border-radius-medium $border-radius-medium 0 0;
      background-color: $color-background;
    }

    .#{$css-prefix}tabs--scoped__item {
      margin-bottom: 0;
      border-bottom: $border-width-thin solid $color-border;

      // scss-lint:disable NestingDepth SelectorDepth
      &.#{$css-prefix}active {
        border-bottom-color: $color-border-tab-active;
      }
    }
  }

  .#{$css-prefix}tab__scroll-controls {
    padding: calc(#{$spacing-xx-small} - #{$border-width-thin}) $spacing-xx-small;
    border: $border-width-thin solid $color-border;
    border-radius: 0 $border-radius-medium 0 0;
  }
}
