// Lightning Design System 2.1.2
// Copyright (c) 2015, salesforce.com, inc. All rights reserved.

// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
// Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
// Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
// Neither the name of salesforce.com, inc. nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



.#{$css-prefix}picklist--draggable {

  .#{$css-prefix}button {
    margin: $spacing-xx-small;

    &:first-of-type {
      margin-top: $spacing-large;
    }
  }
}

.#{$css-prefix}picklist__options {
  border: $border-width-thin solid $color-border-input {
    radius: $border-radius-medium;
  };
  padding: $spacing-xx-small 0;
  width: $size-small;
  height: $size-small;
  background-color: $color-background-input-active;

  &--multi {
    overflow: auto;
  }
}

.#{$css-prefix}picklist__item {
  position: relative;
  line-height: $line-height-text;

  > a,
  > span {
    display: block;
    padding: $spacing-x-small $spacing-small;

    &:hover {
      background-color: $color-background-row-hover;
      cursor: pointer;
    }

    &:active {
      background-color: $color-background-row-active;
    }
  }

  &[aria-selected="true"] {
    background-color: $color-background-row-active;
  }
}
