// Lightning Design System 2.1.2
// Copyright (c) 2015, salesforce.com, inc. All rights reserved.

// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
// Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
// Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
// Neither the name of salesforce.com, inc. nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



.#{$css-prefix}card {
  @include box($theme: shade, $padding: 0, $borders: around);

  + .#{$css-prefix}card {
    margin-top: $spacing-medium;
  }

  &__header,
  &__footer,
  &__body--inner {
    padding-left: $spacing-large;
    padding-right: $spacing-large;
  }

  &__header {
    padding-top: $spacing-small;
    margin-bottom: $spacing-small;
  }

  &__body:empty,
  &__footer:empty {
    display: none;
  }

  &__footer {
    margin-top: $spacing-small;
    padding-bottom: $spacing-small;
    text-align: right;
  }

  &--narrow {

    .#{$css-prefix}card__header {
      padding-top: $spacing-large;
      margin-bottom: $spacing-large;
    }
    .#{$css-prefix}card__footer {
      padding-bottom: $spacing-medium;
    }
  }

  @include deprecate('4.0.0', 'Use a text-align--center utility class instead') {
    .#{$css-prefix}card--empty .#{$css-prefix}card__body {
      text-align: center;
    }
  }
}

.#{$css-prefix}card__tile {
  margin-top: $spacing-x-small;
}
