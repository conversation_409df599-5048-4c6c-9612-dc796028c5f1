// Lightning Design System 2.1.2
// Copyright (c) 2015, salesforce.com, inc. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
// Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
// Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
// Neither the name of salesforce.com, inc. nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

@mixin core($scoped: false, $globals: false) {
  *,
  *:before,
  *:after {
    box-sizing: border-box;
  }

  ::placeholder {
    color: $color-text-placeholder;
    font-weight: $font-weight-regular;
    font-size: $font-size-medium;
  }

  ::selection {
    background: $color-background-selection;
    text-shadow: none;
    color: $color-text-default;
  }

  @if $globals {
    @if $scoped {
      @include root($root: '&', $body: '&:not(html), body');
    } @else {
      @include root();
    }
  }


  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  ol,
  ul,
  dl,
  fieldset {
    margin: 0;
    padding: 0;
  }

  dd,
  figure {
    margin: 0;
  }

  abbr[title],
  fieldset,
  hr {
    border: 0;
  }

  hr {
    padding: 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font: {
      weight: inherit;
      size: 1em;
    }
  }

  ol,
  ul {
    list-style: none;
  }



  a {
    color: $color-text-link;
    text-decoration: none;
    transition: color 0.1s linear;

    &:hover,
    &:focus {
      text-decoration: underline;
      color: $color-text-link-hover;
    }

    &:active {
      color: $color-text-link-active;
    }
  }

  a,
  button {
    cursor: pointer;
  }

  b,
  strong,
  dfn {
    font-weight: $font-weight-bold;
  }

  mark {
    background-color: $color-background-highlight-search;
    color: $color-text-default;
  }

  abbr[title] {
    cursor: help;
  }



  input[type=search] {
    box-sizing: border-box;
  }



  table {
    width: 100%;
  }

  caption,
  th,
  td {
    text-align: left;
  }



  hr {
    display: block;
    margin: $spacing-x-large 0;
    border-top: 1px solid $color-border;
    height: 1px;
    clear: both;
  }

  audio,
  canvas,
  iframe,
  img,
  svg,
  video {
    vertical-align: middle;
  }

  img {
    max-width: 100%;
    height: auto;
  }
}
