// Lightning Design System 2.1.2
// Copyright (c) 2015, salesforce.com, inc. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
// Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
// Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
// Neither the name of salesforce.com, inc. nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

@mixin list-has-bullets {
  margin-left: $spacing-large;
  list-style: disc;
}

@mixin list-has-numbers {
  margin-left: $spacing-large;
  list-style: decimal;
}

// Apply theme on hover for list items
@mixin list-has-hovers {

  &:hover {
    background-color: $color-background-row-hover;
    border-color: $color-border;
    cursor: pointer;
  }

  &:active {
    background-color: $color-background-row-active;
    box-shadow: $color-border 0 ($border-width-thin * -1) 0 inset;
  }
}

// Apply theme on selection for list items
@mixin list-has-selections {

  &.#{$css-prefix}is-selected {
    box-shadow: $color-border-selection 0 0 0 $border-width-thin inset;
    background-color: $color-background-row-selected;

    &:hover,
    &:focus {
      box-shadow: $color-border-selection-hover 0 ($border-width-thick * -1) 0 inset, $color-border-selection-hover 0 0 0 $border-width-thin inset;
    }
  }
}




// Output dot separators based on direction parameter
//
// @require $size, $direction, $color, $mq
// @param $direction {String} Position of dot, accepts 'left' or 'right'
// @param $size {String} Defines the width and height of the dot
// @param $color {String} Defines the color of the dot
// @param $mq {Boolean} If you want a dots on touch device or not

@mixin dot-separator($direction: right, $size: 2px, $color: $color-background-indicator-dot, $mq: null) {
  position: relative;
  display: flex;
  align-items: center;

  @if $direction == right {
    &:after {
      @include square($size);
      content: '';
      margin: {
        left: $spacing-x-small;
        right: $spacing-x-small;
      }
      border-radius: $border-radius-circle;
      background-color: $color;
    }

    &:last-child {
      margin-right: 0;
      padding-right: 0;

      &:after {
        content: none;
      }
    }
  }

  @elseif $direction == left {
    &:before {
      @include square($size);
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin: {
        left: $spacing-x-small;
        right: $spacing-x-small;
      }
      border-radius: $border-radius-circle;
      background-color: $color;
    }

    &:first-child {
      margin-right: 0;
      padding-right: 0;

      &:before {
        content: none;
      }
    }
  }

  @if $mq != null {

    @include mq-medium-max {
      &:before,
      &:after {
        content: none;
      }
    }
  }
}
