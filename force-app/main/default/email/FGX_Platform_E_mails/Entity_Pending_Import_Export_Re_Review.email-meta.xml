<?xml version="1.0" encoding="UTF-8"?>
<EmailTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <available>true</available>
    <description>Notification to confirm an existing entity has been submitted for additional FGX import/export review</description>
    <encodingKey>ISO-8859-1</encodingKey>
    <letterhead>FGX_Standard_Letterhead</letterhead>
    <name>Entity: Pending Import/Export Re-Review</name>
    <style>freeForm</style>
    <subject>FGX Entity Pending Import/Export Re-Review / {!Entity__c.Client_Account__c} / {!Entity__c.Entity_Name__c} / {!Entity__c.Name}</subject>
    <textOnly>Thank you for submitting Entity: {!Entity__c.Entity_Name__c} ({!Entity__c.Entity_Code__c}) for an additional Import/Export Review.

This entity may still be used as an importer or exporter when requesting a quote. However it will not be possible to use this entity in a new shipment until the review is complete and the entity is once again approved.



As soon as FGX&apos;s review is complete and the entity is once again approved for shipping, you will receive a follow-up notification.



Entity Details:
{!Entity__c.Entity_Name__c} ({!Entity__c.Entity_Code__c})
{!Entity__c.Legal_Name__c}
{!Entity__c.Entity_Consignment_Info__c}
Import Status: {!Entity__c.Import_Status__c}
Export Status: {!Entity__c.Export_Status__c}


ref#: {!Entity__c.Name}</textOnly>
    <type>html</type>
    <uiType>Aloha</uiType>
</EmailTemplate>
