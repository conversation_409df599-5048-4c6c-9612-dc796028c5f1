<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Inventory</label>
    <protected>false</protected>
    <values>
        <field>Icon_Name__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Icon__c</field>
        <value xsi:type="xsd:string">/images/inventory.svg</value>
    </values>
    <values>
        <field>Is_Dropdown__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>Label__c</field>
        <value xsi:type="xsd:string">Inventory</value>
    </values>
    <values>
        <field>Link_Type__c</field>
        <value xsi:type="xsd:string">dropdown</value>
    </values>
    <values>
        <field>Navbar_Visible__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>Navigation_Link_Name__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Page_Navigation__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>Page_Ref_Type__c</field>
        <value xsi:type="xsd:string">InternalLink</value>
    </values>
    <values>
        <field>Sort_Order__c</field>
        <value xsi:type="xsd:double">3.0</value>
    </values>
    <values>
        <field>Sub_Header__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Target__c</field>
        <value xsi:type="xsd:string">inventory</value>
    </values>
    <values>
        <field>URL_Destination__c</field>
        <value xsi:nil="true"/>
    </values>
</CustomMetadata>
