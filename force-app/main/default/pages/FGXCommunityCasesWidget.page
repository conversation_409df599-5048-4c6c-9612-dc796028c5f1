<apex:page standardController="Case" recordSetVar="cases" tabstyle="case" sidebar="false" showheader="False">
  <apex:pageBlock >
    <apex:pageBlockTable value="{!cases}" var="c">
      <apex:column headervalue="Case Number"><apex:outputLink value="/{!c.id}" target="_parent">{!c.CaseNumber}</apex:outputLink></apex:column>
      <apex:column value="{!c.status}"/>
      <apex:column value="{!c.subject}"/>
      <apex:column value="{!c.priority}"/>
    </apex:pageBlockTable>
  </apex:pageBlock>
</apex:page>