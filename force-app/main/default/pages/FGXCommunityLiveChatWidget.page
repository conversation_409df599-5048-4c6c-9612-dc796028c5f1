<apex:page showheader="false" sidebar="false">
<script type='text/javascript' src='https://c.la2w1.salesforceliveagent.com/content/g/js/31.0/deployment.js'></script>
<script type='text/javascript'>
liveagent.init('https://d.la2w1.salesforceliveagent.com/chat', '57260000000XZAR', '00D60000000KZsC');
</script>

<img id="liveagent_button_online_57360000000XZBP" style="display: none; border: 0px none; cursor: pointer" onclick="liveagent.startChat('57360000000XZBP')" src="https://fgx.my.salesforce-sites.com/resources/resource/1407267476000/OnlineChatLogo" /><img id="liveagent_button_offline_57360000000XZBP" style="display: none; border: 0px none; " src="https://fgx.my.salesforce-sites.com/resources/resource/1407267464000/OfflineChatLogo" />
<script type="text/javascript">
if (!window._laq) { window._laq = []; }
window._laq.push(function(){liveagent.showWhenOnline('57360000000XZBP', document.getElementById('liveagent_button_online_57360000000XZBP'));
liveagent.showWhenOffline('57360000000XZBP', document.getElementById('liveagent_button_offline_57360000000XZBP'));
});</script>


</apex:page>