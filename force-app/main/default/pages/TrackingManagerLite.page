<!--
 - Created by re<PERSON><PERSON><PERSON> on 10/21/21.
 -->

<apex:page extensions="TrackingManagerLiteExtension"
           standardController="Case"
           recordSetVar="sobjects"
           standardStylesheets="false"
           sidebar="false"
           applyBodyTag="false"
           docType="html-5.0">
    <html xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">

    <head>
        <apex:slds />
        <apex:includeScript value="//code.jquery.com/jquery-2.1.4.min.js" />
        <apex:includeScript value="//code.jquery.com/ui/1.12.1/jquery-ui.min.js" />
        <apex:stylesheet id="fontawesome" value="https://use.fontawesome.com/releases/v5.6.3/css/all.css"/>
    </head>
    <apex:form id="tracking-manager-form">
        <div class="slds-scope">

        <body>
        <div id="tracking-manager-modal" role="dialog" tabindex="-1" aria-labelledby="header43" class="slds-modal slds-modal_medium slds-fade-in-open">
            <div class="modal-container slds-modal__container" style="width: 75%; max-width: 100%;">
                <!-- Modal Header -->
                <div class="slds-modal__header">
                    <button class="slds-button slds-modal__close slds-button--icon-inverse"
                            title="Close"
                            onclick="closeModalAction(); return false;">
                        <i class="fa fa-times" style="color: #fff;font-size:1.75em"></i>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading--medium">Tracking Manager</h2>
                    <apex:actionFunction name="closeModalAction"
                                         action="{!cancel}">
                    </apex:actionFunction>
                </div>
                <!-- / Modal Header -->
                <!-- Modal Content -->
                <div class="slds-modal__content">
                    <div style="margin-bottom: 2em; overflow-x: scroll;">
                        <apex:outputPanel id="selected-case-table-container">
                            <apex:dataTable value="{!selectedCases}"
                                            var="case"
                                            id="selected-case-table"
                                            rowClasses="odd,even"
                                            rules="rows"
                                            styleClass="tableClass slds-table">
                                <apex:column >
                                    <apex:facet name="header"><strong>Case Number</strong></apex:facet>
                                    <apex:outputField value="{!case.CaseNumber}"></apex:outputField>
                                </apex:column>
                                <apex:column >
                                    <apex:facet name="header"><strong>Reference</strong></apex:facet>
                                    <apex:outputField value="{!case.Client_Ref__c}"></apex:outputField>
                                </apex:column>
                                <apex:column >
                                    <apex:facet name="header"><strong>Account Name</strong></apex:facet>
                                    <apex:outputField value="{!case.AccountId}"></apex:outputField>
                                </apex:column>
                                <apex:column >
                                    <apex:facet name="header"><strong>Description</strong></apex:facet>
                                    <apex:outputField value="{!case.Short_Description__c}"></apex:outputField>
                                </apex:column>
                                <apex:column >
                                    <apex:facet name="header"><strong>POD Name</strong></apex:facet>
                                    <apex:outputField value="{!case.POD_Name__c}"></apex:outputField>
                                </apex:column>
                                <apex:column >
                                    <apex:facet name="header"><strong>POD DateTime</strong></apex:facet>
                                    <apex:outputField value="{!case.POD_Datetime__c}"></apex:outputField>
                                </apex:column>
                                <apex:column >
                                    <apex:facet name="header"><strong>Status</strong></apex:facet>
                                    <apex:outputField value="{!case.Status}"></apex:outputField>
                                </apex:column>
                                <apex:column >
                                    <apex:facet name="header"><strong>Action</strong></apex:facet>
                                    <apex:commandButton styleClass="slds-button slds-button_neutral slds-m-around_xx-small"
                                                        onclick="removeSelected('{!case.Id}'); return false;"
                                                        value="Deselect">
                                    </apex:commandButton>
                                </apex:column>
                            </apex:dataTable>
                        </apex:outputPanel>
                        <apex:actionFunction name="removeSelected"
                                             reRender="selected-case-table-container, validate-add-event-script"
                                             onComplete="hideLoading();"
                                             action="{!handleDeselectShipment}">
                            <apex:param name="caseId"
                                        value=""/>
                        </apex:actionFunction>
                    </div>
                    <div>
                        <div class="slds-tabs_scoped">
                            <ul class="slds-tabs_scoped__nav" role="tablist">
                                <li id="clk_tab_1" class="tracking-manager-tab slds-tabs_scoped__item slds-is-active" title="Quick Add" role="presentation">
                                    <a class="slds-tabs_scoped__link" href="javascript:void(0);" role="tab" tabindex="0" aria-selected="true" aria-controls="tab-scoped-1" id="tab-scoped-1__item">Quick Add</a>
                                </li>
                                <li id="clk_tab_2" class="tracking-manager-tab slds-tabs_scoped__item" title="Custom Tracking" role="presentation">
                                    <a class="slds-tabs_scoped__link" href="javascript:void(0);" role="tab" tabindex="1" aria-selected="false" aria-controls="tab-scoped-2" id="tab-scoped-2__item">Custom Tracking</a>
                                </li>
                                <li id="clk_tab_3" class="tracking-manager-tab slds-tabs_scoped__item" title="Enter PDO" role="presentation">
                                    <a class="slds-tabs_scoped__link" href="javascript:void(0);" role="tab" tabindex="2" aria-selected="false" aria-controls="tab-scoped-3" id="tab-scoped-3__item">Enter POD</a>
                                </li>
                                <li id="clk_tab_4" class="tracking-manager-tab slds-tabs_scoped__item" title="Billing" role="presentation">
                                    <a class="slds-tabs_scoped__link" href="javascript:void(0);" role="tab" tabindex="3" aria-selected="false" aria-controls="tab-scoped-4" id="tab-scoped-4__item">Billing</a>
                                </li>
                            </ul>

                            <div id="tab-scoped-1" class="slds-tabs_scoped__content slds-show" role="tabpanel" aria-labelledby="tab-scoped-1__item">
                                <div class="slds-grid slds-m-right_xx-small slds-m-left_xx-small slds-wrap slds-gutters_direct">
                                    <div class="slds-col--padded slds-clearfix slds-size_10-of-12 slds-medium-size_8-of-12">
                                        <div class="slds-form-element slds-m-bottom_small">
                                            <label class="slds-form-element__label">
                                                <span>{!$ObjectType.Tracking_Item__c.Fields.Tracking_Update__c.Label}</span>
                                            </label>
                                            <div class="slds-form-element__control">
                                                <apex:outputPanel id="tracking-update-select">
                                                    <apex:selectList style="padding-left:6px;height:32px;"
                                                                     size="1"
                                                                     onChange="trackingUpdateChange(); return false;"
                                                                     value="{!trackingQuickAddStatus}"
                                                                     styleClass="slds-select">
                                                        <apex:selectOptions value="{!caseStatusOptions}">
                                                            <apex:param value="{!trackingQuickAddStatus}"/>
                                                        </apex:selectOptions>
                                                    </apex:selectList>
                                                </apex:outputPanel>
                                                <apex:actionFunction name="trackingUpdateChange"
                                                                     reRender="tracking-update-output">
                                                </apex:actionFunction>
                                            </div>
                                        </div>
                                        <div class="slds-form-element slds-m-bottom_small">
                                            <label class="slds-form-element__label">
                                                <span>{!$ObjectType.Tracking_Item__c.Fields.Event_Datetime__c.Label}</span>
                                            </label>
                                            <div class="slds-form-element__control">
                                                <apex:input type="datetime-local"
                                                            style="width:100%"
                                                            styleClass="slds-input"
                                                            value="{!trackingQuickAddDate}">
                                                </apex:input>
                                            </div>
                                        </div>
                                        <div class="slds-form-element slds-m-bottom_small">
                                            <label class="slds-form-element__label">
                                                <span>{!$ObjectType.Tracking_Item__c.Fields.Description__c.Label}</span>
                                            </label>
                                            <div class="slds-form-element__control">
                                                <apex:inputTextarea style="width:100%"
                                                                    styleClass="slds-input"
                                                                    value="{!trackingQuickAddDescription}">
                                                </apex:inputTextarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="slds-col--padded slds-clearfix slds-size_4-of-12 slds-medium-size_4-of-12">
<!--                                        <div class="slds-form-element slds-m-bottom_small">-->
<!--                                            <label class="slds-form-element__label">-->
<!--                                                <span>Current Shipment Status</span>-->
<!--                                            </label>-->
<!--                                            <div class="slds-form-element__control">-->
<!--                                                <apex:outputPanel id">-->
<!--                                                    <apex:outputText style="width:100%" value="{!currentShipment.Status}" />-->
<!--                                                </apex:outputPanel>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                        <div class="slds-form-element slds-m-bottom_small">-->
<!--                                            <label class="slds-form-element__label">-->
<!--                                                <span>New Shipment Status</span>-->
<!--                                            </label>-->
<!--                                            <div class="slds-form-element__control">-->
<!--                                                <apex:outputText id="tracking-update-output"-->
<!--                                                                 style="width:100%"-->
<!--                                                />-->
<!--                                            </div>-->
<!--                                        </div>-->

                                        <label class="slds-form-element slds-m-bottom_small">
                                            <div class="slds-form-element__label">Update Status</div>
                                            <div class="slds-checkbox_toggle form-element">
                                                <apex:inputCheckbox styleclass="ng-untouched ng-pristine ng-valid"
                                                                    value="{!updateStatusTrackingMgr}"
                                                                    onChange="trackingUpdateChange(); return false;">
                                                </apex:inputCheckbox>

                                                <span class="slds-checkbox_faux_container">
                                                    <div class="slds-checkbox_faux" style="outline:none;box-shadow: none;"></div>
                                                </span>
                                            </div>
                                        </label>

                                        <apex:outputPanel id="tracking_modal_send_tracking">
                                            <label class="slds-checkbox_toggle form-element">
                                                <div class="slds-form-element__label">Tracking E-mails</div>
                                                <div class="slds-checkbox_toggle form-element">
                                                    <apex:inputcheckbox styleclass="ng-untouched ng-pristine ng-valid" value="{!sendUpdatesTrackingMgr}"/>
                                                    <span class="slds-checkbox_faux_container">
                                                        <div class="slds-checkbox_faux" style="outline:none;box-shadow: none;"></div>
                                                    </span>
                                                </div>
                                            </label>
                                        </apex:outputPanel>
                                    </div>
                                    <div class="slds-col slds-clearfix slds-size_3-of-7 slds-medium-size_1-of-1">
                                        <div class="slds-float_right">
                                            <apex:commandLink styleClass="slds-button slds-button--brand"
                                                              value="Add Event"
                                                              onclick="validateAddEvent(handleTrackingQuickAddAction); return false;">
                                            </apex:commandLink>
                                            <apex:actionFunction name="handleTrackingQuickAddAction"
                                                                 action="{!handleTrackingQuickAdd}"
                                                                 reRender="selected-case-table-container"
                                                                 onComplete="hideLoading();">
                                            </apex:actionFunction>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="tab-scoped-2" class="slds-tabs_scoped__content slds-hide" role="tabpanel" aria-labelledby="tab-scoped-2__item">
                                <div class="slds-grid slds-wrap slds-gutters_direct">
                                    <div class="slds-col--padded slds-clearfix slds-size_1-of-1 slds-medium-size_1-of-1">
                                        <div class="slds-form-element slds-m-bottom_small">
                                            <label class="slds-form-element__label">
                                                <span>{!$ObjectType.Tracking_Item__c.Fields.Name.Label}</span>
                                            </label>
                                            <div class="slds-form-element__control">
                                                <apex:input styleClass="slds-input"
                                                            style="width:100%"
                                                            html-maxlength="100"
                                                            value="{!trackingCustomStatus}"
                                                />
                                            </div>
                                        </div>
                                        <div class="slds-form-element slds-m-bottom_small">
                                            <label class="slds-form-element__label">
                                                <span>{!$ObjectType.Tracking_Item__c.Fields.Description__c.Label}</span>
                                            </label>
                                            <div class="slds-form-element__control">
                                                <apex:inputTextarea styleClass="slds-textarea"
                                                                    style="width:100%"
                                                                    html-maxlength="250"
                                                                    value="{!trackingCustomDescription}"
                                                />
                                            </div>
                                        </div>
                                        <div class="slds-form-element slds-m-bottom_small">
                                            <label class="slds-form-element__label">
                                                <span>{!$ObjectType.Tracking_Item__c.Fields.Event_Datetime__c.Label}</span>
                                            </label>
                                            <div class="slds-form-element__control">
                                                <apex:input type="datetime-local"
                                                            style="width:100%"
                                                            styleClass="slds-input"
                                                            value="{!trackingCustomDate}"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="slds-col slds-clearfix slds-size_3-of-7 slds-medium-size_1-of-1">
                                        <div class="slds-float_right">
                                            <div class="slds-grid">
                                                <div class="slds-col">
                                                    <apex:outputPanel id="tracking_modal_custom_send_tracking">
                                                        <label class="slds-checkbox_toggle form-element">
                                                            <div class="slds-form-element__label">Tracking E-mails</div>
                                                            <div class="slds-checkbox_toggle form-element">
                                                                <apex:inputcheckbox styleclass="ng-untouched ng-pristine ng-valid" value="{!sendUpdatesTrackingMgrCustom}"/>
                                                                <span class="slds-checkbox_faux_container">
                                                                        <div class="slds-checkbox_faux" style="outline:none;box-shadow: none;"></div>
                                                                    </span>
                                                            </div>
                                                        </label>
                                                    </apex:outputPanel>
                                                </div>
                                                <div class="slds-col slds-m-top_small">
                                                    <apex:commandLink styleClass="slds-button slds-button--brand"
                                                                      value="Add Event"
                                                                      onclick="validateAddEvent(handleTrackingCustomAddAction); return false;">
                                                    </apex:commandLink>
                                                    <apex:actionFunction name="handleTrackingCustomAddAction"
                                                                         action="{!handleTrackingCustomAdd}"
                                                                         reRender="selected-case-table-container"
                                                                        onComplete="hideLoading();">
                                                    </apex:actionFunction>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="tab-scoped-3" class="slds-tabs_scoped__content slds-hide" role="tabpanel" aria-labelledby="tab-scoped-3__item">
                                <apex:outputPanel id="tracking-pod-container">
                                    <div class="slds-grid slds-wrap slds-gutters_direct">
                                        <div class="slds-col--padded slds-clearfix slds-size_1-of-2 slds-medium-size_1-of-2">
                                            <apex:outputPanel id="tracking-pod-fields">
                                                <div class="slds-form-element slds-m-bottom_small">
                                                    <label class="slds-form-element__label">
                                                        <span>{!$ObjectType.Tracking_Item__c.Fields.Signature_Details__c.Label}<span style="color:red">*</span></span>
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        <apex:outputText rendered="{!deliveredTrackingEventExists}"
                                                                         value="{!trackingPODSigDetails}">
                                                        </apex:outputText>
                                                        <apex:input id="podSigDetails"
                                                                    rendered="{!!deliveredTrackingEventExists}"
                                                                    styleClass="slds-input"
                                                                    style="width:100%"
                                                                    value="{!trackingPODSigDetails}" />
                                                    </div>
                                                </div>
                                                <div class="slds-form-element slds-m-bottom_small">
                                                    <label class="slds-form-element__label">
                                                        <span>Delivery Date/Time<span style="color:red">*</span></span>
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        <apex:outputText rendered="{!deliveredTrackingEventExists}"
                                                                         value="{!trackingPODDate}">
                                                        </apex:outputText>
                                                        <apex:input id="podDate"
                                                                    rendered="{!!deliveredTrackingEventExists}"
                                                                    type="datetime-local"
                                                                    style="width:100%"
                                                                    styleClass="slds-input"
                                                                    value="{!trackingPODDate}"/>
                                                    </div>
                                                </div>
                                                <div class="slds-form-element slds-m-bottom_small">
                                                    <label class="slds-form-element__label">
                                                        <span>{!$ObjectType.Case.Fields.POD_Notes__c.Label} (will be e-mailed to customer)</span>
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        <apex:inputTextarea style="width:100%"
                                                                            styleClass="slds-input"
                                                                            value="{!trackingPODNotes}"
                                                                            disabled="{!deliveredTrackingEventExists}">
                                                        </apex:inputTextarea>
                                                    </div>
                                                </div>
                                            </apex:outputPanel>
                                        </div>
                                        <div class="slds-col--padded slds-clearfix slds-size_1-of-2 slds-medium-size_1-of-2">
											<c:TrackingManagerFileUpload cases="{!selectedCases}" displayHeight="32px" attachTo="attachments" desc="podDocs"/>
										</div>

                                        <div class="slds-col slds-clearfix slds-size_3-of-7 slds-medium-size_1-of-1">
                                            <div class="slds-float_right">
                                                <div class="slds-grid">
                                                    <div class="slds-col">
                                                        <apex:outputPanel id="tracking-pod-email-toggle">
                                                            <apex:outputPanel rendered="{!showSendPODEmailToggle}">
                                                                <label class="slds-form-element">
                                                                    <div class="slds-form-element__label">POD Email</div>
                                                                    <div class="slds-checkbox_toggle form-element">
                                                                        <apex:inputCheckbox styleclass="ng-untouched ng-pristine ng-valid"
                                                                                            value="{!sendPODEmail}">
                                                                        </apex:inputCheckbox>

                                                                        <span class="slds-checkbox_faux_container">
                                                                                <div class="slds-checkbox_faux" style="outline:none;box-shadow: none;"></div>
                                                                            </span>
                                                                    </div>
                                                                </label>
                                                            </apex:outputPanel>
                                                        </apex:outputPanel>
                                                    </div>
                                                    <div class="slds-col slds-m-top_small">
                                                        <apex:outputPanel id="tracking-pod-actions">

                                                            <apex:commandLink styleClass="slds-button slds-button--brand"
                                                                              value="Save POD"
                                                                              onclick="if (!validatePODinput(handleAddPODAction)) return false;">
                                                            </apex:commandLink>
                                                            <apex:actionFunction name="handleAddPODAction"
                                                                                 action="{!handleAddPOD}"
                                                                                 reRender="selected-case-table-container, tracking-pod-container, tracking-pod-actions, tracking-pod-email-toggle"
                                                                                 onComplete="hideLoading();">
                                                            </apex:actionFunction>

<!--                                                            <apex:commandLink rendered="{!deliveredTrackingEventExists}"-->
<!--                                                                              styleClass="slds-button slds-button_destructive"-->
<!--                                                                              value="Remove POD"-->
<!--                                                                              onclick="validateRemovePOD(); return false;"-->
<!--                                                                              oncomplete="hideLoading();">-->
<!--                                                            </apex:commandLink>-->

<!--                                                            <apex:actionFunction name="intiateRemoveCurrentPOD"-->
<!--                                                                                 action="{!handleRemoveCurrentPOD}"-->
<!--                                                                                 reRender="selected-case-table-container, tracking-pod-actions, tracking-pod-email-toggle, tracking-pod-fields">-->
<!--                                                            </apex:actionFunction>-->
                                                        </apex:outputPanel>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </apex:outputPanel>
                            </div>

                            <div id="tab-scoped-4" class="slds-tabs_scoped__content slds-hide" role="tabpanel" aria-labelledby="tab-scoped-4__item">
                                <div class="slds-grid slds-m-right_xx-small slds-m-left_xx-small slds-wrap slds-gutters_direct">
                                    <div class="slds-col--padded slds-clearfix slds-size_12-of-12 slds-medium-size_12-of-12">

                                    <div class="slds-form-element slds-m-left_small slds-m-right_small">
                                        <apex:outputPanel id="billing_addr">
                                            <label class="slds-form-element__label">Invoice Billing Address (printed on invoice): </label>
                                            <div class="slds-form-element__control">
                                                <apex:inputTextarea rows="5"
                                                                    html-placeholder="Please enter the billing address to be printed on the pre & post-pay invoices for these shipment(s)."
                                                                    styleClass="slds-textarea"
                                                                    value="{!billingAddr}"
                                                                    style="line-height:1.5"/>
                                            </div>
                                        </apex:outputPanel>
                                    </div>
                                    <div class="slds-form-element slds-m-left_small slds-m-right_small">
                                        <div class="slds-grid slds-gutters slds-wrap slds-m-top_medium">
                                            <div class="slds-col_padded slds-size_9-of-12">
                                                <label class="slds-form-element__label" >Billing Email:</label>
                                                <div class="slds-form-element__control">
                                                    <apex:outputPanel id="billing_email">
                                                        <apex:inputText styleClass="slds-input"
                                                                        value="{!billingEmail}"
                                                                        style="line-height:1.5"/>
                                                    </apex:outputPanel>
                                                </div>
                                            </div>
                                            <div class="slds-col--padded slds-size_3-of-12">
                                                <label class="slds-form-element__label">Billing Ready:</label>
                                                <div class="slds-form-element__control">
                                                    <apex:inputCheckbox styleClass="slds-input"
                                                                        style="width: 3em;"
                                                                        value="{!billingReady}"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="slds-form-element slds-m-top_medium slds-m-left_small slds-m-right_small">
                                        <label class="slds-form-element__label" >Billing Override Account:</label>
                                        <div class="slds-form-element__control">
                                                <c:AutoCompleteV2 allowClear="true"
                                                                  importJquery="false"
                                                                  syncManualEntry="false"
                                                                  labelField="Name"
                                                                  SObject="Account"
                                                                  valueField="Id"
                                                                  targetField="{!billingOverrideAccount}"
                                                                  whereClause=" RecordType.Name = 'Customer'"
                                                                  style="width:100%;"/>
                                        </div>
                                    </div>
                                    <div class="slds-form-element slds-m-top_medium slds-m-left_small slds-m-right_small">
                                        <label class="slds-form-element__label">Billing Invoice Notes (printed on invoice): </label>
                                        <div class="slds-form-element__control">
                                                <apex:inputText styleClass="slds-textarea"
                                                                style="width: 100%;"
                                                                value="{!billingInvoiceNotes}"/>
                                        </div>
                                    </div>

                                    <div class="slds-col slds-clearfix slds-size_3-of-7 slds-medium-size_1-of-1">
                                        <div class="slds-float_right slds-m-top_medium slds-m-right_small">
                                            <apex:commandLink styleClass="slds-button slds-button--brand"
                                                              value="Save"
                                                              onClick="showLoading();"
                                                              action="{!handleSaveBillingInfo}"
                                                              onComplete="hideLoading();">
                                            </apex:commandLink>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- / Modal Content -->
                <!-- Modal Footer -->
                <div class="slds-modal__footer">
                    <apex:commandLink styleClass="slds-button slds-button_neutral"
                                      value="Close"
                                      action="{!cancel}">
                    </apex:commandLink>
<!--                    <apex:commandLink styleClass="slds-button slds-button_brand"-->
<!--                                      value="Save"-->
<!--                                      onclick="showLoading();"-->
<!--                                      reRender="record_path"-->
<!--                                      action="{!handleTrackingTableSave}"-->
<!--                                      oncomplete="hideLoading(); toggleTrackingModal(); return false;">-->
<!--                    </apex:commandLink>-->
                </div>
                <!-- / Modal Footer -->
            </div>
        </div>
        <apex:outputPanel id="output_loading_modal">
            <div id="output_loading" style="display:none;">
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.25; z-index: 9002; background-color: black;">
                    &nbsp;
                </div>
                <div style="position: fixed; left: 0; top: 0; bottom: 0; right: 0; z-index: 9003; margin: 20% 45%">
                    <div style="display: inline-block; padding: 2px; background-color: #fff; width: 140px;border-radius:4px;">
                        <img src="/img/loading.gif" style="float: left; margin: 12px;" />
                        <span style="display: inline-block; padding: 10px 0px;">Please Wait...</span>
                    </div>
                </div>
            </div>
        </apex:outputPanel>
        <div class="slds-backdrop slds-backdrop--open"></div>
    </body>
    </div>

    <apex:outputPanel id="validate-add-event-script">
        <script>
            function validateAddEvent(actionFunction) {
                if ({!!containsDelivered}) {
                    showLoading();
                    actionFunction();
                } else if (confirm('WARNING - you are about to update tracking for a shipment that is already delivered, this will clear the POD information and change the status to what you have specified. Are you sure you want to proceed?')) {
                    showLoading();
                    actionFunction();
                }
            }
        </script>
    </apex:outputPanel>

    <script>
        $( document ).ready(function() {
            $( ".tracking-manager-tab" ).click(function(e) {
                var selected = this.id.substr(8,1);
                var activeTabnum = $(".tracking-manager-tab.slds-is-active")[0].id.substr(8,1);
                var activeTab = '#' + $(".tracking-manager-tab.slds-is-active")[0].id;
                var clickSelected = "#clk_tab_";
                var clickActive = "#clk_tab_";
                var tabSelected = "#tab-scoped-";
                var activeTabcontent = "#tab-scoped-";
                activeTabcontent += activeTabnum;
                clickActive += activeTabnum;
                clickSelected += selected;
                tabSelected += selected;
                console.log(activeTab + ',' + activeTabnum + ', ' + selected + ', ' + clickSelected + ', ' + tabSelected);
                if ( selected != activeTabnum ) {
                    $(activeTab).removeClass("slds-is-active");
                    $(activeTabcontent).toggleClass("slds-show slds-hide");

                    $(clickSelected).addClass("slds-is-active");
                    $(tabSelected).toggleClass("slds-hide slds-show");
                }
            });
        });

        function showLoading() {
            $("#output_loading").show();
        }

        function hideLoading() {
            $("#output_loading").hide();
        }

        function validatePODinput(actionFunction){
			var sigDetails = document.getElementById('j_id0:tracking-manager-form:podSigDetails').value;
			var podDate = document.getElementById('j_id0:tracking-manager-form:podDate').value;

			if (!podDate || !sigDetails){
				if (!sigDetails){
					alert('Please enter signature details.');
				}
				if (!podDate){
					alert('Please enter a valid date.');
				}
				return false;
			}

            if ({!!containsDelivered}) {
                    showLoading();
                    actionFunction();
                } else if (confirm('WARNING - you are about to update tracking for a shipment that is already delivered, this will clear the POD information and change the status to what you have specified. Are you sure you want to proceed?')) {
                    showLoading();
                    actionFunction();
                }

			return true;
		  }
    </script>
    </apex:form>
    </html>
</apex:page>