<apex:page id="communitiesSelfRegPage" showHeader="true" controller="CommunitiesSelfRegController" title="{!$Label.site.user_registration}">

    
    
    <style type="text/css">
        .verticalLine {
            border-left-color: #c00;
            border-left-width: 3px;
            border-left-style: solid;
            border-top-color: #c00;
            border-top-style: solid;
            border-bottom-color: #c00;
            border-bottom-style: solid;
        }
        .col1 {width:150px;}
        .col2 {width:350px;}
        .col1a {width:154px;}
        .col2a {width:185px;}
        .col3 {width:161px;}
    </style>

     <apex:define name="body">  
      <center>
              <!--Client Specific Logo -->
              <apex:image value="{!accountImageLink}" url="{!accountImageLink}" rendered="{!if((accountImageLink == ''),false,true)}"/>
              <br/>
                <div style="font-weight:bold">Already have an account? &nbsp;<apex:outputLink value="https://fgx.my.site.com/service/login" id="LoginLink">Click here to Login</apex:outputLink></div>
              <br/>
              <br/>
                 <apex:form id="theForm" forceSSL="true">
                    <apex:pageMessages id="error"/>
                    
                    <apex:panelGrid columns="1" width="500" cellpadding="0" cellspacing="0">
                      <apex:panelGrid columns="2" columnClasses="col1,col2" width="100%">
                      <div style="text-decoration:underline;font-weight:bold;font-size:12pt;align:left">Create an account</div><apex:outputLabel value="  " />
                      <apex:outputLabel value="  " /><apex:outputLabel value="  " />
                      <div style="font-weight:bold;font-size:10pt;align:left">Contact info:</div><apex:outputLabel value="  " />
                      <apex:outputLabel value="First Name" for="firstName"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="firstName" value="{!firstName}" label="First Name" />
                      </apex:outputPanel>  

                      <apex:outputLabel value="Last Name" for="lastName"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="lastName" value="{!lastName}" label="Last Name"/>
                      </apex:outputPanel> 
                      <!-- <apex:outputLabel value="{!$Label.site.community_nickname}" for="communityNickname"/>
                      <apex:inputText required="true" id="communityNickname" value="{!communityNickname}" label="{!$Label.site.community_nickname}"/> -->
                    
                      <apex:outputLabel value="{!$Label.site.email}" for="email"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="email" value="{!email}" label="{!$Label.site.email}"/>                      
                      </apex:outputPanel>


                      <apex:outputLabel value="Office Phone" for="phone"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="phone" value="{!phone}" label="Office Phone"/>
                      </apex:outputPanel>

                      <apex:outputLabel value="Mobile Phone" for="mobile"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="mobile" value="{!mobile}" label="Mobile Phone"/>
                      </apex:outputPanel>

                      <apex:outputLabel value="Department" for="department"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="department" value="{!department}" label="Department"/>   
                      </apex:outputPanel>
                      <apex:outputLabel value="  " /><apex:outputLabel value="  " />

                      <div style="font-weight:bold;font-size:10pt;align:left">Primary office location:</div><apex:outputLabel value="  " />
                      <apex:outputLabel value="Address" for="address"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="address" value="{!address}" label="Address"/>
                      </apex:outputPanel>
                      </apex:panelGrid>

                      <apex:panelGrid columns="3" columnClasses="col1a,col2a,col3" width="500">       
                        <apex:outputLabel value="City" for="city"/>
                        <apex:outputPanel >      
                         <div class="verticalLine" style="display: inline"></div>&nbsp;
                         <apex:inputText required="true" id="city" value="{!city}" label="City"/>
                        </apex:outputPanel>

                        <apex:panelGroup >
                          <apex:outputLabel value="State  " for="state"/>
                          <apex:outputPanel >
                          <div class="verticalLine" style="display: inline"></div>&nbsp;
                          <apex:selectList value="{!state}" multiselect="false" size="1">
                            <apex:selectOptions value="{!stateOptions}"/>
                          </apex:selectList>
                          </apex:outputPanel>
                        </apex:panelGroup>
                     </apex:panelGrid>

                     <apex:panelGrid columns="2" columnClasses="col1,col2" width="100%">

                     <apex:outputLabel value="Postal Code" for="postcode"/>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText required="true" id="postcode" maxLength="5" value="{!postcode}" label="Postal Code"/>
                      </apex:outputPanel>
                      <apex:panelGroup >
                         <apex:outputLabel value="Country " for="country"/> <!--<c:ToolTip helpText="We can only provision accounts for US based users, to inquire about international accounts please contact: <EMAIL>"/>-->
                         </apex:panelGroup>
                      <apex:outputPanel >      
                       <div class="verticalLine" style="display: inline"></div>&nbsp;
                       <apex:inputText disabled="true" id="country" value="{!country}" label="Country"/>
                      </apex:outputPanel>



                      <apex:outputLabel value="Default Time Zone" for="timeZoneSelector"/> 

                      <apex:outputPanel layout="block" styleClass="requiredInput">
                        <div class="verticalLine" style="display: inline"></div>&nbsp;
                          <apex:selectList id="timeZoneSelector" required="true" value="{!defaultTimeZone}" multiselect="false" size="1">
                            <apex:selectOptions value="{!timeZoneOptions}"/>
                          </apex:selectList>
                      </apex:outputPanel>

                
                      <apex:outputText value=""/>

                      <apex:commandButton action="{!registerUser}" value="Create Account" id="submit"/>
                    </apex:panelGrid> 
                    </apex:panelGrid> 
                  <br/>
                  </apex:form>
      </center>
      <br/>
    </apex:define>
</apex:page>